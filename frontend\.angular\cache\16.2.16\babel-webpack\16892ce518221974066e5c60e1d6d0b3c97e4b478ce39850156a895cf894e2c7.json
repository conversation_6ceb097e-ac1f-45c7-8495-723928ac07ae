{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { throwError } from 'rxjs';\nimport { catchError, tap } from 'rxjs/operators';\nimport { environment } from '../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@auth0/angular-jwt\";\nimport * as i3 from \"./logger.service\";\nexport class TaskService {\n  constructor(http, jwtHelper, logger) {\n    this.http = http;\n    this.jwtHelper = jwtHelper;\n    this.logger = logger;\n    this.baseUrl = `${environment.urlBackend}tasks`;\n    this.logger.debug('TaskService', 'Task API URL:', this.baseUrl);\n  }\n  /**\n   * Obtient les en-têtes HTTP avec le token d'authentification\n   */\n  getUserHeaders() {\n    const token = localStorage.getItem('token');\n    if (!token || this.jwtHelper.isTokenExpired(token)) {\n      throw new Error('Token invalide ou expiré');\n    }\n    return new HttpHeaders({\n      Authorization: `Bearer ${token}`,\n      'Content-Type': 'application/json'\n    });\n  }\n  // Récupérer toutes les tâches\n  getTasks() {\n    return this.http.get(this.apiUrl).pipe(tap(data => console.log('Tasks received:', data)), catchError(this.handleError));\n  }\n  // Récupérer les tâches d'une équipe spécifique\n  getTasksByTeam(teamId) {\n    return this.http.get(`${this.apiUrl}/team/${teamId}`).pipe(tap(data => console.log(`Tasks for team ${teamId} received:`, data)), catchError(this.handleError));\n  }\n  // Récupérer une tâche par son ID\n  getTask(id) {\n    return this.http.get(`${this.apiUrl}/${id}`).pipe(tap(data => console.log('Task received:', data)), catchError(this.handleError));\n  }\n  // Créer une nouvelle tâche\n  createTask(task) {\n    return this.http.post(this.apiUrl, task).pipe(tap(data => console.log('Task created:', data)), catchError(this.handleError));\n  }\n  // Mettre à jour une tâche existante\n  updateTask(id, task) {\n    return this.http.put(`${this.apiUrl}/${id}`, task).pipe(tap(data => console.log('Task updated:', data)), catchError(this.handleError));\n  }\n  // Supprimer une tâche\n  deleteTask(id) {\n    return this.http.delete(`${this.apiUrl}/${id}`).pipe(tap(data => console.log('Task deleted:', data)), catchError(this.handleError));\n  }\n  // Mettre à jour le statut d'une tâche\n  updateTaskStatus(id, status) {\n    return this.http.patch(`${this.apiUrl}/${id}/status`, {\n      status\n    }).pipe(tap(data => console.log('Task status updated:', data)), catchError(this.handleError));\n  }\n  // Gérer les erreurs HTTP\n  handleError(error) {\n    let errorMessage = '';\n    if (error.error instanceof ErrorEvent) {\n      // Erreur côté client\n      errorMessage = `Error: ${error.error.message}`;\n    } else {\n      // Erreur côté serveur\n      errorMessage = `Error Code: ${error.status}\\nMessage: ${error.message}`;\n    }\n    console.error(errorMessage);\n    return throwError(() => new Error(errorMessage));\n  }\n  static {\n    this.ɵfac = function TaskService_Factory(t) {\n      return new (t || TaskService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.JwtHelperService), i0.ɵɵinject(i3.LoggerService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: TaskService,\n      factory: TaskService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpHeaders", "throwError", "catchError", "tap", "environment", "TaskService", "constructor", "http", "jwtHelper", "logger", "baseUrl", "urlBackend", "debug", "getUserHeaders", "token", "localStorage", "getItem", "isTokenExpired", "Error", "Authorization", "getTasks", "get", "apiUrl", "pipe", "data", "console", "log", "handleError", "getTasksByTeam", "teamId", "getTask", "id", "createTask", "task", "post", "updateTask", "put", "deleteTask", "delete", "updateTaskStatus", "status", "patch", "error", "errorMessage", "ErrorEvent", "message", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "JwtHelperService", "i3", "LoggerService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\services\\task.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport {\r\n  HttpClient,\r\n  HttpErrorResponse,\r\n  HttpHeaders,\r\n} from '@angular/common/http';\r\nimport { Observable, throwError } from 'rxjs';\r\nimport { catchError, tap, map } from 'rxjs/operators';\r\nimport { JwtHelperService } from '@auth0/angular-jwt';\r\nimport { environment } from '../../environments/environment';\r\nimport { Task, TaskResponse } from '../models/message.model';\r\nimport { LoggerService } from './logger.service';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class TaskService {\r\n  private readonly baseUrl = `${environment.urlBackend}tasks`;\r\n\r\n  constructor(\r\n    private http: HttpClient,\r\n    private jwtHelper: JwtHelperService,\r\n    private logger: LoggerService\r\n  ) {\r\n    this.logger.debug('TaskService', 'Task API URL:', this.baseUrl);\r\n  }\r\n\r\n  /**\r\n   * Obtient les en-têtes HTTP avec le token d'authentification\r\n   */\r\n  private getUserHeaders(): HttpHeaders {\r\n    const token = localStorage.getItem('token');\r\n    if (!token || this.jwtHelper.isTokenExpired(token)) {\r\n      throw new Error('Token invalide ou expiré');\r\n    }\r\n    return new HttpHeaders({\r\n      Authorization: `Bearer ${token}`,\r\n      'Content-Type': 'application/json',\r\n    });\r\n  }\r\n\r\n  // Récupérer toutes les tâches\r\n  getTasks(): Observable<Task[]> {\r\n    return this.http.get<Task[]>(this.apiUrl).pipe(\r\n      tap((data) => console.log('Tasks received:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Récupérer les tâches d'une équipe spécifique\r\n  getTasksByTeam(teamId: string): Observable<Task[]> {\r\n    return this.http.get<Task[]>(`${this.apiUrl}/team/${teamId}`).pipe(\r\n      tap((data) => console.log(`Tasks for team ${teamId} received:`, data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Récupérer une tâche par son ID\r\n  getTask(id: string): Observable<Task> {\r\n    return this.http.get<Task>(`${this.apiUrl}/${id}`).pipe(\r\n      tap((data) => console.log('Task received:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Créer une nouvelle tâche\r\n  createTask(task: Task): Observable<Task> {\r\n    return this.http.post<Task>(this.apiUrl, task).pipe(\r\n      tap((data) => console.log('Task created:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Mettre à jour une tâche existante\r\n  updateTask(id: string, task: Task): Observable<Task> {\r\n    return this.http.put<Task>(`${this.apiUrl}/${id}`, task).pipe(\r\n      tap((data) => console.log('Task updated:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Supprimer une tâche\r\n  deleteTask(id: string): Observable<any> {\r\n    return this.http.delete(`${this.apiUrl}/${id}`).pipe(\r\n      tap((data) => console.log('Task deleted:', data)),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Mettre à jour le statut d'une tâche\r\n  updateTaskStatus(\r\n    id: string,\r\n    status: 'todo' | 'in-progress' | 'done'\r\n  ): Observable<Task> {\r\n    return this.http\r\n      .patch<Task>(`${this.apiUrl}/${id}/status`, { status })\r\n      .pipe(\r\n        tap((data) => console.log('Task status updated:', data)),\r\n        catchError(this.handleError)\r\n      );\r\n  }\r\n\r\n  // Gérer les erreurs HTTP\r\n  private handleError(error: HttpErrorResponse) {\r\n    let errorMessage = '';\r\n    if (error.error instanceof ErrorEvent) {\r\n      // Erreur côté client\r\n      errorMessage = `Error: ${error.error.message}`;\r\n    } else {\r\n      // Erreur côté serveur\r\n      errorMessage = `Error Code: ${error.status}\\nMessage: ${error.message}`;\r\n    }\r\n    console.error(errorMessage);\r\n    return throwError(() => new Error(errorMessage));\r\n  }\r\n}\r\n"], "mappings": "AACA,SAGEA,WAAW,QACN,sBAAsB;AAC7B,SAAqBC,UAAU,QAAQ,MAAM;AAC7C,SAASC,UAAU,EAAEC,GAAG,QAAa,gBAAgB;AAErD,SAASC,WAAW,QAAQ,gCAAgC;;;;;AAO5D,OAAM,MAAOC,WAAW;EAGtBC,YACUC,IAAgB,EAChBC,SAA2B,EAC3BC,MAAqB;IAFrB,KAAAF,IAAI,GAAJA,IAAI;IACJ,KAAAC,SAAS,GAATA,SAAS;IACT,KAAAC,MAAM,GAANA,MAAM;IALC,KAAAC,OAAO,GAAG,GAAGN,WAAW,CAACO,UAAU,OAAO;IAOzD,IAAI,CAACF,MAAM,CAACG,KAAK,CAAC,aAAa,EAAE,eAAe,EAAE,IAAI,CAACF,OAAO,CAAC;EACjE;EAEA;;;EAGQG,cAAcA,CAAA;IACpB,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAACF,KAAK,IAAI,IAAI,CAACN,SAAS,CAACS,cAAc,CAACH,KAAK,CAAC,EAAE;MAClD,MAAM,IAAII,KAAK,CAAC,0BAA0B,CAAC;;IAE7C,OAAO,IAAIlB,WAAW,CAAC;MACrBmB,aAAa,EAAE,UAAUL,KAAK,EAAE;MAChC,cAAc,EAAE;KACjB,CAAC;EACJ;EAEA;EACAM,QAAQA,CAAA;IACN,OAAO,IAAI,CAACb,IAAI,CAACc,GAAG,CAAS,IAAI,CAACC,MAAM,CAAC,CAACC,IAAI,CAC5CpB,GAAG,CAAEqB,IAAI,IAAKC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEF,IAAI,CAAC,CAAC,EACnDtB,UAAU,CAAC,IAAI,CAACyB,WAAW,CAAC,CAC7B;EACH;EAEA;EACAC,cAAcA,CAACC,MAAc;IAC3B,OAAO,IAAI,CAACtB,IAAI,CAACc,GAAG,CAAS,GAAG,IAAI,CAACC,MAAM,SAASO,MAAM,EAAE,CAAC,CAACN,IAAI,CAChEpB,GAAG,CAAEqB,IAAI,IAAKC,OAAO,CAACC,GAAG,CAAC,kBAAkBG,MAAM,YAAY,EAAEL,IAAI,CAAC,CAAC,EACtEtB,UAAU,CAAC,IAAI,CAACyB,WAAW,CAAC,CAC7B;EACH;EAEA;EACAG,OAAOA,CAACC,EAAU;IAChB,OAAO,IAAI,CAACxB,IAAI,CAACc,GAAG,CAAO,GAAG,IAAI,CAACC,MAAM,IAAIS,EAAE,EAAE,CAAC,CAACR,IAAI,CACrDpB,GAAG,CAAEqB,IAAI,IAAKC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEF,IAAI,CAAC,CAAC,EAClDtB,UAAU,CAAC,IAAI,CAACyB,WAAW,CAAC,CAC7B;EACH;EAEA;EACAK,UAAUA,CAACC,IAAU;IACnB,OAAO,IAAI,CAAC1B,IAAI,CAAC2B,IAAI,CAAO,IAAI,CAACZ,MAAM,EAAEW,IAAI,CAAC,CAACV,IAAI,CACjDpB,GAAG,CAAEqB,IAAI,IAAKC,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEF,IAAI,CAAC,CAAC,EACjDtB,UAAU,CAAC,IAAI,CAACyB,WAAW,CAAC,CAC7B;EACH;EAEA;EACAQ,UAAUA,CAACJ,EAAU,EAAEE,IAAU;IAC/B,OAAO,IAAI,CAAC1B,IAAI,CAAC6B,GAAG,CAAO,GAAG,IAAI,CAACd,MAAM,IAAIS,EAAE,EAAE,EAAEE,IAAI,CAAC,CAACV,IAAI,CAC3DpB,GAAG,CAAEqB,IAAI,IAAKC,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEF,IAAI,CAAC,CAAC,EACjDtB,UAAU,CAAC,IAAI,CAACyB,WAAW,CAAC,CAC7B;EACH;EAEA;EACAU,UAAUA,CAACN,EAAU;IACnB,OAAO,IAAI,CAACxB,IAAI,CAAC+B,MAAM,CAAC,GAAG,IAAI,CAAChB,MAAM,IAAIS,EAAE,EAAE,CAAC,CAACR,IAAI,CAClDpB,GAAG,CAAEqB,IAAI,IAAKC,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEF,IAAI,CAAC,CAAC,EACjDtB,UAAU,CAAC,IAAI,CAACyB,WAAW,CAAC,CAC7B;EACH;EAEA;EACAY,gBAAgBA,CACdR,EAAU,EACVS,MAAuC;IAEvC,OAAO,IAAI,CAACjC,IAAI,CACbkC,KAAK,CAAO,GAAG,IAAI,CAACnB,MAAM,IAAIS,EAAE,SAAS,EAAE;MAAES;IAAM,CAAE,CAAC,CACtDjB,IAAI,CACHpB,GAAG,CAAEqB,IAAI,IAAKC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEF,IAAI,CAAC,CAAC,EACxDtB,UAAU,CAAC,IAAI,CAACyB,WAAW,CAAC,CAC7B;EACL;EAEA;EACQA,WAAWA,CAACe,KAAwB;IAC1C,IAAIC,YAAY,GAAG,EAAE;IACrB,IAAID,KAAK,CAACA,KAAK,YAAYE,UAAU,EAAE;MACrC;MACAD,YAAY,GAAG,UAAUD,KAAK,CAACA,KAAK,CAACG,OAAO,EAAE;KAC/C,MAAM;MACL;MACAF,YAAY,GAAG,eAAeD,KAAK,CAACF,MAAM,cAAcE,KAAK,CAACG,OAAO,EAAE;;IAEzEpB,OAAO,CAACiB,KAAK,CAACC,YAAY,CAAC;IAC3B,OAAO1C,UAAU,CAAC,MAAM,IAAIiB,KAAK,CAACyB,YAAY,CAAC,CAAC;EAClD;;;uBAlGWtC,WAAW,EAAAyC,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,gBAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAK,EAAA,CAAAC,aAAA;IAAA;EAAA;;;aAAXhD,WAAW;MAAAiD,OAAA,EAAXjD,WAAW,CAAAkD,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}