{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { Subscription } from 'rxjs';\nimport { MessageType, CallType } from 'src/app/models/message.model';\nimport { switchMap, distinctUntilChanged, filter } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@app/services/message.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/services/authuser.service\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"src/app/services/user-status.service\";\nimport * as i6 from \"src/app/services/toast.service\";\nimport * as i7 from \"src/app/services/logger.service\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"../../../../components/voice-recorder/voice-recorder.component\";\nimport * as i10 from \"../../../../components/voice-message-player/voice-message-player.component\";\nconst _c0 = [\"messagesContainer\"];\nconst _c1 = [\"fileInput\"];\nfunction MessageChatComponent_span_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 50);\n  }\n}\nfunction MessageChatComponent_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51)(1, \"span\", 52);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 53);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.otherParticipant.username, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.otherParticipant.isOnline ? \"En ligne\" : ctx_r1.formatLastActive(ctx_r1.otherParticipant.lastActive), \" \");\n  }\n}\nfunction MessageChatComponent_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 54)(1, \"div\", 55);\n    i0.ɵɵtext(2, \" Choisir un th\\u00E8me \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 56)(4, \"a\", 57);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_45_Template_a_click_4_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.changeTheme(\"theme-default\"));\n    });\n    i0.ɵɵelementStart(5, \"div\", 58);\n    i0.ɵɵelement(6, \"div\", 59);\n    i0.ɵɵelementStart(7, \"div\");\n    i0.ɵɵtext(8, \"Par d\\u00E9faut\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"a\", 60);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_45_Template_a_click_9_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r22 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r22.changeTheme(\"theme-feminine\"));\n    });\n    i0.ɵɵelementStart(10, \"div\", 58);\n    i0.ɵɵelement(11, \"div\", 61);\n    i0.ɵɵelementStart(12, \"div\");\n    i0.ɵɵtext(13, \"Rose\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"a\", 62);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_45_Template_a_click_14_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r23 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r23.changeTheme(\"theme-masculine\"));\n    });\n    i0.ɵɵelementStart(15, \"div\", 58);\n    i0.ɵɵelement(16, \"div\", 63);\n    i0.ɵɵelementStart(17, \"div\");\n    i0.ɵɵtext(18, \"Bleu\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(19, \"a\", 64);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_45_Template_a_click_19_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r24 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r24.changeTheme(\"theme-neutral\"));\n    });\n    i0.ɵɵelementStart(20, \"div\", 58);\n    i0.ɵɵelement(21, \"div\", 65);\n    i0.ɵɵelementStart(22, \"div\");\n    i0.ɵɵtext(23, \"Vert\");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nfunction MessageChatComponent_div_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 66)(1, \"div\", 67)(2, \"div\", 20);\n    i0.ɵɵelement(3, \"div\", 68)(4, \"div\", 69);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 70);\n    i0.ɵɵtext(6, \" Initializing communication... \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction MessageChatComponent_div_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71)(1, \"div\", 72)(2, \"div\", 73);\n    i0.ɵɵelement(3, \"div\", 74)(4, \"div\", 75)(5, \"div\", 76);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 77);\n    i0.ɵɵtext(7, \" Retrieving data... \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction MessageChatComponent_div_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 78)(1, \"div\", 79);\n    i0.ɵɵelement(2, \"div\", 80);\n    i0.ɵɵelementStart(3, \"div\", 81);\n    i0.ɵɵtext(4, \" Communication Initialized \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"div\", 80);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MessageChatComponent_div_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 82)(1, \"div\", 83)(2, \"div\", 84);\n    i0.ɵɵelement(3, \"i\", 85)(4, \"div\", 86);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\")(6, \"h3\", 87);\n    i0.ɵɵtext(7, \" System Error: Communication Failure \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 88);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r7.error);\n  }\n}\nfunction MessageChatComponent_ng_container_54_div_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 98);\n    i0.ɵɵelement(1, \"div\", 99);\n    i0.ɵɵelementStart(2, \"div\", 100);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"div\", 99);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r26 = i0.ɵɵnextContext().$implicit;\n    const ctx_r28 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r28.formatMessageDate(message_r26 == null ? null : message_r26.timestamp), \" \");\n  }\n}\nfunction MessageChatComponent_ng_container_54_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 101);\n    i0.ɵɵelement(1, \"img\", 102);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r26 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", (message_r26 == null ? null : message_r26.sender == null ? null : message_r26.sender.image) || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl);\n  }\n}\nfunction MessageChatComponent_ng_container_54_div_1_div_5_span_6_i_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 111);\n  }\n}\nfunction MessageChatComponent_ng_container_54_div_1_div_5_span_6_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 112);\n  }\n}\nfunction MessageChatComponent_ng_container_54_div_1_div_5_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 108);\n    i0.ɵɵtemplate(1, MessageChatComponent_ng_container_54_div_1_div_5_span_6_i_1_Template, 1, 0, \"i\", 109);\n    i0.ɵɵtemplate(2, MessageChatComponent_ng_container_54_div_1_div_5_span_6_i_2_Template, 1, 0, \"i\", 110);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r26 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r26 == null ? null : message_r26.isRead);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !(message_r26 == null ? null : message_r26.isRead));\n  }\n}\nconst _c2 = function (a0, a1, a2) {\n  return {\n    \"futuristic-message-pending\": a0,\n    \"futuristic-message-sending\": a1,\n    \"futuristic-message-error\": a2\n  };\n};\nfunction MessageChatComponent_ng_container_54_div_1_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 103)(1, \"div\", 104);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 105)(4, \"span\", 106);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, MessageChatComponent_ng_container_54_div_1_div_5_span_6_Template, 3, 2, \"span\", 107);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const message_r26 = i0.ɵɵnextContext().$implicit;\n    const ctx_r30 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(4, _c2, message_r26.isPending, message_r26.isPending && !message_r26.isError, message_r26.isError));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", message_r26.content, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r30.formatMessageTime(message_r26 == null ? null : message_r26.timestamp), \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (message_r26 == null ? null : message_r26.sender == null ? null : message_r26.sender.id) === ctx_r30.currentUserId || (message_r26 == null ? null : message_r26.sender == null ? null : message_r26.sender._id) === ctx_r30.currentUserId || (message_r26 == null ? null : message_r26.senderId) === ctx_r30.currentUserId);\n  }\n}\nfunction MessageChatComponent_ng_container_54_div_1_div_6_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 120);\n  }\n  if (rf & 2) {\n    const i_r42 = ctx.$implicit;\n    i0.ɵɵstyleProp(\"height\", 5 + (i_r42 % 3 === 0 ? 20 : i_r42 % 3 === 1 ? 15 : 10), \"px\");\n  }\n}\nfunction MessageChatComponent_ng_container_54_div_1_div_6_span_10_i_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 111);\n  }\n}\nfunction MessageChatComponent_ng_container_54_div_1_div_6_span_10_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 112);\n  }\n}\nfunction MessageChatComponent_ng_container_54_div_1_div_6_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 108);\n    i0.ɵɵtemplate(1, MessageChatComponent_ng_container_54_div_1_div_6_span_10_i_1_Template, 1, 0, \"i\", 109);\n    i0.ɵɵtemplate(2, MessageChatComponent_ng_container_54_div_1_div_6_span_10_i_2_Template, 1, 0, \"i\", 110);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r26 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r26 == null ? null : message_r26.isRead);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !(message_r26 == null ? null : message_r26.isRead));\n  }\n}\nconst _c3 = function () {\n  return [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14];\n};\nfunction MessageChatComponent_ng_container_54_div_1_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 113)(1, \"div\", 114)(2, \"div\", 115);\n    i0.ɵɵelement(3, \"i\", 116);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 117);\n    i0.ɵɵtemplate(5, MessageChatComponent_ng_container_54_div_1_div_6_div_5_Template, 1, 2, \"div\", 118);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(6, \"app-voice-message-player\", 119);\n    i0.ɵɵelementStart(7, \"div\", 105)(8, \"span\", 106);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, MessageChatComponent_ng_container_54_div_1_div_6_span_10_Template, 3, 2, \"span\", 107);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const message_r26 = i0.ɵɵnextContext().$implicit;\n    const ctx_r31 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(6, _c2, message_r26.isPending, message_r26.isPending && !message_r26.isError, message_r26.isError));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(10, _c3));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"audioUrl\", ctx_r31.getVoiceMessageUrl(message_r26))(\"duration\", ctx_r31.getVoiceMessageDuration(message_r26));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r31.formatMessageTime(message_r26 == null ? null : message_r26.timestamp), \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (message_r26 == null ? null : message_r26.sender == null ? null : message_r26.sender.id) === ctx_r31.currentUserId || (message_r26 == null ? null : message_r26.sender == null ? null : message_r26.sender._id) === ctx_r31.currentUserId || (message_r26 == null ? null : message_r26.senderId) === ctx_r31.currentUserId);\n  }\n}\nfunction MessageChatComponent_ng_container_54_div_1_div_7_span_9_i_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 111);\n  }\n}\nfunction MessageChatComponent_ng_container_54_div_1_div_7_span_9_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 112);\n  }\n}\nfunction MessageChatComponent_ng_container_54_div_1_div_7_span_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 108);\n    i0.ɵɵtemplate(1, MessageChatComponent_ng_container_54_div_1_div_7_span_9_i_1_Template, 1, 0, \"i\", 109);\n    i0.ɵɵtemplate(2, MessageChatComponent_ng_container_54_div_1_div_7_span_9_i_2_Template, 1, 0, \"i\", 110);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r26 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", message_r26 == null ? null : message_r26.isRead);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !(message_r26 == null ? null : message_r26.isRead));\n  }\n}\nfunction MessageChatComponent_ng_container_54_div_1_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 121)(1, \"div\", 122)(2, \"a\", 123);\n    i0.ɵɵelement(3, \"img\", 124);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 125);\n    i0.ɵɵelement(5, \"i\", 126);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 105)(7, \"span\", 106);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, MessageChatComponent_ng_container_54_div_1_div_7_span_9_Template, 3, 2, \"span\", 107);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const message_r26 = i0.ɵɵnextContext().$implicit;\n    const ctx_r32 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(5, _c2, message_r26.isPending, message_r26.isPending && !message_r26.isError, message_r26.isError));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"href\", ctx_r32.getImageUrl(message_r26), i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r32.getImageUrl(message_r26), i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r32.formatMessageTime(message_r26 == null ? null : message_r26.timestamp), \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (message_r26 == null ? null : message_r26.sender == null ? null : message_r26.sender.id) === ctx_r32.currentUserId || (message_r26 == null ? null : message_r26.sender == null ? null : message_r26.sender._id) === ctx_r32.currentUserId || (message_r26 == null ? null : message_r26.senderId) === ctx_r32.currentUserId);\n  }\n}\nconst _c4 = function (a0, a1) {\n  return {\n    \"futuristic-message-current-user\": a0,\n    \"futuristic-message-other-user\": a1\n  };\n};\nfunction MessageChatComponent_ng_container_54_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 90);\n    i0.ɵɵtemplate(1, MessageChatComponent_ng_container_54_div_1_div_1_Template, 5, 1, \"div\", 91);\n    i0.ɵɵelementStart(2, \"div\", 92);\n    i0.ɵɵtemplate(3, MessageChatComponent_ng_container_54_div_1_div_3_Template, 2, 1, \"div\", 93);\n    i0.ɵɵelementStart(4, \"div\", 94);\n    i0.ɵɵtemplate(5, MessageChatComponent_ng_container_54_div_1_div_5_Template, 7, 8, \"div\", 95);\n    i0.ɵɵtemplate(6, MessageChatComponent_ng_container_54_div_1_div_6_Template, 11, 11, \"div\", 96);\n    i0.ɵɵtemplate(7, MessageChatComponent_ng_container_54_div_1_div_7_Template, 10, 9, \"div\", 97);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const message_r26 = ctx.$implicit;\n    const i_r27 = ctx.index;\n    const ctx_r25 = i0.ɵɵnextContext(2);\n    i0.ɵɵattribute(\"data-message-id\", message_r26.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r25.shouldShowDateHeader(i_r27));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(7, _c4, (message_r26 == null ? null : message_r26.sender == null ? null : message_r26.sender.id) === ctx_r25.currentUserId || (message_r26 == null ? null : message_r26.sender == null ? null : message_r26.sender._id) === ctx_r25.currentUserId || (message_r26 == null ? null : message_r26.senderId) === ctx_r25.currentUserId, !((message_r26 == null ? null : message_r26.sender == null ? null : message_r26.sender.id) === ctx_r25.currentUserId || (message_r26 == null ? null : message_r26.sender == null ? null : message_r26.sender._id) === ctx_r25.currentUserId || (message_r26 == null ? null : message_r26.senderId) === ctx_r25.currentUserId)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !((message_r26 == null ? null : message_r26.sender == null ? null : message_r26.sender.id) === ctx_r25.currentUserId || (message_r26 == null ? null : message_r26.sender == null ? null : message_r26.sender._id) === ctx_r25.currentUserId || (message_r26 == null ? null : message_r26.senderId) === ctx_r25.currentUserId));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", (message_r26 == null ? null : message_r26.content) && !ctx_r25.hasImage(message_r26) && !ctx_r25.isVoiceMessage(message_r26));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r25.isVoiceMessage(message_r26));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r25.hasImage(message_r26));\n  }\n}\nfunction MessageChatComponent_ng_container_54_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MessageChatComponent_ng_container_54_div_1_Template, 8, 10, \"div\", 89);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r8.messages);\n  }\n}\nfunction MessageChatComponent_ng_template_55_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r54 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 128)(1, \"div\", 129);\n    i0.ɵɵelement(2, \"i\", 130);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 131);\n    i0.ɵɵtext(4, \" Aucun message dans cette conversation. \");\n    i0.ɵɵelement(5, \"br\");\n    i0.ɵɵtext(6, \"\\u00C9tablissez le premier contact pour commencer. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 132);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_ng_template_55_div_0_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r54);\n      const ctx_r53 = i0.ɵɵnextContext(2);\n      let tmp_b_0;\n      return i0.ɵɵresetView((tmp_b_0 = ctx_r53.messageForm.get(\"content\")) == null ? null : tmp_b_0.setValue(\"Bonjour!\"));\n    });\n    i0.ɵɵelement(8, \"i\", 133);\n    i0.ɵɵtext(9, \" Initialiser la communication \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction MessageChatComponent_ng_template_55_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MessageChatComponent_ng_template_55_div_0_Template, 10, 0, \"div\", 127);\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r10.loading && !ctx_r10.error);\n  }\n}\nfunction MessageChatComponent_div_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 134)(1, \"div\", 101);\n    i0.ɵɵelement(2, \"img\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 135)(4, \"div\", 136);\n    i0.ɵɵelement(5, \"div\", 137)(6, \"div\", 138)(7, \"div\", 139);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", (ctx_r11.otherParticipant == null ? null : ctx_r11.otherParticipant.image) || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl);\n  }\n}\nfunction MessageChatComponent_div_59_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r56 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 140);\n    i0.ɵɵelement(1, \"img\", 141);\n    i0.ɵɵelementStart(2, \"button\", 142);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_59_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r56);\n      const ctx_r55 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r55.removeAttachment());\n    });\n    i0.ɵɵelement(3, \"i\", 143);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r12.previewUrl, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction MessageChatComponent_div_60_button_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r60 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 155);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_60_button_15_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r60);\n      const emoji_r58 = restoredCtx.$implicit;\n      const ctx_r59 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r59.insertEmoji(emoji_r58));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const emoji_r58 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", emoji_r58, \" \");\n  }\n}\nfunction MessageChatComponent_div_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 144)(1, \"div\", 145)(2, \"button\", 146);\n    i0.ɵɵelement(3, \"i\", 40);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 147);\n    i0.ɵɵelement(5, \"i\", 148);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 147);\n    i0.ɵɵelement(7, \"i\", 149);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 147);\n    i0.ɵɵelement(9, \"i\", 150);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 147);\n    i0.ɵɵelement(11, \"i\", 151);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"button\", 147);\n    i0.ɵɵelement(13, \"i\", 152);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 153);\n    i0.ɵɵtemplate(15, MessageChatComponent_div_60_button_15_Template, 2, 1, \"button\", 154);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(15);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r13.commonEmojis);\n  }\n}\nfunction MessageChatComponent_app_voice_recorder_69_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r62 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-voice-recorder\", 156);\n    i0.ɵɵlistener(\"recordingComplete\", function MessageChatComponent_app_voice_recorder_69_Template_app_voice_recorder_recordingComplete_0_listener($event) {\n      i0.ɵɵrestoreView(_r62);\n      const ctx_r61 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r61.onVoiceRecordingComplete($event));\n    })(\"recordingCancelled\", function MessageChatComponent_app_voice_recorder_69_Template_app_voice_recorder_recordingCancelled_0_listener() {\n      i0.ɵɵrestoreView(_r62);\n      const ctx_r63 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r63.onVoiceRecordingCancelled());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"maxDuration\", 60);\n  }\n}\nfunction MessageChatComponent_input_70_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r65 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 157);\n    i0.ɵɵlistener(\"input\", function MessageChatComponent_input_70_Template_input_input_0_listener() {\n      i0.ɵɵrestoreView(_r65);\n      const ctx_r64 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r64.onTyping());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MessageChatComponent_button_71_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r67 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 158);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_button_71_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r67);\n      const ctx_r66 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r66.toggleVoiceRecording());\n    });\n    i0.ɵɵelement(1, \"i\", 159);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MessageChatComponent_button_72_i_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 133);\n  }\n}\nfunction MessageChatComponent_button_72_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 163);\n  }\n}\nfunction MessageChatComponent_button_72_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 160);\n    i0.ɵɵtemplate(1, MessageChatComponent_button_72_i_1_Template, 1, 0, \"i\", 161);\n    i0.ɵɵtemplate(2, MessageChatComponent_button_72_i_2_Template, 1, 0, \"i\", 162);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r18.isUploading || ctx_r18.messageForm.invalid && !ctx_r18.selectedFile);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r18.isUploading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r18.isUploading);\n  }\n}\nfunction MessageChatComponent_div_73_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r71 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 164)(1, \"div\", 165)(2, \"div\", 166)(3, \"div\", 167);\n    i0.ɵɵelement(4, \"img\", 168);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h3\", 169);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 170);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 171)(10, \"button\", 172);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_73_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r71);\n      const ctx_r70 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r70.rejectCall());\n    });\n    i0.ɵɵelement(11, \"i\", 173);\n    i0.ɵɵelementStart(12, \"span\");\n    i0.ɵɵtext(13, \"Rejeter\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"button\", 174);\n    i0.ɵɵlistener(\"click\", function MessageChatComponent_div_73_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r71);\n      const ctx_r72 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r72.acceptCall());\n    });\n    i0.ɵɵelement(15, \"i\", 175);\n    i0.ɵɵelementStart(16, \"span\");\n    i0.ɵɵtext(17, \"Accepter\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r19 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"src\", (ctx_r19.incomingCall.caller == null ? null : ctx_r19.incomingCall.caller.image) || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r19.incomingCall.caller == null ? null : ctx_r19.incomingCall.caller.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r19.incomingCall.type === \"AUDIO\" ? \"Appel audio entrant\" : \"Appel vid\\u00E9o entrant\", \" \");\n  }\n}\nconst _c5 = function (a0) {\n  return {\n    active: a0\n  };\n};\nexport class MessageChatComponent {\n  constructor(MessageService, route, authService, fb, statusService, router, toastService, logger, cdr) {\n    this.MessageService = MessageService;\n    this.route = route;\n    this.authService = authService;\n    this.fb = fb;\n    this.statusService = statusService;\n    this.router = router;\n    this.toastService = toastService;\n    this.logger = logger;\n    this.cdr = cdr;\n    this.messages = [];\n    this.conversation = null;\n    this.loading = true;\n    this.currentUserId = null;\n    this.currentUsername = 'You';\n    this.otherParticipant = null;\n    this.selectedFile = null;\n    this.previewUrl = null;\n    this.isUploading = false;\n    this.isTyping = false;\n    this.isRecordingVoice = false;\n    this.voiceRecordingDuration = 0;\n    this.MAX_MESSAGES_PER_SIDE = 5; // Nombre maximum de messages à afficher par côté (expéditeur/destinataire)\n    this.MAX_MESSAGES_TO_LOAD = 10; // Nombre maximum de messages à charger à la fois (pagination)\n    this.MAX_TOTAL_MESSAGES = 100; // Limite totale de messages à conserver en mémoire\n    this.currentPage = 1; // Page actuelle pour la pagination\n    this.isLoadingMore = false; // Indicateur de chargement en cours (public pour le template)\n    this.hasMoreMessages = true; // Indique s'il y a plus de messages à charger (public pour le template)\n    this.subscriptions = new Subscription();\n    // Variables pour l'édition et la suppression de messages\n    this.editingMessageId = null;\n    this.editingContent = '';\n    this.showMessageOptions = {};\n    this.showDeleteConfirm = {};\n    // Variables pour le sélecteur de thème\n    this.selectedTheme = 'theme-default'; // Thème par défaut\n    this.showThemeSelector = false; // Affichage du sélecteur de thème\n    // Variables pour le sélecteur d'émojis\n    this.showEmojiPicker = false;\n    // Variables pour les appels\n    this.incomingCall = null;\n    this.showCallModal = false;\n    this.commonEmojis = ['😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇', '🙂', '🙃', '😉', '😌', '😍', '🥰', '😘', '😗', '😙', '😚', '😋', '😛', '😝', '😜', '🤪', '🤨', '🧐', '🤓', '😎', '🤩', '😏', '😒', '😞', '😔', '😟', '😕', '🙁', '☹️', '😣', '😖', '😫', '😩', '🥺', '😢', '😭', '😤', '😠', '😡', '🤬', '🤯', '😳', '🥵', '🥶', '😱', '😨', '😰', '😥', '😓', '🤗', '🤔', '👍', '👎', '👏', '🙌', '👐', '🤲', '🤝', '🙏', '✌️', '🤞', '❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '💔', '💯', '💢'];\n    this.isCurrentlyTyping = false;\n    this.TYPING_DELAY = 500; // Délai en ms avant d'envoyer l'événement de frappe\n    this.TYPING_TIMEOUT = 3000; // Délai en ms avant d'arrêter l'indicateur de frappe\n    this.messageForm = this.fb.group({\n      content: ['', [Validators.maxLength(1000)]]\n    });\n  }\n  ngOnInit() {\n    this.currentUserId = this.authService.getCurrentUserId();\n    // Charger le thème sauvegardé\n    const savedTheme = localStorage.getItem('chat-theme');\n    if (savedTheme) {\n      this.selectedTheme = savedTheme;\n      this.logger.debug('MessageChat', `Loaded saved theme: ${savedTheme}`);\n    }\n    // Récupérer les messages vocaux pour assurer leur persistance\n    this.loadVoiceMessages();\n    // S'abonner aux notifications en temps réel\n    this.subscribeToNotifications();\n    const routeSub = this.route.params.pipe(filter(params => params['id']), distinctUntilChanged(), switchMap(params => {\n      this.loading = true;\n      this.messages = [];\n      this.currentPage = 1; // Réinitialiser à la page 1\n      this.hasMoreMessages = true; // Réinitialiser l'indicateur de messages supplémentaires\n      this.logger.debug('MessageChat', `Loading conversation with pagination: page=${this.currentPage}, limit=${this.MAX_MESSAGES_TO_LOAD}`);\n      // Charger la conversation avec pagination (page 1, limit 10)\n      return this.MessageService.getConversation(params['id'], this.MAX_MESSAGES_TO_LOAD, this.currentPage // Utiliser la page au lieu de l'offset\n      );\n    })).subscribe({\n      next: conversation => {\n        this.handleConversationLoaded(conversation);\n      },\n      error: error => {\n        this.handleError('Failed to load conversation', error);\n      }\n    });\n    this.subscriptions.add(routeSub);\n  }\n  /**\n   * Charge les messages vocaux pour assurer leur persistance\n   */\n  loadVoiceMessages() {\n    this.logger.debug('MessageChat', 'Loading voice messages for persistence');\n    const sub = this.MessageService.getVoiceMessages().subscribe({\n      next: voiceMessages => {\n        this.logger.info('MessageChat', `Retrieved ${voiceMessages.length} voice messages`);\n        // Les messages vocaux sont maintenant chargés et disponibles dans le service\n        // Ils seront automatiquement associés aux conversations correspondantes\n        if (voiceMessages.length > 0) {\n          this.logger.debug('MessageChat', 'Voice messages loaded successfully');\n          // Forcer le rafraîchissement de la vue après le chargement des messages vocaux\n          setTimeout(() => {\n            this.cdr.detectChanges();\n            this.logger.debug('MessageChat', 'View refreshed after loading voice messages');\n          }, 100);\n        }\n      },\n      error: error => {\n        this.logger.error('MessageChat', 'Error loading voice messages:', error);\n        // Ne pas bloquer l'expérience utilisateur si le chargement des messages vocaux échoue\n      }\n    });\n\n    this.subscriptions.add(sub);\n  }\n  /**\n   * Gère les erreurs et les affiche à l'utilisateur\n   * @param message Message d'erreur à afficher\n   * @param error Objet d'erreur\n   */\n  handleError(message, error) {\n    this.logger.error('MessageChat', message, error);\n    this.loading = false;\n    this.error = error;\n    this.toastService.showError(message);\n  }\n  // logique FileService\n  getFileIcon(mimeType) {\n    if (!mimeType) return 'fa-file';\n    if (mimeType.startsWith('image/')) return 'fa-image';\n    if (mimeType.includes('pdf')) return 'fa-file-pdf';\n    if (mimeType.includes('word') || mimeType.includes('msword')) return 'fa-file-word';\n    if (mimeType.includes('excel')) return 'fa-file-excel';\n    if (mimeType.includes('powerpoint')) return 'fa-file-powerpoint';\n    if (mimeType.includes('audio')) return 'fa-file-audio';\n    if (mimeType.includes('video')) return 'fa-file-video';\n    if (mimeType.includes('zip') || mimeType.includes('compressed')) return 'fa-file-archive';\n    return 'fa-file';\n  }\n  getFileType(mimeType) {\n    if (!mimeType) return 'File';\n    const typeMap = {\n      'image/': 'Image',\n      'application/pdf': 'PDF',\n      'application/msword': 'Word Doc',\n      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'Word Doc',\n      'application/vnd.ms-excel': 'Excel',\n      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'Excel',\n      'application/vnd.ms-powerpoint': 'PowerPoint',\n      'application/vnd.openxmlformats-officedocument.presentationml.presentation': 'PowerPoint',\n      'audio/': 'Audio',\n      'video/': 'Video',\n      'application/zip': 'ZIP Archive',\n      'application/x-rar-compressed': 'RAR Archive'\n    };\n    for (const [key, value] of Object.entries(typeMap)) {\n      if (mimeType.includes(key)) return value;\n    }\n    return 'File';\n  }\n  handleConversationLoaded(conversation) {\n    this.logger.info('MessageChat', `Handling loaded conversation: ${conversation.id}`);\n    this.logger.debug('MessageChat', `Conversation has ${conversation?.messages?.length || 0} messages and ${conversation?.participants?.length || 0} participants`);\n    // Log détaillé des messages pour le débogage\n    if (conversation?.messages && conversation.messages.length > 0) {\n      this.logger.debug('MessageChat', `First message details: id=${conversation.messages[0].id}, content=${conversation.messages[0].content?.substring(0, 20)}, sender=${conversation.messages[0].sender?.username}`);\n    }\n    this.conversation = conversation;\n    // Si la conversation n'a pas de messages, initialiser un tableau vide\n    if (!conversation?.messages || conversation.messages.length === 0) {\n      this.logger.debug('MessageChat', 'No messages found in conversation');\n      // Récupérer les participants\n      this.otherParticipant = conversation?.participants?.find(p => p.id !== this.currentUserId && p._id !== this.currentUserId) || null;\n      // Initialiser un tableau vide pour les messages\n      this.messages = [];\n      this.logger.debug('MessageChat', 'Initialized empty messages array');\n    } else {\n      // Récupérer les messages de la conversation\n      const conversationMessages = [...(conversation?.messages || [])];\n      // Trier les messages par date (du plus ancien au plus récent)\n      conversationMessages.sort((a, b) => {\n        const timeA = a.timestamp instanceof Date ? a.timestamp.getTime() : new Date(a.timestamp).getTime();\n        const timeB = b.timestamp instanceof Date ? b.timestamp.getTime() : new Date(b.timestamp).getTime();\n        return timeA - timeB;\n      });\n      // Log détaillé pour comprendre la structure des messages\n      if (conversationMessages.length > 0) {\n        const firstMessage = conversationMessages[0];\n        this.logger.debug('MessageChat', `Message structure: sender.id=${firstMessage.sender?.id}, sender._id=${firstMessage.sender?._id}, senderId=${firstMessage.senderId}, receiver.id=${firstMessage.receiver?.id}, receiver._id=${firstMessage.receiver?._id}, receiverId=${firstMessage.receiverId}`);\n      }\n      // Utiliser directement tous les messages triés sans filtrage supplémentaire\n      this.messages = conversationMessages;\n      this.logger.debug('MessageChat', `Using all ${this.messages.length} messages from conversation`);\n      this.logger.debug('MessageChat', `Using ${conversationMessages.length} messages from conversation, showing last ${this.messages.length}`);\n    }\n    this.otherParticipant = conversation?.participants?.find(p => p.id !== this.currentUserId && p._id !== this.currentUserId) || null;\n    this.logger.debug('MessageChat', `Other participant identified: ${this.otherParticipant?.username || 'Unknown'}`);\n    this.loading = false;\n    setTimeout(() => this.scrollToBottom(), 100);\n    this.logger.debug('MessageChat', `Marking unread messages as read`);\n    this.markMessagesAsRead();\n    if (this.conversation?.id) {\n      this.logger.debug('MessageChat', `Setting up subscriptions for conversation: ${this.conversation.id}`);\n      this.subscribeToConversationUpdates(this.conversation.id);\n      this.subscribeToNewMessages(this.conversation.id);\n      this.subscribeToTypingIndicators(this.conversation.id);\n    }\n    this.logger.info('MessageChat', `Conversation loaded successfully`);\n  }\n  subscribeToConversationUpdates(conversationId) {\n    const sub = this.MessageService.subscribeToConversationUpdates(conversationId).subscribe({\n      next: updatedConversation => {\n        this.conversation = updatedConversation;\n        this.messages = updatedConversation.messages ? [...updatedConversation.messages] : [];\n        this.scrollToBottom();\n      },\n      error: error => {\n        this.toastService.showError('Connection to conversation updates lost');\n      }\n    });\n    this.subscriptions.add(sub);\n  }\n  subscribeToNewMessages(conversationId) {\n    const sub = this.MessageService.subscribeToNewMessages(conversationId).subscribe({\n      next: newMessage => {\n        if (newMessage?.conversationId === this.conversation?.id) {\n          // Ajouter le nouveau message à la liste complète\n          this.messages = [...this.messages, newMessage].sort((a, b) => {\n            const timeA = a.timestamp instanceof Date ? a.timestamp.getTime() : new Date(a.timestamp).getTime();\n            const timeB = b.timestamp instanceof Date ? b.timestamp.getTime() : new Date(b.timestamp).getTime();\n            return timeA - timeB; // Tri par ordre croissant pour l'affichage\n          });\n\n          this.logger.debug('MessageChat', `Added new message, now showing ${this.messages.length} messages`);\n          setTimeout(() => this.scrollToBottom(), 100);\n          // Marquer le message comme lu s'il vient d'un autre utilisateur\n          if (newMessage.sender?.id !== this.currentUserId && newMessage.sender?._id !== this.currentUserId) {\n            if (newMessage.id) {\n              this.MessageService.markMessageAsRead(newMessage.id).subscribe();\n            }\n          }\n        }\n      },\n      error: error => {\n        this.toastService.showError('Connection to new messages lost');\n      }\n    });\n    this.subscriptions.add(sub);\n  }\n  subscribeToTypingIndicators(conversationId) {\n    const sub = this.MessageService.subscribeToTypingIndicator(conversationId).subscribe({\n      next: event => {\n        if (event.userId !== this.currentUserId) {\n          this.isTyping = event.isTyping;\n          if (this.isTyping) {\n            clearTimeout(this.typingTimeout);\n            this.typingTimeout = setTimeout(() => {\n              this.isTyping = false;\n            }, 2000);\n          }\n        }\n      }\n    });\n    this.subscriptions.add(sub);\n  }\n  markMessagesAsRead() {\n    const unreadMessages = this.messages.filter(msg => !msg.isRead && (msg.receiver?.id === this.currentUserId || msg.receiver?._id === this.currentUserId));\n    unreadMessages.forEach(msg => {\n      if (msg.id) {\n        const sub = this.MessageService.markMessageAsRead(msg.id).subscribe({\n          error: error => {\n            this.logger.error('MessageChat', 'Error marking message as read:', error);\n          }\n        });\n        this.subscriptions.add(sub);\n      }\n    });\n  }\n  onFileSelected(event) {\n    const file = event.target.files[0];\n    if (!file) return;\n    // Validate file size (e.g., 5MB max)\n    if (file.size > 5 * 1024 * 1024) {\n      this.toastService.showError('File size should be less than 5MB');\n      return;\n    }\n    // Validate file type\n    const validTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];\n    if (!validTypes.includes(file.type)) {\n      this.toastService.showError('Invalid file type. Only images, PDFs and Word docs are allowed');\n      return;\n    }\n    this.selectedFile = file;\n    const reader = new FileReader();\n    reader.onload = () => {\n      this.previewUrl = reader.result;\n    };\n    reader.readAsDataURL(file);\n  }\n  removeAttachment() {\n    this.selectedFile = null;\n    this.previewUrl = null;\n    if (this.fileInput?.nativeElement) {\n      this.fileInput.nativeElement.value = '';\n    }\n  }\n  /**\n   * Gère l'événement de frappe de l'utilisateur\n   * Envoie un indicateur de frappe avec un délai pour éviter trop de requêtes\n   */\n  onTyping() {\n    if (!this.conversation?.id || !this.currentUserId) {\n      return;\n    }\n    // Stocker l'ID de conversation pour éviter les erreurs TypeScript\n    const conversationId = this.conversation.id;\n    // Annuler le timer précédent\n    clearTimeout(this.typingTimer);\n    // Si l'utilisateur n'est pas déjà en train de taper, envoyer l'événement immédiatement\n    if (!this.isCurrentlyTyping) {\n      this.isCurrentlyTyping = true;\n      this.logger.debug('MessageChat', 'Starting typing indicator');\n      this.MessageService.startTyping(conversationId).subscribe({\n        next: () => {\n          this.logger.debug('MessageChat', 'Typing indicator started successfully');\n        },\n        error: error => {\n          this.logger.error('MessageChat', 'Error starting typing indicator:', error);\n        }\n      });\n    }\n    // Définir un timer pour arrêter l'indicateur de frappe après un délai d'inactivité\n    this.typingTimer = setTimeout(() => {\n      if (this.isCurrentlyTyping) {\n        this.isCurrentlyTyping = false;\n        this.logger.debug('MessageChat', 'Stopping typing indicator due to inactivity');\n        this.MessageService.stopTyping(conversationId).subscribe({\n          next: () => {\n            this.logger.debug('MessageChat', 'Typing indicator stopped successfully');\n          },\n          error: error => {\n            this.logger.error('MessageChat', 'Error stopping typing indicator:', error);\n          }\n        });\n      }\n    }, this.TYPING_TIMEOUT);\n  }\n  /**\n   * Affiche ou masque le sélecteur de thème\n   */\n  toggleThemeSelector() {\n    this.showThemeSelector = !this.showThemeSelector;\n    // Fermer le sélecteur de thème lorsqu'on clique ailleurs\n    if (this.showThemeSelector) {\n      setTimeout(() => {\n        const clickHandler = event => {\n          const target = event.target;\n          if (!target.closest('.theme-selector')) {\n            this.showThemeSelector = false;\n            document.removeEventListener('click', clickHandler);\n          }\n        };\n        document.addEventListener('click', clickHandler);\n      }, 0);\n    }\n  }\n  /**\n   * Change le thème de la conversation\n   * @param theme Nom du thème à appliquer\n   */\n  changeTheme(theme) {\n    this.selectedTheme = theme;\n    this.showThemeSelector = false;\n    // Sauvegarder le thème dans le localStorage pour le conserver entre les sessions\n    localStorage.setItem('chat-theme', theme);\n    this.logger.debug('MessageChat', `Theme changed to: ${theme}`);\n  }\n  sendMessage() {\n    this.logger.info('MessageChat', `Attempting to send message`);\n    // Vérifier l'authentification\n    const token = localStorage.getItem('token');\n    this.logger.debug('MessageChat', `Authentication check: token=${!!token}, userId=${this.currentUserId}`);\n    if (this.messageForm.invalid && !this.selectedFile || !this.currentUserId || !this.otherParticipant?.id) {\n      this.logger.warn('MessageChat', `Cannot send message: form invalid or missing user IDs`);\n      return;\n    }\n    // Arrêter l'indicateur de frappe lorsqu'un message est envoyé\n    this.stopTypingIndicator();\n    const content = this.messageForm.get('content')?.value;\n    // Créer un message temporaire pour l'affichage immédiat (comme dans Facebook Messenger)\n    const tempMessage = {\n      id: 'temp-' + new Date().getTime(),\n      content: content || '',\n      sender: {\n        id: this.currentUserId || '',\n        username: this.currentUsername\n      },\n      receiver: {\n        id: this.otherParticipant.id,\n        username: this.otherParticipant.username || 'Recipient'\n      },\n      timestamp: new Date(),\n      isRead: false,\n      isPending: true // Marquer comme en attente\n    };\n    // Si un fichier est sélectionné, ajouter l'aperçu au message temporaire\n    if (this.selectedFile) {\n      // Déterminer le type de fichier\n      let fileType = 'file';\n      if (this.selectedFile.type.startsWith('image/')) {\n        fileType = 'image';\n        // Pour les images, ajouter un aperçu immédiat\n        if (this.previewUrl) {\n          tempMessage.attachments = [{\n            id: 'temp-attachment',\n            url: this.previewUrl ? this.previewUrl.toString() : '',\n            type: MessageType.IMAGE,\n            name: this.selectedFile.name,\n            size: this.selectedFile.size\n          }];\n        }\n      }\n      // Définir le type de message en fonction du type de fichier\n      if (fileType === 'image') {\n        tempMessage.type = MessageType.IMAGE;\n      } else if (fileType === 'file') {\n        tempMessage.type = MessageType.FILE;\n      }\n    }\n    // Ajouter immédiatement le message temporaire à la liste\n    this.messages = [...this.messages, tempMessage];\n    // Réinitialiser le formulaire immédiatement pour une meilleure expérience utilisateur\n    const fileToSend = this.selectedFile; // Sauvegarder une référence\n    this.messageForm.reset();\n    this.removeAttachment();\n    // Forcer le défilement vers le bas immédiatement\n    setTimeout(() => this.scrollToBottom(true), 50);\n    // Maintenant, envoyer le message au serveur\n    this.isUploading = true;\n    const sendSub = this.MessageService.sendMessage(this.otherParticipant.id, content, fileToSend || undefined, MessageType.TEXT).subscribe({\n      next: message => {\n        this.logger.info('MessageChat', `Message sent successfully: ${message?.id || 'unknown'}`);\n        // Remplacer le message temporaire par le message réel\n        this.messages = this.messages.map(msg => msg.id === tempMessage.id ? message : msg);\n        this.isUploading = false;\n      },\n      error: error => {\n        this.logger.error('MessageChat', `Error sending message:`, error);\n        // Marquer le message temporaire comme échoué\n        this.messages = this.messages.map(msg => {\n          if (msg.id === tempMessage.id) {\n            return {\n              ...msg,\n              isPending: false,\n              isError: true\n            };\n          }\n          return msg;\n        });\n        this.isUploading = false;\n        this.toastService.showError('Failed to send message');\n      }\n    });\n    this.subscriptions.add(sendSub);\n  }\n  formatMessageTime(timestamp) {\n    if (!timestamp) {\n      return 'Unknown time';\n    }\n    try {\n      const date = timestamp instanceof Date ? timestamp : new Date(timestamp);\n      // Format heure:minute sans les secondes, comme dans l'image de référence\n      return date.toLocaleTimeString([], {\n        hour: '2-digit',\n        minute: '2-digit',\n        hour12: false\n      });\n    } catch (error) {\n      this.logger.error('MessageChat', 'Error formatting message time:', error);\n      return 'Invalid time';\n    }\n  }\n  formatLastActive(lastActive) {\n    if (!lastActive) return 'Offline';\n    const lastActiveDate = lastActive instanceof Date ? lastActive : new Date(lastActive);\n    const now = new Date();\n    const diffHours = Math.abs(now.getTime() - lastActiveDate.getTime()) / (1000 * 60 * 60);\n    if (diffHours < 24) {\n      return `Active ${lastActiveDate.toLocaleTimeString([], {\n        hour: '2-digit',\n        minute: '2-digit'\n      })}`;\n    }\n    return `Active ${lastActiveDate.toLocaleDateString()}`;\n  }\n  formatMessageDate(timestamp) {\n    if (!timestamp) {\n      return 'Unknown date';\n    }\n    try {\n      const date = timestamp instanceof Date ? timestamp : new Date(timestamp);\n      const today = new Date();\n      // Format pour l'affichage comme dans l'image de référence\n      const options = {\n        weekday: 'short',\n        hour: '2-digit',\n        minute: '2-digit'\n      };\n      if (date.toDateString() === today.toDateString()) {\n        return date.toLocaleTimeString([], {\n          hour: '2-digit',\n          minute: '2-digit'\n        });\n      }\n      const yesterday = new Date(today);\n      yesterday.setDate(yesterday.getDate() - 1);\n      if (date.toDateString() === yesterday.toDateString()) {\n        return `LUN., ${date.toLocaleTimeString([], {\n          hour: '2-digit',\n          minute: '2-digit'\n        })}`;\n      }\n      // Format pour les autres jours (comme dans l'image)\n      const day = date.toLocaleDateString('fr-FR', {\n        weekday: 'short'\n      }).toUpperCase();\n      return `${day}., ${date.toLocaleTimeString([], {\n        hour: '2-digit',\n        minute: '2-digit'\n      })}`;\n    } catch (error) {\n      this.logger.error('MessageChat', 'Error formatting message date:', error);\n      return 'Invalid date';\n    }\n  }\n  shouldShowDateHeader(index) {\n    if (index === 0) return true;\n    try {\n      const currentMsg = this.messages[index];\n      const prevMsg = this.messages[index - 1];\n      if (!currentMsg?.timestamp || !prevMsg?.timestamp) {\n        return true;\n      }\n      const currentDate = this.getDateFromTimestamp(currentMsg.timestamp);\n      const prevDate = this.getDateFromTimestamp(prevMsg.timestamp);\n      return currentDate !== prevDate;\n    } catch (error) {\n      this.logger.error('MessageChat', 'Error checking date header:', error);\n      return false;\n    }\n  }\n  getDateFromTimestamp(timestamp) {\n    if (!timestamp) {\n      return 'unknown-date';\n    }\n    try {\n      return (timestamp instanceof Date ? timestamp : new Date(timestamp)).toDateString();\n    } catch (error) {\n      this.logger.error('MessageChat', 'Error getting date from timestamp:', error);\n      return 'invalid-date';\n    }\n  }\n  getMessageType(message) {\n    if (!message) {\n      return MessageType.TEXT;\n    }\n    try {\n      // Vérifier d'abord le type de message explicite\n      if (message.type) {\n        // Convertir les types en minuscules en leurs équivalents en majuscules\n        const msgType = message.type.toString();\n        if (msgType === 'text' || msgType === 'TEXT') {\n          return MessageType.TEXT;\n        } else if (msgType === 'image' || msgType === 'IMAGE') {\n          return MessageType.IMAGE;\n        } else if (msgType === 'file' || msgType === 'FILE') {\n          return MessageType.FILE;\n        } else if (msgType === 'audio' || msgType === 'AUDIO') {\n          return MessageType.AUDIO;\n        } else if (msgType === 'video' || msgType === 'VIDEO') {\n          return MessageType.VIDEO;\n        } else if (msgType === 'system' || msgType === 'SYSTEM') {\n          return MessageType.SYSTEM;\n        }\n      }\n      // Ensuite, vérifier les pièces jointes\n      if (message.attachments?.length) {\n        const attachment = message.attachments[0];\n        if (attachment && attachment.type) {\n          const attachmentTypeStr = attachment.type.toString();\n          // Gérer les différentes formes de types d'attachements\n          if (attachmentTypeStr === 'image' || attachmentTypeStr === 'IMAGE') {\n            return MessageType.IMAGE;\n          } else if (attachmentTypeStr === 'file' || attachmentTypeStr === 'FILE') {\n            return MessageType.FILE;\n          } else if (attachmentTypeStr === 'audio' || attachmentTypeStr === 'AUDIO') {\n            return MessageType.AUDIO;\n          } else if (attachmentTypeStr === 'video' || attachmentTypeStr === 'VIDEO') {\n            return MessageType.VIDEO;\n          }\n        }\n        // Type par défaut pour les pièces jointes\n        return MessageType.FILE;\n      }\n      // Type par défaut\n      return MessageType.TEXT;\n    } catch (error) {\n      this.logger.error('MessageChat', 'Error getting message type:', error);\n      return MessageType.TEXT;\n    }\n  }\n  // Méthode auxiliaire pour vérifier si un message contient une image\n  hasImage(message) {\n    if (!message || !message.attachments || message.attachments.length === 0) {\n      return false;\n    }\n    const attachment = message.attachments[0];\n    if (!attachment || !attachment.type) {\n      return false;\n    }\n    const type = attachment.type.toString();\n    return type === 'IMAGE' || type === 'image';\n  }\n  /**\n   * Vérifie si le message est un message vocal\n   */\n  isVoiceMessage(message) {\n    if (!message) {\n      return false;\n    }\n    // Vérifier le type du message\n    if (message.type === MessageType.VOICE_MESSAGE || message.type === MessageType.VOICE_MESSAGE_LOWER) {\n      return true;\n    }\n    // Vérifier les pièces jointes\n    if (message.attachments && message.attachments.length > 0) {\n      return message.attachments.some(att => {\n        const type = att.type?.toString();\n        return type === 'VOICE_MESSAGE' || type === 'voice_message' || message.metadata?.isVoiceMessage && (type === 'AUDIO' || type === 'audio');\n      });\n    }\n    // Vérifier les métadonnées\n    return !!message.metadata?.isVoiceMessage;\n  }\n  /**\n   * Récupère l'URL du message vocal\n   */\n  getVoiceMessageUrl(message) {\n    if (!message || !message.attachments || message.attachments.length === 0) {\n      return '';\n    }\n    // Chercher une pièce jointe de type message vocal ou audio\n    const voiceAttachment = message.attachments.find(att => {\n      const type = att.type?.toString();\n      return type === 'VOICE_MESSAGE' || type === 'voice_message' || type === 'AUDIO' || type === 'audio';\n    });\n    return voiceAttachment?.url || '';\n  }\n  /**\n   * Récupère la durée du message vocal\n   */\n  getVoiceMessageDuration(message) {\n    if (!message) {\n      return 0;\n    }\n    // Essayer d'abord de récupérer la durée depuis les métadonnées\n    if (message.metadata?.duration) {\n      return message.metadata.duration;\n    }\n    // Sinon, essayer de récupérer depuis les pièces jointes\n    if (message.attachments && message.attachments.length > 0) {\n      const voiceAttachment = message.attachments.find(att => {\n        const type = att.type?.toString();\n        return type === 'VOICE_MESSAGE' || type === 'voice_message' || type === 'AUDIO' || type === 'audio';\n      });\n      if (voiceAttachment && voiceAttachment.duration) {\n        return voiceAttachment.duration;\n      }\n    }\n    return 0;\n  }\n  // Méthode pour obtenir l'URL de l'image en toute sécurité\n  getImageUrl(message) {\n    if (!message || !message.attachments || message.attachments.length === 0) {\n      return '';\n    }\n    const attachment = message.attachments[0];\n    return attachment?.url || '';\n  }\n  getMessageTypeClass(message) {\n    if (!message) {\n      return 'bg-gray-100 rounded-lg px-4 py-2';\n    }\n    try {\n      const isCurrentUser = message.sender?.id === this.currentUserId || message.sender?._id === this.currentUserId || message.senderId === this.currentUserId;\n      // Utiliser une couleur plus foncée pour les messages de l'utilisateur actuel (à droite)\n      // et une couleur plus claire pour les messages des autres utilisateurs (à gauche)\n      // Couleurs et forme adaptées exactement à l'image de référence mobile\n      const baseClass = isCurrentUser ? 'bg-blue-500 text-white rounded-2xl rounded-br-sm' : 'bg-gray-200 text-gray-800 rounded-2xl rounded-bl-sm';\n      const messageType = this.getMessageType(message);\n      // Vérifier si le message contient une image\n      if (message.attachments && message.attachments.length > 0) {\n        const attachment = message.attachments[0];\n        if (attachment && attachment.type) {\n          const attachmentTypeStr = attachment.type.toString();\n          if (attachmentTypeStr === 'IMAGE' || attachmentTypeStr === 'image') {\n            // Pour les images, on utilise un style sans bordure\n            return `p-1 max-w-xs`;\n          } else if (attachmentTypeStr === 'FILE' || attachmentTypeStr === 'file') {\n            return `${baseClass} p-3`;\n          }\n        }\n      }\n      // Vérifier le type de message\n      if (messageType === MessageType.IMAGE || messageType === MessageType.IMAGE_LOWER) {\n        // Pour les images, on utilise un style sans bordure\n        return `p-1 max-w-xs`;\n      } else if (messageType === MessageType.FILE || messageType === MessageType.FILE_LOWER) {\n        return `${baseClass} p-3`;\n      }\n      // Type par défaut (texte)\n      return `${baseClass} px-4 py-3 whitespace-normal break-words min-w-[120px]`;\n    } catch (error) {\n      this.logger.error('MessageChat', 'Error getting message type class:', error);\n      return 'bg-gray-100 rounded-lg px-4 py-2 whitespace-normal break-words';\n    }\n  }\n  // La méthode ngAfterViewChecked est implémentée plus bas dans le fichier\n  // Méthode pour détecter le défilement vers le haut et charger plus de messages\n  onScroll(event) {\n    const container = event.target;\n    const scrollTop = container.scrollTop;\n    // Si on est proche du haut de la liste et qu'on n'est pas déjà en train de charger\n    if (scrollTop < 50 && !this.isLoadingMore && this.conversation?.id && this.hasMoreMessages) {\n      // Afficher un indicateur de chargement en haut de la liste\n      this.showLoadingIndicator();\n      // Sauvegarder la hauteur actuelle et la position des messages\n      const oldScrollHeight = container.scrollHeight;\n      const firstVisibleMessage = this.getFirstVisibleMessage();\n      // Marquer comme chargement en cours\n      this.isLoadingMore = true;\n      // Charger plus de messages avec un délai réduit\n      this.loadMoreMessages();\n      // Maintenir la position de défilement pour que l'utilisateur reste au même endroit\n      // en utilisant le premier message visible comme ancre\n      requestAnimationFrame(() => {\n        const preserveScrollPosition = () => {\n          if (firstVisibleMessage) {\n            const messageElement = this.findMessageElement(firstVisibleMessage.id);\n            if (messageElement) {\n              // Faire défiler jusqu'à l'élément qui était visible avant\n              messageElement.scrollIntoView({\n                block: 'center'\n              });\n            } else {\n              // Fallback: utiliser la différence de hauteur\n              const newScrollHeight = container.scrollHeight;\n              const scrollDiff = newScrollHeight - oldScrollHeight;\n              container.scrollTop = scrollTop + scrollDiff;\n            }\n          }\n          // Masquer l'indicateur de chargement\n          this.hideLoadingIndicator();\n        };\n        // Attendre que le DOM soit mis à jour\n        setTimeout(preserveScrollPosition, 100);\n      });\n    }\n  }\n  // Méthode pour trouver le premier message visible dans la vue\n  getFirstVisibleMessage() {\n    if (!this.messagesContainer?.nativeElement || !this.messages.length) return null;\n    const container = this.messagesContainer.nativeElement;\n    const messageElements = container.querySelectorAll('.message-item');\n    for (let i = 0; i < messageElements.length; i++) {\n      const element = messageElements[i];\n      const rect = element.getBoundingClientRect();\n      // Si l'élément est visible dans la vue\n      if (rect.top >= 0 && rect.bottom <= container.clientHeight) {\n        const messageId = element.getAttribute('data-message-id');\n        return this.messages.find(m => m.id === messageId) || null;\n      }\n    }\n    return null;\n  }\n  // Méthode pour trouver un élément de message par ID\n  findMessageElement(messageId) {\n    if (!this.messagesContainer?.nativeElement || !messageId) return null;\n    return this.messagesContainer.nativeElement.querySelector(`[data-message-id=\"${messageId}\"]`);\n  }\n  // Afficher un indicateur de chargement en haut de la liste\n  showLoadingIndicator() {\n    // Créer l'indicateur s'il n'existe pas déjà\n    if (!document.getElementById('message-loading-indicator')) {\n      const indicator = document.createElement('div');\n      indicator.id = 'message-loading-indicator';\n      indicator.className = 'text-center py-2 text-gray-500 text-sm';\n      indicator.innerHTML = '<i class=\"fas fa-spinner fa-spin mr-2\"></i> Loading older messages...';\n      if (this.messagesContainer?.nativeElement) {\n        this.messagesContainer.nativeElement.prepend(indicator);\n      }\n    }\n  }\n  // Masquer l'indicateur de chargement\n  hideLoadingIndicator() {\n    const indicator = document.getElementById('message-loading-indicator');\n    if (indicator && indicator.parentNode) {\n      indicator.parentNode.removeChild(indicator);\n    }\n  }\n  // Méthode pour charger plus de messages (style Facebook Messenger)\n  loadMoreMessages() {\n    if (this.isLoadingMore || !this.conversation?.id || !this.hasMoreMessages) return;\n    // Marquer comme chargement en cours\n    this.isLoadingMore = true;\n    // Augmenter la page pour charger les messages plus anciens\n    this.currentPage++;\n    // Charger plus de messages depuis le serveur avec pagination\n    this.MessageService.getConversation(this.conversation.id, this.MAX_MESSAGES_TO_LOAD, this.currentPage).subscribe({\n      next: conversation => {\n        if (conversation && conversation.messages && conversation.messages.length > 0) {\n          // Sauvegarder les messages actuels\n          const oldMessages = [...this.messages];\n          // Créer un Set des IDs existants pour une recherche de doublons plus rapide\n          const existingIds = new Set(oldMessages.map(msg => msg.id));\n          // Filtrer et trier les nouveaux messages plus efficacement\n          const newMessages = conversation.messages.filter(msg => !existingIds.has(msg.id)).sort((a, b) => {\n            const timeA = new Date(a.timestamp).getTime();\n            const timeB = new Date(b.timestamp).getTime();\n            return timeA - timeB;\n          });\n          if (newMessages.length > 0) {\n            // Ajouter les nouveaux messages au début de la liste\n            this.messages = [...newMessages, ...oldMessages];\n            // Limiter le nombre total de messages pour éviter les problèmes de performance\n            if (this.messages.length > this.MAX_TOTAL_MESSAGES) {\n              this.messages = this.messages.slice(0, this.MAX_TOTAL_MESSAGES);\n            }\n            // Vérifier s'il y a plus de messages à charger\n            this.hasMoreMessages = newMessages.length >= this.MAX_MESSAGES_TO_LOAD;\n          } else {\n            // Si aucun nouveau message n'est chargé, c'est qu'on a atteint le début de la conversation\n            this.hasMoreMessages = false;\n          }\n        } else {\n          this.hasMoreMessages = false;\n        }\n        // Désactiver le flag de chargement après un court délai\n        // pour permettre au DOM de se mettre à jour\n        setTimeout(() => {\n          this.isLoadingMore = false;\n        }, 200);\n      },\n      error: error => {\n        this.logger.error('MessageChat', 'Error loading more messages:', error);\n        this.isLoadingMore = false;\n        this.hideLoadingIndicator();\n        this.toastService.showError('Failed to load more messages');\n      }\n    });\n  }\n  // Méthode utilitaire pour comparer les timestamps\n  isSameTimestamp(timestamp1, timestamp2) {\n    if (!timestamp1 || !timestamp2) return false;\n    try {\n      const time1 = timestamp1 instanceof Date ? timestamp1.getTime() : new Date(timestamp1).getTime();\n      const time2 = timestamp2 instanceof Date ? timestamp2.getTime() : new Date(timestamp2).getTime();\n      return Math.abs(time1 - time2) < 1000; // Tolérance d'une seconde\n    } catch (error) {\n      return false;\n    }\n  }\n  scrollToBottom(force = false) {\n    try {\n      if (!this.messagesContainer?.nativeElement) return;\n      // Utiliser requestAnimationFrame pour s'assurer que le DOM est prêt\n      requestAnimationFrame(() => {\n        const container = this.messagesContainer.nativeElement;\n        const isScrolledToBottom = container.scrollHeight - container.clientHeight <= container.scrollTop + 150;\n        // Faire défiler vers le bas si:\n        // - force est true (pour les nouveaux messages envoyés par l'utilisateur)\n        // - ou si l'utilisateur est déjà proche du bas\n        if (force || isScrolledToBottom) {\n          // Utiliser une animation fluide pour le défilement (comme dans Messenger)\n          container.scrollTo({\n            top: container.scrollHeight,\n            behavior: 'smooth'\n          });\n        }\n      });\n    } catch (err) {\n      this.logger.error('MessageChat', 'Error scrolling to bottom:', err);\n    }\n  }\n  // Méthode pour ouvrir l'image en plein écran (style Messenger)\n  /**\n   * Active/désactive l'enregistrement vocal\n   */\n  toggleVoiceRecording() {\n    this.isRecordingVoice = !this.isRecordingVoice;\n    if (!this.isRecordingVoice) {\n      // Si on désactive l'enregistrement, réinitialiser la durée\n      this.voiceRecordingDuration = 0;\n    }\n  }\n  /**\n   * Gère la fin de l'enregistrement vocal\n   * @param audioBlob Blob audio enregistré\n   */\n  onVoiceRecordingComplete(audioBlob) {\n    this.logger.debug('MessageChat', 'Voice recording complete, size:', audioBlob.size);\n    if (!this.conversation?.id && !this.otherParticipant?.id) {\n      this.toastService.showError('No conversation or recipient selected');\n      this.isRecordingVoice = false;\n      return;\n    }\n    // Récupérer l'ID du destinataire\n    const receiverId = this.otherParticipant?.id || '';\n    // Envoyer le message vocal\n    this.MessageService.sendVoiceMessage(receiverId, audioBlob, this.conversation?.id, this.voiceRecordingDuration).subscribe({\n      next: message => {\n        this.logger.debug('MessageChat', 'Voice message sent:', message);\n        this.isRecordingVoice = false;\n        this.voiceRecordingDuration = 0;\n        this.scrollToBottom(true);\n      },\n      error: error => {\n        this.logger.error('MessageChat', 'Error sending voice message:', error);\n        this.toastService.showError('Failed to send voice message');\n        this.isRecordingVoice = false;\n      }\n    });\n  }\n  /**\n   * Gère l'annulation de l'enregistrement vocal\n   */\n  onVoiceRecordingCancelled() {\n    this.logger.debug('MessageChat', 'Voice recording cancelled');\n    this.isRecordingVoice = false;\n    this.voiceRecordingDuration = 0;\n  }\n  /**\n   * Ouvre une image en plein écran (méthode conservée pour compatibilité)\n   * @param imageUrl URL de l'image à afficher\n   */\n  openImageFullscreen(imageUrl) {\n    // Ouvrir l'image dans un nouvel onglet\n    window.open(imageUrl, '_blank');\n    this.logger.debug('MessageChat', `Image opened in new tab: ${imageUrl}`);\n  }\n  /**\n   * Détecte les changements après chaque vérification de la vue\n   * Cela permet de s'assurer que les messages vocaux sont correctement affichés\n   * et que le défilement est maintenu\n   */\n  ngAfterViewChecked() {\n    // Faire défiler vers le bas si nécessaire\n    this.scrollToBottom();\n    // Forcer la détection des changements pour les messages vocaux\n    // Cela garantit que les messages vocaux sont correctement affichés même après avoir quitté la conversation\n    if (this.messages.some(msg => msg.type === MessageType.VOICE_MESSAGE)) {\n      // Utiliser setTimeout pour éviter l'erreur ExpressionChangedAfterItHasBeenCheckedError\n      setTimeout(() => {\n        this.cdr.detectChanges();\n      }, 0);\n    }\n  }\n  /**\n   * Arrête l'indicateur de frappe\n   */\n  stopTypingIndicator() {\n    if (this.isCurrentlyTyping && this.conversation?.id) {\n      this.isCurrentlyTyping = false;\n      clearTimeout(this.typingTimer);\n      this.logger.debug('MessageChat', 'Stopping typing indicator');\n      // Utiliser l'opérateur de chaînage optionnel pour éviter les erreurs TypeScript\n      const conversationId = this.conversation?.id;\n      if (conversationId) {\n        this.MessageService.stopTyping(conversationId).subscribe({\n          next: () => {\n            this.logger.debug('MessageChat', 'Typing indicator stopped successfully');\n          },\n          error: error => {\n            this.logger.error('MessageChat', 'Error stopping typing indicator:', error);\n          }\n        });\n      }\n    }\n  }\n  ngOnDestroy() {\n    // Arrêter l'indicateur de frappe lorsque l'utilisateur quitte la conversation\n    this.stopTypingIndicator();\n    this.subscriptions.unsubscribe();\n    clearTimeout(this.typingTimeout);\n  }\n  /**\n   * Navigue vers la liste des conversations\n   */\n  goBackToConversations() {\n    this.router.navigate(['/messages/conversations']);\n  }\n  /**\n   * Bascule l'affichage du sélecteur d'émojis\n   */\n  toggleEmojiPicker() {\n    this.showEmojiPicker = !this.showEmojiPicker;\n    if (this.showEmojiPicker) {\n      this.showThemeSelector = false;\n    }\n  }\n  /**\n   * Insère un emoji dans le champ de message\n   * @param emoji Emoji à insérer\n   */\n  insertEmoji(emoji) {\n    const control = this.messageForm.get('content');\n    if (control) {\n      const currentValue = control.value || '';\n      control.setValue(currentValue + emoji);\n      control.markAsDirty();\n      // Garder le focus sur le champ de saisie\n      setTimeout(() => {\n        const inputElement = document.querySelector('.whatsapp-input-field');\n        if (inputElement) {\n          inputElement.focus();\n        }\n      }, 0);\n    }\n  }\n  /**\n   * S'abonne aux notifications en temps réel\n   */\n  subscribeToNotifications() {\n    // S'abonner aux nouvelles notifications\n    const notificationSub = this.MessageService.subscribeToNewNotifications().subscribe({\n      next: notification => {\n        this.logger.debug('MessageChat', `Nouvelle notification reçue: ${notification.type}`);\n        // Si c'est une notification de message et que nous sommes dans la conversation concernée\n        if (notification.type === 'NEW_MESSAGE' && notification.conversationId === this.conversation?.id) {\n          // Marquer automatiquement comme lue\n          if (notification.id) {\n            this.MessageService.markAsRead([notification.id]).subscribe();\n          }\n        }\n      },\n      error: error => {\n        this.logger.error('MessageChat', 'Erreur lors de la réception des notifications:', error);\n      }\n    });\n    this.subscriptions.add(notificationSub);\n    // S'abonner aux appels entrants\n    const callSub = this.MessageService.incomingCall$.subscribe({\n      next: call => {\n        if (call) {\n          this.logger.debug('MessageChat', `Appel entrant de: ${call.caller.username}`);\n          this.incomingCall = call;\n          this.showCallModal = true;\n          // Jouer la sonnerie\n          this.MessageService.play('ringtone');\n        } else {\n          this.showCallModal = false;\n          this.incomingCall = null;\n        }\n      }\n    });\n    this.subscriptions.add(callSub);\n  }\n  /**\n   * Initie un appel audio ou vidéo avec l'autre participant\n   * @param type Type d'appel (AUDIO ou VIDEO)\n   */\n  initiateCall(type) {\n    if (!this.otherParticipant || !this.otherParticipant.id) {\n      console.error(\"Impossible d'initier un appel: participant invalide\");\n      return;\n    }\n    this.logger.info('MessageChat', `Initiation d'un appel ${type} avec ${this.otherParticipant.username}`);\n    // Utiliser le service d'appel pour initier l'appel\n    this.MessageService.initiateCall(this.otherParticipant.id, type === 'AUDIO' ? CallType.AUDIO : CallType.VIDEO, this.conversation?.id).subscribe({\n      next: call => {\n        this.logger.info('MessageChat', 'Appel initié avec succès:', call);\n        // Ici, vous pourriez ouvrir une fenêtre d'appel ou rediriger vers une page d'appel\n      },\n\n      error: error => {\n        this.logger.error('MessageChat', \"Erreur lors de l'initiation de l'appel:\", error);\n        this.toastService.showError(\"Impossible d'initier l'appel. Veuillez réessayer.\");\n      }\n    });\n  }\n  /**\n   * Accepte un appel entrant\n   */\n  acceptCall() {\n    if (!this.incomingCall) {\n      this.logger.error('MessageChat', 'Aucun appel entrant à accepter');\n      return;\n    }\n    this.logger.info('MessageChat', `Acceptation de l'appel de ${this.incomingCall.caller.username}`);\n    this.MessageService.acceptCall(this.incomingCall.id).subscribe({\n      next: call => {\n        this.logger.info('MessageChat', 'Appel accepté avec succès:', call);\n        this.showCallModal = false;\n        // Ici, vous pourriez ouvrir une fenêtre d'appel ou rediriger vers une page d'appel\n      },\n\n      error: error => {\n        this.logger.error('MessageChat', \"Erreur lors de l'acceptation de l'appel:\", error);\n        this.toastService.showError(\"Impossible d'accepter l'appel. Veuillez réessayer.\");\n        this.showCallModal = false;\n        this.incomingCall = null;\n      }\n    });\n  }\n  /**\n   * Rejette un appel entrant\n   */\n  rejectCall() {\n    if (!this.incomingCall) {\n      this.logger.error('MessageChat', 'Aucun appel entrant à rejeter');\n      return;\n    }\n    this.logger.info('MessageChat', `Rejet de l'appel de ${this.incomingCall.caller.username}`);\n    this.MessageService.rejectCall(this.incomingCall.id).subscribe({\n      next: call => {\n        this.logger.info('MessageChat', 'Appel rejeté avec succès:', call);\n        this.showCallModal = false;\n        this.incomingCall = null;\n      },\n      error: error => {\n        this.logger.error('MessageChat', \"Erreur lors du rejet de l'appel:\", error);\n        this.showCallModal = false;\n        this.incomingCall = null;\n      }\n    });\n  }\n  /**\n   * Termine un appel en cours\n   */\n  endCall() {\n    // Utiliser une variable pour stocker la dernière valeur de l'observable\n    let activeCall = null;\n    // S'abonner à l'observable pour obtenir la valeur actuelle\n    const sub = this.MessageService.activeCall$.subscribe(call => {\n      activeCall = call;\n      if (!activeCall) {\n        this.logger.error('MessageChat', 'Aucun appel actif à terminer');\n        return;\n      }\n      this.logger.info('MessageChat', `Fin de l'appel`);\n      this.MessageService.endCall(activeCall.id).subscribe({\n        next: call => {\n          this.logger.info('MessageChat', 'Appel terminé avec succès:', call);\n        },\n        error: error => {\n          this.logger.error('MessageChat', \"Erreur lors de la fin de l'appel:\", error);\n        }\n      });\n    });\n    // Se désabonner immédiatement après avoir obtenu la valeur\n    sub.unsubscribe();\n  }\n  static {\n    this.ɵfac = function MessageChatComponent_Factory(t) {\n      return new (t || MessageChatComponent)(i0.ɵɵdirectiveInject(i1.MessageService), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.AuthuserService), i0.ɵɵdirectiveInject(i4.FormBuilder), i0.ɵɵdirectiveInject(i5.UserStatusService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i6.ToastService), i0.ɵɵdirectiveInject(i7.LoggerService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: MessageChatComponent,\n      selectors: [[\"app-message-chat\"]],\n      viewQuery: function MessageChatComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.messagesContainer = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fileInput = _t.first);\n        }\n      },\n      decls: 74,\n      vars: 23,\n      consts: [[1, \"flex\", \"flex-col\", \"h-full\", \"bg-[#edf1f4]\", \"dark:bg-[#121212]\", \"relative\", \"overflow-hidden\", \"futuristic-chat-container\", \"dark\", 3, \"ngClass\"], [1, \"absolute\", \"inset-0\", \"overflow-hidden\", \"pointer-events-none\"], [1, \"absolute\", \"inset-0\", \"opacity-5\", \"dark:opacity-[0.03]\"], [1, \"h-full\", \"grid\", \"grid-cols-12\"], [1, \"border-r\", \"border-[#4f5fad]\", \"dark:border-[#6d78c9]\"], [1, \"w-full\", \"grid\", \"grid-rows-12\"], [1, \"border-b\", \"border-[#4f5fad]\", \"dark:border-[#6d78c9]\"], [1, \"absolute\", \"inset-0\", \"opacity-0\", \"dark:opacity-100\", \"overflow-hidden\"], [1, \"h-px\", \"w-full\", \"bg-[#00f7ff]/20\", \"absolute\", \"animate-scan\"], [1, \"whatsapp-chat-header\"], [1, \"whatsapp-action-button\", 3, \"click\"], [1, \"fas\", \"fa-arrow-left\"], [1, \"whatsapp-user-info\"], [1, \"whatsapp-avatar\"], [\"alt\", \"User avatar\", 3, \"src\"], [\"class\", \"whatsapp-online-indicator\", 4, \"ngIf\"], [\"class\", \"whatsapp-user-details\", 4, \"ngIf\"], [1, \"whatsapp-actions\"], [1, \"fas\", \"fa-phone-alt\"], [1, \"fas\", \"fa-video\"], [1, \"relative\"], [1, \"fas\", \"fa-palette\"], [\"class\", \"absolute right-0 mt-2 w-48 bg-white dark:bg-[#1e1e1e] rounded-lg shadow-lg z-50 border border-[#edf1f4]/50 dark:border-[#2a2a2a] overflow-hidden\", 4, \"ngIf\"], [1, \"whatsapp-action-button\"], [1, \"fas\", \"fa-ellipsis-v\"], [1, \"futuristic-messages-container\", 3, \"scroll\"], [\"messagesContainer\", \"\"], [\"class\", \"flex justify-center items-center h-full\", 4, \"ngIf\"], [\"class\", \"flex justify-center py-2 sticky top-0 z-10\", 4, \"ngIf\"], [\"class\", \"flex justify-center py-2 mb-2\", 4, \"ngIf\"], [\"class\", \"bg-[#ff6b69]/10 dark:bg-[#ff6b69]/5 border border-[#ff6b69] dark:border-[#ff6b69]/30 rounded-lg p-4 mx-auto max-w-md my-4 backdrop-blur-sm\", 4, \"ngIf\"], [4, \"ngIf\", \"ngIfElse\"], [\"noMessages\", \"\"], [\"class\", \"futuristic-typing-indicator\", 4, \"ngIf\"], [1, \"whatsapp-input-container\"], [\"class\", \"whatsapp-file-preview\", 4, \"ngIf\"], [\"class\", \"whatsapp-emoji-picker\", 4, \"ngIf\"], [1, \"whatsapp-input-form\", 3, \"formGroup\", \"ngSubmit\"], [1, \"whatsapp-input-tools\"], [\"type\", \"button\", 1, \"whatsapp-tool-button\", 3, \"ngClass\", \"click\"], [1, \"far\", \"fa-smile\"], [\"type\", \"button\", 1, \"whatsapp-tool-button\", 3, \"click\"], [1, \"fas\", \"fa-paperclip\"], [\"type\", \"file\", \"accept\", \"image/*\", 1, \"hidden\", 3, \"change\"], [\"fileInput\", \"\"], [3, \"maxDuration\", \"recordingComplete\", \"recordingCancelled\", 4, \"ngIf\"], [\"formControlName\", \"content\", \"type\", \"text\", \"placeholder\", \"Message\", \"class\", \"whatsapp-input-field\", 3, \"input\", 4, \"ngIf\"], [\"type\", \"button\", \"class\", \"whatsapp-voice-button\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"submit\", \"class\", \"whatsapp-send-button\", 3, \"disabled\", 4, \"ngIf\"], [\"class\", \"whatsapp-call-modal\", 4, \"ngIf\"], [1, \"whatsapp-online-indicator\"], [1, \"whatsapp-user-details\"], [1, \"whatsapp-username\"], [1, \"whatsapp-status\"], [1, \"absolute\", \"right-0\", \"mt-2\", \"w-48\", \"bg-white\", \"dark:bg-[#1e1e1e]\", \"rounded-lg\", \"shadow-lg\", \"z-50\", \"border\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\", \"overflow-hidden\"], [1, \"p-2\", \"text-xs\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"border-b\", \"border-[#edf1f4]/50\", \"dark:border-[#2a2a2a]\"], [1, \"p-1\"], [\"href\", \"javascript:void(0)\", 1, \"block\", \"w-full\", \"text-left\", \"px-3\", \"py-2\", \"text-sm\", \"rounded-md\", \"hover:bg-[#4f5fad]/10\", \"dark:hover:bg-[#6d78c9]/10\", \"transition-colors\", 3, \"click\"], [1, \"flex\", \"items-center\"], [1, \"w-4\", \"h-4\", \"rounded-full\", \"bg-[#4f5fad]\", \"mr-2\"], [\"href\", \"javascript:void(0)\", 1, \"block\", \"w-full\", \"text-left\", \"px-3\", \"py-2\", \"text-sm\", \"rounded-md\", \"hover:bg-[#ff6b9d]/10\", \"dark:hover:bg-[#ff6b9d]/10\", \"transition-colors\", 3, \"click\"], [1, \"w-4\", \"h-4\", \"rounded-full\", \"bg-[#ff6b9d]\", \"mr-2\"], [\"href\", \"javascript:void(0)\", 1, \"block\", \"w-full\", \"text-left\", \"px-3\", \"py-2\", \"text-sm\", \"rounded-md\", \"hover:bg-[#3d85c6]/10\", \"dark:hover:bg-[#3d85c6]/10\", \"transition-colors\", 3, \"click\"], [1, \"w-4\", \"h-4\", \"rounded-full\", \"bg-[#3d85c6]\", \"mr-2\"], [\"href\", \"javascript:void(0)\", 1, \"block\", \"w-full\", \"text-left\", \"px-3\", \"py-2\", \"text-sm\", \"rounded-md\", \"hover:bg-[#6aa84f]/10\", \"dark:hover:bg-[#6aa84f]/10\", \"transition-colors\", 3, \"click\"], [1, \"w-4\", \"h-4\", \"rounded-full\", \"bg-[#6aa84f]\", \"mr-2\"], [1, \"flex\", \"justify-center\", \"items-center\", \"h-full\"], [1, \"flex\", \"flex-col\", \"items-center\"], [1, \"w-12\", \"h-12\", \"border-4\", \"border-[#4f5fad]/20\", \"dark:border-[#6d78c9]/20\", \"border-t-[#4f5fad]\", \"dark:border-t-[#6d78c9]\", \"rounded-full\", \"animate-spin\", \"mb-3\"], [1, \"absolute\", \"inset-0\", \"bg-[#4f5fad]/20\", \"dark:bg-[#6d78c9]/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\", \"mt-3\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\", \"font-medium\"], [1, \"flex\", \"justify-center\", \"py-2\", \"sticky\", \"top-0\", \"z-10\"], [1, \"flex\", \"flex-col\", \"items-center\", \"backdrop-blur-sm\", \"bg-white/50\", \"dark:bg-[#1e1e1e]/50\", \"px-4\", \"py-2\", \"rounded-full\", \"shadow-sm\"], [1, \"flex\", \"space-x-2\", \"mb-1\"], [1, \"w-2\", \"h-2\", \"bg-[#4f5fad]\", \"dark:bg-[#6d78c9]\", \"rounded-full\", \"animate-pulse\", \"shadow-[0_0_5px_rgba(79,95,173,0.5)]\", \"dark:shadow-[0_0_5px_rgba(109,120,201,0.5)]\"], [1, \"w-2\", \"h-2\", \"bg-[#4f5fad]\", \"dark:bg-[#6d78c9]\", \"rounded-full\", \"animate-pulse\", \"shadow-[0_0_5px_rgba(79,95,173,0.5)]\", \"dark:shadow-[0_0_5px_rgba(109,120,201,0.5)]\", 2, \"animation-delay\", \"0.2s\"], [1, \"w-2\", \"h-2\", \"bg-[#4f5fad]\", \"dark:bg-[#6d78c9]\", \"rounded-full\", \"animate-pulse\", \"shadow-[0_0_5px_rgba(79,95,173,0.5)]\", \"dark:shadow-[0_0_5px_rgba(109,120,201,0.5)]\", 2, \"animation-delay\", \"0.4s\"], [1, \"text-xs\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [1, \"flex\", \"justify-center\", \"py-2\", \"mb-2\"], [1, \"flex\", \"items-center\", \"w-full\", \"max-w-xs\"], [1, \"flex-1\", \"h-px\", \"bg-gradient-to-r\", \"from-transparent\", \"via-[#4f5fad]/20\", \"dark:via-[#6d78c9]/20\", \"to-transparent\"], [1, \"px-3\", \"text-xs\", \"bg-gradient-to-r\", \"from-[#3d4a85]\", \"to-[#4f5fad]\", \"dark:from-[#6d78c9]\", \"dark:to-[#4f5fad]\", \"bg-clip-text\", \"text-transparent\", \"font-medium\"], [1, \"bg-[#ff6b69]/10\", \"dark:bg-[#ff6b69]/5\", \"border\", \"border-[#ff6b69]\", \"dark:border-[#ff6b69]/30\", \"rounded-lg\", \"p-4\", \"mx-auto\", \"max-w-md\", \"my-4\", \"backdrop-blur-sm\"], [1, \"flex\", \"items-start\"], [1, \"text-[#ff6b69]\", \"dark:text-[#ff8785]\", \"mr-3\", \"text-xl\", \"relative\"], [1, \"fas\", \"fa-exclamation-triangle\"], [1, \"absolute\", \"inset-0\", \"bg-[#ff6b69]/20\", \"dark:bg-[#ff8785]/20\", \"blur-xl\", \"rounded-full\", \"transform\", \"scale-150\", \"-z-10\"], [1, \"font-medium\", \"text-[#ff6b69]\", \"dark:text-[#ff8785]\", \"mb-1\"], [1, \"text-sm\", \"text-[#6d6870]\", \"dark:text-[#a0a0a0]\"], [\"class\", \"futuristic-message-wrapper\", 4, \"ngFor\", \"ngForOf\"], [1, \"futuristic-message-wrapper\"], [\"class\", \"futuristic-date-separator\", 4, \"ngIf\"], [1, \"futuristic-message\", 3, \"ngClass\"], [\"class\", \"futuristic-avatar\", 4, \"ngIf\"], [1, \"futuristic-message-content\"], [\"class\", \"futuristic-message-bubble\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"futuristic-message-bubble futuristic-voice-message-container\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"futuristic-message-image-container\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"futuristic-date-separator\"], [1, \"futuristic-date-line\"], [1, \"futuristic-date-text\"], [1, \"futuristic-avatar\"], [\"alt\", \"User avatar\", \"onerror\", \"this.src='assets/images/default-avatar.png'\", 3, \"src\"], [1, \"futuristic-message-bubble\", 3, \"ngClass\"], [1, \"futuristic-message-text\"], [1, \"futuristic-message-info\"], [1, \"futuristic-message-time\"], [\"class\", \"futuristic-message-status\", 4, \"ngIf\"], [1, \"futuristic-message-status\"], [\"class\", \"fas fa-check-double\", 4, \"ngIf\"], [\"class\", \"fas fa-check\", 4, \"ngIf\"], [1, \"fas\", \"fa-check-double\"], [1, \"fas\", \"fa-check\"], [1, \"futuristic-message-bubble\", \"futuristic-voice-message-container\", 3, \"ngClass\"], [1, \"futuristic-voice-message\"], [1, \"futuristic-voice-play-button\"], [1, \"fas\", \"fa-play\"], [1, \"futuristic-voice-waveform\"], [\"class\", \"futuristic-voice-bar\", 3, \"height\", 4, \"ngFor\", \"ngForOf\"], [1, \"messenger-style-voice-message\", 2, \"display\", \"none\", 3, \"audioUrl\", \"duration\"], [1, \"futuristic-voice-bar\"], [1, \"futuristic-message-image-container\", 3, \"ngClass\"], [1, \"futuristic-image-wrapper\"], [\"target\", \"_blank\", 1, \"futuristic-message-image-link\", 3, \"href\"], [\"alt\", \"Image\", 1, \"futuristic-message-image\", 3, \"src\"], [1, \"futuristic-image-overlay\"], [1, \"fas\", \"fa-expand\"], [\"class\", \"futuristic-no-messages\", 4, \"ngIf\"], [1, \"futuristic-no-messages\"], [1, \"futuristic-no-messages-icon\"], [1, \"fas\", \"fa-satellite-dish\"], [1, \"futuristic-no-messages-text\"], [1, \"futuristic-start-button\", 3, \"click\"], [1, \"fas\", \"fa-paper-plane\"], [1, \"futuristic-typing-indicator\"], [1, \"futuristic-typing-bubble\"], [1, \"futuristic-typing-dots\"], [1, \"futuristic-typing-dot\"], [1, \"futuristic-typing-dot\", 2, \"animation-delay\", \"0.2s\"], [1, \"futuristic-typing-dot\", 2, \"animation-delay\", \"0.4s\"], [1, \"whatsapp-file-preview\"], [1, \"whatsapp-preview-image\", 3, \"src\"], [1, \"whatsapp-remove-button\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"whatsapp-emoji-picker\"], [1, \"whatsapp-emoji-categories\"], [1, \"whatsapp-emoji-category\", \"active\"], [1, \"whatsapp-emoji-category\"], [1, \"fas\", \"fa-cat\"], [1, \"fas\", \"fa-hamburger\"], [1, \"fas\", \"fa-futbol\"], [1, \"fas\", \"fa-car\"], [1, \"fas\", \"fa-lightbulb\"], [1, \"whatsapp-emoji-list\"], [\"class\", \"whatsapp-emoji-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"whatsapp-emoji-item\", 3, \"click\"], [3, \"maxDuration\", \"recordingComplete\", \"recordingCancelled\"], [\"formControlName\", \"content\", \"type\", \"text\", \"placeholder\", \"Message\", 1, \"whatsapp-input-field\", 3, \"input\"], [\"type\", \"button\", 1, \"whatsapp-voice-button\", 3, \"click\"], [1, \"fas\", \"fa-microphone\"], [\"type\", \"submit\", 1, \"whatsapp-send-button\", 3, \"disabled\"], [\"class\", \"fas fa-paper-plane\", 4, \"ngIf\"], [\"class\", \"fas fa-spinner fa-spin\", 4, \"ngIf\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\"], [1, \"whatsapp-call-modal\"], [1, \"whatsapp-call-modal-content\"], [1, \"whatsapp-call-header\"], [1, \"whatsapp-call-avatar\"], [\"alt\", \"Caller avatar\", 3, \"src\"], [1, \"whatsapp-call-name\"], [1, \"whatsapp-call-status\"], [1, \"whatsapp-call-actions\"], [1, \"whatsapp-call-reject\", 3, \"click\"], [1, \"fas\", \"fa-phone-slash\"], [1, \"whatsapp-call-accept\", 3, \"click\"], [1, \"fas\", \"fa-phone\"]],\n      template: function MessageChatComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r73 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵelement(4, \"div\", 4)(5, \"div\", 4)(6, \"div\", 4)(7, \"div\", 4)(8, \"div\", 4)(9, \"div\", 4)(10, \"div\", 4)(11, \"div\", 4)(12, \"div\", 4)(13, \"div\", 4)(14, \"div\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"div\", 5);\n          i0.ɵɵelement(16, \"div\", 6)(17, \"div\", 6)(18, \"div\", 6)(19, \"div\", 6)(20, \"div\", 6)(21, \"div\", 6)(22, \"div\", 6)(23, \"div\", 6)(24, \"div\", 6)(25, \"div\", 6)(26, \"div\", 6);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(27, \"div\", 7);\n          i0.ɵɵelement(28, \"div\", 8);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(29, \"div\", 9)(30, \"button\", 10);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_30_listener() {\n            return ctx.goBackToConversations();\n          });\n          i0.ɵɵelement(31, \"i\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"div\", 12)(33, \"div\", 13);\n          i0.ɵɵelement(34, \"img\", 14);\n          i0.ɵɵtemplate(35, MessageChatComponent_span_35_Template, 1, 0, \"span\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(36, MessageChatComponent_div_36_Template, 5, 2, \"div\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"div\", 17)(38, \"button\", 10);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_38_listener() {\n            return ctx.initiateCall(\"AUDIO\");\n          });\n          i0.ɵɵelement(39, \"i\", 18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"button\", 10);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_40_listener() {\n            return ctx.initiateCall(\"VIDEO\");\n          });\n          i0.ɵɵelement(41, \"i\", 19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"div\", 20)(43, \"button\", 10);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_43_listener() {\n            return ctx.toggleThemeSelector();\n          });\n          i0.ɵɵelement(44, \"i\", 21);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(45, MessageChatComponent_div_45_Template, 24, 0, \"div\", 22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"button\", 23);\n          i0.ɵɵelement(47, \"i\", 24);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(48, \"div\", 25, 26);\n          i0.ɵɵlistener(\"scroll\", function MessageChatComponent_Template_div_scroll_48_listener($event) {\n            return ctx.onScroll($event);\n          });\n          i0.ɵɵtemplate(50, MessageChatComponent_div_50_Template, 7, 0, \"div\", 27);\n          i0.ɵɵtemplate(51, MessageChatComponent_div_51_Template, 8, 0, \"div\", 28);\n          i0.ɵɵtemplate(52, MessageChatComponent_div_52_Template, 6, 0, \"div\", 29);\n          i0.ɵɵtemplate(53, MessageChatComponent_div_53_Template, 10, 1, \"div\", 30);\n          i0.ɵɵtemplate(54, MessageChatComponent_ng_container_54_Template, 2, 1, \"ng-container\", 31);\n          i0.ɵɵtemplate(55, MessageChatComponent_ng_template_55_Template, 1, 1, \"ng-template\", null, 32, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵtemplate(57, MessageChatComponent_div_57_Template, 8, 1, \"div\", 33);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(58, \"div\", 34);\n          i0.ɵɵtemplate(59, MessageChatComponent_div_59_Template, 4, 1, \"div\", 35);\n          i0.ɵɵtemplate(60, MessageChatComponent_div_60_Template, 16, 1, \"div\", 36);\n          i0.ɵɵelementStart(61, \"form\", 37);\n          i0.ɵɵlistener(\"ngSubmit\", function MessageChatComponent_Template_form_ngSubmit_61_listener() {\n            return ctx.sendMessage();\n          });\n          i0.ɵɵelementStart(62, \"div\", 38)(63, \"button\", 39);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_63_listener() {\n            return ctx.toggleEmojiPicker();\n          });\n          i0.ɵɵelement(64, \"i\", 40);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(65, \"button\", 41);\n          i0.ɵɵlistener(\"click\", function MessageChatComponent_Template_button_click_65_listener() {\n            i0.ɵɵrestoreView(_r73);\n            const _r14 = i0.ɵɵreference(68);\n            return i0.ɵɵresetView(_r14.click());\n          });\n          i0.ɵɵelement(66, \"i\", 42);\n          i0.ɵɵelementStart(67, \"input\", 43, 44);\n          i0.ɵɵlistener(\"change\", function MessageChatComponent_Template_input_change_67_listener($event) {\n            return ctx.onFileSelected($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(69, MessageChatComponent_app_voice_recorder_69_Template, 1, 1, \"app-voice-recorder\", 45);\n          i0.ɵɵtemplate(70, MessageChatComponent_input_70_Template, 1, 0, \"input\", 46);\n          i0.ɵɵtemplate(71, MessageChatComponent_button_71_Template, 2, 0, \"button\", 47);\n          i0.ɵɵtemplate(72, MessageChatComponent_button_72_Template, 3, 3, \"button\", 48);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(73, MessageChatComponent_div_73_Template, 18, 3, \"div\", 49);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          const _r9 = i0.ɵɵreference(56);\n          let tmp_18_0;\n          let tmp_19_0;\n          i0.ɵɵproperty(\"ngClass\", ctx.selectedTheme);\n          i0.ɵɵadvance(34);\n          i0.ɵɵproperty(\"src\", (ctx.otherParticipant == null ? null : ctx.otherParticipant.image) || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.otherParticipant == null ? null : ctx.otherParticipant.isOnline);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.otherParticipant);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngIf\", ctx.showThemeSelector);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoadingMore);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.hasMoreMessages && ctx.messages.length > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.messages && ctx.messages.length > 0)(\"ngIfElse\", _r9);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.isTyping);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.previewUrl);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showEmojiPicker);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"formGroup\", ctx.messageForm);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(21, _c5, ctx.showEmojiPicker));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.isRecordingVoice);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isRecordingVoice);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isRecordingVoice && ((tmp_18_0 = ctx.messageForm.get(\"content\")) == null ? null : tmp_18_0.value) === \"\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isRecordingVoice && ((tmp_19_0 = ctx.messageForm.get(\"content\")) == null ? null : tmp_19_0.value) !== \"\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showCallModal && ctx.incomingCall);\n        }\n      },\n      dependencies: [i8.NgClass, i8.NgForOf, i8.NgIf, i4.ɵNgNoValidate, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgControlStatusGroup, i4.FormGroupDirective, i4.FormControlName, i9.VoiceRecorderComponent, i10.VoiceMessagePlayerComponent],\n      styles: [\"\\n\\n\\n\\n\\n.futuristic-message-current-user[_ngcontent-%COMP%]   .futuristic-message-bubble[_ngcontent-%COMP%] {\\n  background: linear-gradient(\\n    135deg,\\n    rgba(0, 247, 255, 0.9),\\n    rgba(131, 56, 236, 0.9)\\n  );\\n  color: white;\\n  border: 1px solid rgba(255, 255, 255, 0.3);\\n  box-shadow: 0 4px 20px rgba(0, 247, 255, 0.4);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  transform: perspective(800px) rotateX(0deg);\\n  transition: all 0.3s ease;\\n}\\n\\n.futuristic-message-current-user[_ngcontent-%COMP%]   .futuristic-message-bubble[_ngcontent-%COMP%]:hover {\\n  transform: perspective(800px) rotateX(2deg) translateY(-2px);\\n  box-shadow: 0 6px 25px rgba(0, 247, 255, 0.5);\\n}\\n\\n.futuristic-message-current-user[_ngcontent-%COMP%]   .futuristic-message-bubble[_ngcontent-%COMP%]::before {\\n  border-left: 8px solid rgba(131, 56, 236, 0.8);\\n}\\n\\n.futuristic-message-other-user[_ngcontent-%COMP%]   .futuristic-message-bubble[_ngcontent-%COMP%] {\\n  background: rgba(30, 30, 40, 0.7);\\n  border-color: rgba(0, 247, 255, 0.15);\\n}\\n\\n.futuristic-message-other-user[_ngcontent-%COMP%]   .futuristic-message-bubble[_ngcontent-%COMP%]:hover {\\n  border-color: rgba(0, 247, 255, 0.25);\\n  box-shadow: 0 2px 15px rgba(0, 247, 255, 0.2);\\n}\\n\\n\\n\\n.whatsapp-chat-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 6px 10px;\\n  background-color: #f0f2f5;\\n  border-bottom: 1px solid #e0e0e0;\\n  height: 50px; \\n\\n}\\n\\n.dark[_nghost-%COMP%]   .whatsapp-chat-header[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .whatsapp-chat-header[_ngcontent-%COMP%] {\\n  background-color: #2a2a2a;\\n  border-bottom: 1px solid #3a3a3a;\\n}\\n\\n.whatsapp-user-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  flex: 1;\\n  margin-left: 12px;\\n}\\n\\n.whatsapp-avatar[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 36px;\\n  height: 36px;\\n  border-radius: 50%;\\n  overflow: hidden;\\n  margin-right: 8px;\\n}\\n\\n.whatsapp-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n}\\n\\n.whatsapp-online-indicator[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 2px;\\n  right: 2px;\\n  width: 8px;\\n  height: 8px;\\n  border-radius: 50%;\\n  background: #25d366;\\n  border: 2px solid #f0f2f5;\\n}\\n\\n.dark[_nghost-%COMP%]   .whatsapp-online-indicator[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .whatsapp-online-indicator[_ngcontent-%COMP%] {\\n  border-color: #2a2a2a;\\n}\\n\\n.whatsapp-user-details[_ngcontent-%COMP%] {\\n  margin-left: 8px;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.whatsapp-username[_ngcontent-%COMP%] {\\n  font-size: 0.9375rem;\\n  font-weight: 600;\\n  color: #333;\\n}\\n\\n.dark[_nghost-%COMP%]   .whatsapp-username[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .whatsapp-username[_ngcontent-%COMP%] {\\n  color: #e0e0e0;\\n}\\n\\n.whatsapp-status[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: #666;\\n}\\n\\n.dark[_nghost-%COMP%]   .whatsapp-status[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .whatsapp-status[_ngcontent-%COMP%] {\\n  color: #aaa;\\n}\\n\\n.whatsapp-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n}\\n\\n.whatsapp-action-button[_ngcontent-%COMP%] {\\n  background: transparent;\\n  border: none;\\n  color: #54656f;\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: background-color 0.2s;\\n}\\n\\n.dark[_nghost-%COMP%]   .whatsapp-action-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .whatsapp-action-button[_ngcontent-%COMP%] {\\n  color: #aaa;\\n}\\n\\n.whatsapp-action-button[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(0, 0, 0, 0.05);\\n}\\n\\n.dark[_nghost-%COMP%]   .whatsapp-action-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .whatsapp-action-button[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(255, 255, 255, 0.1);\\n}\\n\\n\\n\\n.futuristic-messages-container[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 0.75rem;\\n  background-color: #f0f4f8; \\n\\n  overflow-y: auto;\\n  scroll-behavior: smooth;\\n  -webkit-overflow-scrolling: touch;\\n  scrollbar-width: thin;\\n  scrollbar-color: var(--accent-color) transparent;\\n  position: relative;\\n  max-height: calc(\\n    100vh - 180px\\n  ); \\n\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-messages-container[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-messages-container[_ngcontent-%COMP%] {\\n  background-color: var(--dark-bg);\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_scan {\\n  0% {\\n    top: 0;\\n  }\\n  100% {\\n    top: 100%;\\n  }\\n}\\n\\n\\n\\n.futuristic-messages-container[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 5px;\\n}\\n\\n.futuristic-messages-container[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: rgba(0, 247, 255, 0.05);\\n}\\n\\n.futuristic-messages-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background-color: var(--accent-color);\\n  border-radius: 10px;\\n  border: transparent;\\n}\\n\\n\\n\\n.futuristic-message-wrapper[_ngcontent-%COMP%] {\\n  margin-bottom: 4px;\\n  position: relative;\\n  z-index: 1;\\n}\\n\\n\\n\\n.futuristic-message[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-end;\\n  margin-bottom: 1px;\\n  position: relative;\\n  width: 100%;\\n}\\n\\n\\n\\n.futuristic-message-bubble[_ngcontent-%COMP%] {\\n  margin-bottom: 0.25rem;\\n  max-width: 70%;\\n  min-width: 40px;\\n  padding: 0.6rem 0.8rem;\\n  font-size: 0.9rem;\\n  line-height: 1.4;\\n  word-wrap: break-word;\\n  word-break: normal;\\n  display: inline-block;\\n  width: auto;\\n  white-space: normal;\\n  overflow-wrap: break-word;\\n  text-align: left;\\n  direction: ltr;\\n  position: relative;\\n  transition: all var(--transition-fast);\\n  animation: _ngcontent-%COMP%_fadeIn 0.3s ease-out;\\n  border-radius: 12px;\\n  letter-spacing: 0.02em;\\n  font-weight: 400;\\n  -webkit-backdrop-filter: blur(5px);\\n          backdrop-filter: blur(5px);\\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);\\n}\\n\\n\\n\\n.futuristic-message-content[_ngcontent-%COMP%] {\\n  max-width: 80%;\\n}\\n\\n.futuristic-message-text[_ngcontent-%COMP%] {\\n  white-space: normal;\\n  word-break: break-word;\\n  overflow-wrap: break-word;\\n  text-align: left;\\n  direction: ltr;\\n  min-width: 80px;\\n  font-weight: 400;\\n  letter-spacing: 0.01em;\\n  line-height: 1.5;\\n}\\n\\n.futuristic-message-current-user[_ngcontent-%COMP%]   .futuristic-message-text[_ngcontent-%COMP%] {\\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\\n}\\n\\n.futuristic-message-other-user[_ngcontent-%COMP%]   .futuristic-message-text[_ngcontent-%COMP%] {\\n  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);\\n}\\n\\n\\n\\n.futuristic-date-separator[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin: 1.5rem 0;\\n  color: var(--text-dim);\\n  font-size: 0.75rem;\\n  text-transform: uppercase;\\n  letter-spacing: 1px;\\n}\\n\\n.futuristic-date-line[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 1px;\\n  background: linear-gradient(\\n    to right,\\n    transparent,\\n    var(--accent-color),\\n    transparent\\n  );\\n  opacity: 0.3;\\n}\\n\\n.futuristic-date-text[_ngcontent-%COMP%] {\\n  margin: 0 10px;\\n  padding: 2px 8px;\\n  background-color: rgba(0, 247, 255, 0.05);\\n  border-radius: var(--border-radius-sm);\\n}\\n\\n\\n\\n.futuristic-message-time[_ngcontent-%COMP%] {\\n  font-size: 0.7rem;\\n  margin-top: 0.2rem;\\n  opacity: 0.7;\\n  color: var(--text-dim);\\n}\\n\\n\\n\\n.futuristic-message-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: flex-end;\\n  gap: 6px;\\n  margin-top: 4px;\\n  font-size: 0.75rem;\\n  letter-spacing: 0.02em;\\n  font-weight: 300;\\n}\\n\\n.futuristic-message-current-user[_ngcontent-%COMP%]   .futuristic-message-info[_ngcontent-%COMP%] {\\n  color: rgba(255, 255, 255, 0.8);\\n}\\n\\n.futuristic-message-other-user[_ngcontent-%COMP%]   .futuristic-message-info[_ngcontent-%COMP%] {\\n  color: rgba(0, 247, 255, 0.7);\\n}\\n\\n.futuristic-message-status[_ngcontent-%COMP%] {\\n  color: rgba(0, 247, 255, 0.9);\\n}\\n\\n\\n\\n.futuristic-message-current-user[_ngcontent-%COMP%] {\\n  justify-content: flex-end;\\n  width: 100%;\\n  display: flex;\\n  margin-left: auto;\\n  animation: _ngcontent-%COMP%_slideInRight 0.3s ease-out;\\n}\\n\\n.futuristic-message-current-user[_ngcontent-%COMP%]   .futuristic-message-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: flex-end;\\n  max-width: 80%;\\n  margin-left: auto;\\n}\\n\\n.futuristic-message-current-user[_ngcontent-%COMP%]   .futuristic-message-bubble[_ngcontent-%COMP%] {\\n  background: linear-gradient(\\n    135deg,\\n    rgba(0, 247, 255, 0.8),\\n    rgba(131, 56, 236, 0.8)\\n  );\\n  color: white;\\n  border-radius: 12px;\\n  margin-left: auto;\\n  box-shadow: 0 2px 15px rgba(0, 247, 255, 0.3);\\n  position: relative;\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);\\n}\\n\\n\\n\\n.futuristic-message-current-user[_ngcontent-%COMP%]   .futuristic-message-bubble[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  right: -8px;\\n  width: 0;\\n  height: 0;\\n  border-top: 8px solid transparent;\\n  border-left: 8px solid rgba(131, 56, 236, 0.8);\\n  border-bottom: 8px solid transparent;\\n  z-index: 1;\\n  filter: drop-shadow(2px 0px 2px rgba(0, 0, 0, 0.1));\\n}\\n\\n.futuristic-message-current-user[_ngcontent-%COMP%]   .futuristic-message-bubble[_ngcontent-%COMP%]:hover {\\n  box-shadow: var(--glow-effect);\\n}\\n\\n\\n\\n.futuristic-message-other-user[_ngcontent-%COMP%] {\\n  justify-content: flex-start;\\n  width: 100%;\\n  display: flex;\\n  margin-right: auto;\\n  animation: _ngcontent-%COMP%_slideInLeft 0.3s ease-out;\\n}\\n\\n.futuristic-message-other-user[_ngcontent-%COMP%]   .futuristic-message-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: flex-start;\\n  max-width: 80%;\\n  margin-right: auto;\\n}\\n\\n\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-message-other-user[_ngcontent-%COMP%]   .futuristic-message-bubble[_ngcontent-%COMP%], :not(.dark)   [_nghost-%COMP%]   .futuristic-message-other-user[_ngcontent-%COMP%]   .futuristic-message-bubble[_ngcontent-%COMP%] {\\n  background: rgba(220, 225, 235, 0.8);\\n  color: #333;\\n  border-radius: 12px;\\n  margin-right: auto;\\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);\\n  border: 1px solid rgba(79, 95, 173, 0.2);\\n  position: relative;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  transform: perspective(800px) rotateX(0deg);\\n  transition: all 0.3s ease;\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-message-other-user[_ngcontent-%COMP%]   .futuristic-message-bubble[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .futuristic-message-other-user[_ngcontent-%COMP%]   .futuristic-message-bubble[_ngcontent-%COMP%] {\\n  background: rgba(30, 30, 40, 0.8);\\n  color: rgba(255, 255, 255, 0.95);\\n  border-radius: 12px;\\n  margin-right: auto;\\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.25);\\n  border: 1px solid rgba(0, 247, 255, 0.2);\\n  position: relative;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  transform: perspective(800px) rotateX(0deg);\\n  transition: all 0.3s ease;\\n}\\n\\n\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-message-other-user[_ngcontent-%COMP%]   .futuristic-message-bubble[_ngcontent-%COMP%]::before, :not(.dark)   [_nghost-%COMP%]   .futuristic-message-other-user[_ngcontent-%COMP%]   .futuristic-message-bubble[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: -8px;\\n  width: 0;\\n  height: 0;\\n  border-top: 8px solid transparent;\\n  border-right: 8px solid rgba(220, 225, 235, 0.8);\\n  border-bottom: 8px solid transparent;\\n  z-index: 1;\\n  filter: drop-shadow(-2px 0px 2px rgba(0, 0, 0, 0.05));\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-message-other-user[_ngcontent-%COMP%]   .futuristic-message-bubble[_ngcontent-%COMP%]::before, .dark   [_nghost-%COMP%]   .futuristic-message-other-user[_ngcontent-%COMP%]   .futuristic-message-bubble[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: -8px;\\n  width: 0;\\n  height: 0;\\n  border-top: 8px solid transparent;\\n  border-right: 8px solid rgba(30, 30, 40, 0.7);\\n  border-bottom: 8px solid transparent;\\n  z-index: 1;\\n  filter: drop-shadow(-2px 0px 2px rgba(0, 0, 0, 0.1));\\n}\\n\\n\\n\\n:not(.dark)[_nghost-%COMP%]   .futuristic-message-other-user[_ngcontent-%COMP%]   .futuristic-message-bubble[_ngcontent-%COMP%]:hover, :not(.dark)   [_nghost-%COMP%]   .futuristic-message-other-user[_ngcontent-%COMP%]   .futuristic-message-bubble[_ngcontent-%COMP%]:hover {\\n  background: rgba(230, 235, 245, 0.9);\\n  box-shadow: 0 6px 20px rgba(79, 95, 173, 0.2);\\n  border-color: rgba(79, 95, 173, 0.35);\\n  transform: perspective(800px) rotateX(2deg) translateY(-2px);\\n}\\n\\n\\n\\n.dark[_nghost-%COMP%]   .futuristic-message-other-user[_ngcontent-%COMP%]   .futuristic-message-bubble[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .futuristic-message-other-user[_ngcontent-%COMP%]   .futuristic-message-bubble[_ngcontent-%COMP%]:hover {\\n  background: rgba(40, 40, 50, 0.9);\\n  box-shadow: 0 6px 20px rgba(0, 247, 255, 0.3);\\n  border-color: rgba(0, 247, 255, 0.35);\\n  transform: perspective(800px) rotateX(2deg) translateY(-2px);\\n}\\n\\n\\n\\n\\n\\n.futuristic-input-container[_ngcontent-%COMP%] {\\n  padding: 6px 10px;\\n  background-color: var(--medium-bg);\\n  min-height: 50px; \\n\\n  position: relative;\\n  z-index: 10;\\n  overflow: hidden;\\n}\\n\\n\\n\\n.futuristic-input-container[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  height: 1px;\\n  background: linear-gradient(\\n    90deg,\\n    transparent 0%,\\n    rgba(0, 247, 255, 0.2) 20%,\\n    rgba(0, 247, 255, 0.8) 50%,\\n    rgba(0, 247, 255, 0.2) 80%,\\n    transparent 100%\\n  );\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_borderFlow 3s infinite linear;\\n  box-shadow: 0 0 15px rgba(0, 247, 255, 0.7);\\n  z-index: 1;\\n}\\n\\n\\n\\n.futuristic-input-container[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: radial-gradient(\\n    ellipse at center,\\n    rgba(0, 247, 255, 0.05) 0%,\\n    transparent 70%\\n  );\\n  opacity: 0;\\n  animation: _ngcontent-%COMP%_ambientGlow 4s infinite alternate;\\n  pointer-events: none;\\n  z-index: -1;\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_borderFlow {\\n  0% {\\n    background-position: 0% 0%;\\n  }\\n  100% {\\n    background-position: 200% 0%;\\n  }\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_ambientGlow {\\n  0% {\\n    opacity: 0;\\n    transform: scale(0.95);\\n  }\\n  100% {\\n    opacity: 0.5;\\n    transform: scale(1.05);\\n  }\\n}\\n\\n.futuristic-input-form[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  position: relative;\\n  z-index: 2;\\n}\\n\\n.futuristic-input-tools[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n\\n.futuristic-tool-button[_ngcontent-%COMP%] {\\n  background-color: rgba(0, 247, 255, 0.1);\\n  color: var(--accent-color);\\n  border: none;\\n  border-radius: 50%;\\n  width: 32px;\\n  height: 32px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all var(--transition-fast);\\n}\\n\\n.futuristic-tool-button[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(0, 247, 255, 0.2);\\n  transform: translateY(-2px);\\n  box-shadow: var(--glow-effect);\\n}\\n\\n.futuristic-tool-button.active[_ngcontent-%COMP%] {\\n  background-color: var(--accent-color);\\n  color: var(--dark-bg);\\n  animation: _ngcontent-%COMP%_pulse 1.5s infinite;\\n}\\n\\n\\n\\n.futuristic-input-field[_ngcontent-%COMP%] {\\n  flex: 1;\\n  font-size: 0.9rem;\\n  padding: 10px 16px;\\n  height: 44px;\\n  border-radius: 22px;\\n  border: 1px solid rgba(0, 247, 255, 0.2);\\n  background-color: rgba(0, 247, 255, 0.05);\\n  color: var(--text-light);\\n  transition: all 0.3s ease;\\n}\\n\\n\\n\\n.futuristic-send-button[_ngcontent-%COMP%] {\\n  position: relative;\\n  background: linear-gradient(135deg, #00f7ff, #0066ff);\\n  color: white;\\n  border: none;\\n  border-radius: 50%;\\n  width: 44px;\\n  height: 44px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);\\n  box-shadow: 0 0 10px rgba(0, 247, 255, 0.5), 0 0 20px rgba(0, 247, 255, 0.2),\\n    inset 0 0 5px rgba(255, 255, 255, 0.3);\\n  overflow: hidden;\\n  z-index: 2;\\n}\\n\\n\\n\\n.futuristic-send-button[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: -2px;\\n  left: -2px;\\n  right: -2px;\\n  bottom: -2px;\\n  background: conic-gradient(\\n    from 0deg,\\n    transparent 0%,\\n    rgba(0, 247, 255, 0.8) 25%,\\n    rgba(131, 56, 236, 0.8) 50%,\\n    rgba(0, 247, 255, 0.8) 75%,\\n    transparent 100%\\n  );\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_rotateHalo 3s linear infinite;\\n  z-index: -1;\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n\\n\\n\\n.futuristic-send-button[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: radial-gradient(\\n    circle at center,\\n    rgba(255, 255, 255, 0.9) 0%,\\n    rgba(0, 247, 255, 0.5) 30%,\\n    transparent 70%\\n  );\\n  border-radius: 50%;\\n  opacity: 0;\\n  transform: scale(0.5);\\n  transition: all 0.3s ease;\\n  z-index: -1;\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_rotateHalo {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_pulseButton {\\n  0% {\\n    box-shadow: 0 0 10px rgba(0, 247, 255, 0.5), 0 0 20px rgba(0, 247, 255, 0.2);\\n    transform: scale(1);\\n  }\\n  50% {\\n    box-shadow: 0 0 15px rgba(0, 247, 255, 0.7), 0 0 30px rgba(0, 247, 255, 0.4);\\n    transform: scale(1.05);\\n  }\\n  100% {\\n    box-shadow: 0 0 10px rgba(0, 247, 255, 0.5), 0 0 20px rgba(0, 247, 255, 0.2);\\n    transform: scale(1);\\n  }\\n}\\n\\n.futuristic-send-button[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-3px) scale(1.1);\\n  box-shadow: 0 0 15px rgba(0, 247, 255, 0.7), 0 0 30px rgba(0, 247, 255, 0.4),\\n    inset 0 0 10px rgba(255, 255, 255, 0.5);\\n  animation: _ngcontent-%COMP%_pulseButton 1.5s infinite;\\n}\\n\\n.futuristic-send-button[_ngcontent-%COMP%]:hover::before {\\n  opacity: 1;\\n}\\n\\n.futuristic-send-button[_ngcontent-%COMP%]:hover::after {\\n  opacity: 0.8;\\n  transform: scale(1.2);\\n}\\n\\n.futuristic-send-button[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n  transform: none;\\n  box-shadow: none;\\n  animation: none;\\n}\\n\\n.futuristic-send-button[_ngcontent-%COMP%]:disabled::before, .futuristic-send-button[_ngcontent-%COMP%]:disabled::after {\\n  opacity: 0;\\n}\\n\\n.message-header[_ngcontent-%COMP%] {\\n  padding: 0.25rem 0.5rem;\\n  border-bottom: 1px solid #e9ecef;\\n  height: 40px;\\n}\\n\\n.message-header[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  margin-bottom: 0;\\n}\\n\\n\\n\\n.futuristic-chat-container[_ngcontent-%COMP%] {\\n  width: 100%;\\n  margin: 0 auto;\\n  border: 1px solid rgba(0, 247, 255, 0.2);\\n  border-radius: var(--border-radius-lg);\\n  overflow: hidden;\\n  box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n  background-color: var(--medium-bg);\\n  position: relative;\\n}\\n\\n.message-date-divider[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: #6c757d;\\n  margin: 0.5rem 0;\\n  text-align: center;\\n}\\n\\n\\n\\n.futuristic-message-image-container[_ngcontent-%COMP%] {\\n  margin: 2px 0;\\n  overflow: hidden;\\n  background-color: transparent !important;\\n  border: none !important;\\n  box-shadow: none !important;\\n  padding: 0 !important;\\n  position: relative;\\n  max-width: 220px;\\n  transform: perspective(800px) rotateX(0deg);\\n  transition: all 0.3s ease;\\n}\\n\\n.futuristic-message-image-container[_ngcontent-%COMP%]:hover {\\n  transform: perspective(800px) rotateX(2deg) translateY(-3px);\\n}\\n\\n.futuristic-image-wrapper[_ngcontent-%COMP%] {\\n  position: relative;\\n  overflow: hidden;\\n  border-radius: 12px;\\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);\\n  transition: all var(--transition-fast);\\n  border: 1px solid rgba(255, 255, 255, 0.1);\\n  -webkit-backdrop-filter: blur(5px);\\n          backdrop-filter: blur(5px);\\n  transform: perspective(800px) rotateX(0deg);\\n}\\n\\n.futuristic-message-current-user[_ngcontent-%COMP%]   .futuristic-image-wrapper[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n\\n.futuristic-message-current-user[_ngcontent-%COMP%]   .futuristic-image-wrapper[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  right: -8px;\\n  width: 0;\\n  height: 0;\\n  border-top: 8px solid transparent;\\n  border-left: 8px solid var(--secondary-color);\\n  border-bottom: 8px solid transparent;\\n  z-index: 1;\\n}\\n\\n.futuristic-message-other-user[_ngcontent-%COMP%]   .futuristic-image-wrapper[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n\\n.futuristic-message-other-user[_ngcontent-%COMP%]   .futuristic-image-wrapper[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: -8px;\\n  width: 0;\\n  height: 0;\\n  border-top: 8px solid transparent;\\n  border-right: 8px solid rgba(255, 255, 255, 0.1);\\n  border-bottom: 8px solid transparent;\\n  z-index: 1;\\n}\\n\\n.futuristic-message-image-link[_ngcontent-%COMP%] {\\n  display: block;\\n  width: 100%;\\n  height: 100%;\\n  text-decoration: none;\\n  cursor: pointer;\\n}\\n\\n.futuristic-message-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  display: block;\\n  transition: transform 0.3s ease;\\n  cursor: pointer;\\n}\\n\\n.futuristic-image-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: linear-gradient(to top, rgba(0, 0, 0, 0.5), transparent);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n  color: white;\\n  font-size: 1.5rem;\\n}\\n\\n.futuristic-image-wrapper[_ngcontent-%COMP%]:hover   .futuristic-message-image[_ngcontent-%COMP%] {\\n  transform: scale(1.05);\\n}\\n\\n.futuristic-image-wrapper[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 12px 30px rgba(0, 247, 255, 0.4);\\n  transform: perspective(800px) rotateX(3deg) translateY(-5px);\\n  border-color: rgba(0, 247, 255, 0.4);\\n}\\n\\n.futuristic-image-wrapper[_ngcontent-%COMP%]:hover   .futuristic-image-overlay[_ngcontent-%COMP%] {\\n  opacity: 1;\\n}\\n\\n.futuristic-message-current-user[_ngcontent-%COMP%]   .futuristic-image-wrapper[_ngcontent-%COMP%] {\\n  border: 2px solid transparent;\\n  background: linear-gradient(\\n    135deg,\\n    rgba(0, 247, 255, 0.8),\\n    rgba(131, 56, 236, 0.8)\\n  );\\n  padding: 2px;\\n  box-shadow: 0 5px 20px rgba(0, 247, 255, 0.3);\\n}\\n\\n.futuristic-message-other-user[_ngcontent-%COMP%]   .futuristic-image-wrapper[_ngcontent-%COMP%] {\\n  border: 2px solid rgba(0, 247, 255, 0.15);\\n  background-color: rgba(30, 30, 40, 0.7);\\n  padding: 2px;\\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n    transform: translateY(10px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_scaleIn {\\n  from {\\n    transform: scale(0.9);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: scale(1);\\n    opacity: 1;\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_slideInRight {\\n  from {\\n    opacity: 0;\\n    transform: translateX(20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateX(0);\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_slideInLeft {\\n  from {\\n    opacity: 0;\\n    transform: translateX(-20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateX(0);\\n  }\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n  }\\n  to {\\n    opacity: 1;\\n  }\\n}\\n\\n\\n\\n.fullscreen-image-container[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background-color: rgba(0, 0, 0, 0.95);\\n  z-index: 2147483647; \\n\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  animation: _ngcontent-%COMP%_fadeIn 0.3s ease-in-out;\\n}\\n\\n.fullscreen-image-wrapper[_ngcontent-%COMP%] {\\n  position: relative;\\n  max-width: 90%;\\n  max-height: 90%;\\n}\\n\\n.fullscreen-image-wrapper[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  max-width: 100%;\\n  max-height: 90vh;\\n  object-fit: contain;\\n  border-radius: 8px;\\n  box-shadow: 0 0 30px rgba(0, 247, 255, 0.3);\\n}\\n\\n\\n\\n.whatsapp-input-container[_ngcontent-%COMP%] {\\n  padding: 8px 10px;\\n  background-color: #f0f2f5;\\n  min-height: 50px; \\n\\n  position: relative;\\n  z-index: 10;\\n  border-top: 1px solid #e0e0e0;\\n}\\n\\n.dark[_nghost-%COMP%]   .whatsapp-input-container[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .whatsapp-input-container[_ngcontent-%COMP%] {\\n  background-color: #2a2a2a;\\n  border-top: 1px solid #3a3a3a;\\n}\\n\\n\\n\\n.whatsapp-input-form[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  position: relative;\\n  z-index: 2;\\n}\\n\\n\\n\\n.whatsapp-input-tools[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n  margin-right: 8px;\\n}\\n\\n\\n\\n.whatsapp-tool-button[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  background-color: transparent;\\n  border: none;\\n  color: #54656f;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n\\n.dark[_nghost-%COMP%]   .whatsapp-tool-button[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .whatsapp-tool-button[_ngcontent-%COMP%] {\\n  color: #aaa;\\n}\\n\\n\\n\\n.whatsapp-tool-button[_ngcontent-%COMP%]:hover, .whatsapp-tool-button.active[_ngcontent-%COMP%] {\\n  background-color: rgba(0, 0, 0, 0.05);\\n  color: #128c7e;\\n}\\n\\n.dark[_nghost-%COMP%]   .whatsapp-tool-button[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .whatsapp-tool-button[_ngcontent-%COMP%]:hover, .dark[_nghost-%COMP%]   .whatsapp-tool-button.active[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .whatsapp-tool-button.active[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.1);\\n  color: #25d366;\\n}\\n\\n\\n\\n.whatsapp-input-field[_ngcontent-%COMP%] {\\n  flex: 1;\\n  background-color: white;\\n  border: none;\\n  border-radius: 20px;\\n  padding: 8px 16px;\\n  color: #333;\\n  font-size: 0.9375rem;\\n  transition: all 0.2s ease;\\n  outline: none;\\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\\n}\\n\\n.dark[_nghost-%COMP%]   .whatsapp-input-field[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .whatsapp-input-field[_ngcontent-%COMP%] {\\n  background-color: #3a3a3a;\\n  color: #e0e0e0;\\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\\n}\\n\\n\\n\\n.whatsapp-input-field[_ngcontent-%COMP%]:focus {\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);\\n}\\n\\n\\n\\n.whatsapp-send-button[_ngcontent-%COMP%] {\\n  width: 36px;\\n  height: 36px;\\n  border-radius: 50%;\\n  background-color: #25d366;\\n  border: none;\\n  color: white;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  margin-left: 8px;\\n  transition: all 0.2s ease;\\n}\\n\\n\\n\\n.whatsapp-send-button[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background-color: #128c7e;\\n}\\n\\n\\n\\n.whatsapp-send-button[_ngcontent-%COMP%]:disabled {\\n  background-color: #b3b3b3;\\n  cursor: not-allowed;\\n}\\n\\n\\n\\n.whatsapp-voice-button[_ngcontent-%COMP%] {\\n  width: 36px;\\n  height: 36px;\\n  border-radius: 50%;\\n  background-color: #25d366;\\n  border: none;\\n  color: white;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  margin-left: 8px;\\n  transition: all 0.2s ease;\\n}\\n\\n\\n\\n.whatsapp-voice-button[_ngcontent-%COMP%]:hover {\\n  background-color: #128c7e;\\n}\\n\\n\\n\\n.whatsapp-file-preview[_ngcontent-%COMP%] {\\n  position: relative;\\n  margin-bottom: 8px;\\n  border-radius: 8px;\\n  overflow: hidden;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);\\n  max-width: 200px;\\n  max-height: 200px;\\n}\\n\\n.whatsapp-preview-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n}\\n\\n.whatsapp-remove-button[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 4px;\\n  right: 4px;\\n  width: 24px;\\n  height: 24px;\\n  border-radius: 50%;\\n  background-color: rgba(0, 0, 0, 0.5);\\n  border: none;\\n  color: white;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n\\n.whatsapp-remove-button[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(255, 0, 0, 0.7);\\n}\\n\\n\\n\\n.whatsapp-emoji-picker[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 60px;\\n  left: 0;\\n  right: 0;\\n  background-color: white;\\n  border-radius: 8px 8px 0 0;\\n  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);\\n  z-index: 100;\\n  display: flex;\\n  flex-direction: column;\\n  height: 250px;\\n  overflow: hidden;\\n}\\n\\n.dark[_nghost-%COMP%]   .whatsapp-emoji-picker[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .whatsapp-emoji-picker[_ngcontent-%COMP%] {\\n  background-color: #2a2a2a;\\n  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.3);\\n}\\n\\n.whatsapp-emoji-categories[_ngcontent-%COMP%] {\\n  display: flex;\\n  padding: 8px;\\n  border-bottom: 1px solid #e0e0e0;\\n  overflow-x: auto;\\n  scrollbar-width: none;\\n}\\n\\n.dark[_nghost-%COMP%]   .whatsapp-emoji-categories[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .whatsapp-emoji-categories[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #3a3a3a;\\n}\\n\\n.whatsapp-emoji-categories[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  display: none;\\n}\\n\\n.whatsapp-emoji-category[_ngcontent-%COMP%] {\\n  width: 36px;\\n  height: 36px;\\n  border-radius: 50%;\\n  background-color: transparent;\\n  border: none;\\n  color: #54656f;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  flex-shrink: 0;\\n}\\n\\n.dark[_nghost-%COMP%]   .whatsapp-emoji-category[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .whatsapp-emoji-category[_ngcontent-%COMP%] {\\n  color: #aaa;\\n}\\n\\n.whatsapp-emoji-category[_ngcontent-%COMP%]:hover, .whatsapp-emoji-category.active[_ngcontent-%COMP%] {\\n  background-color: rgba(0, 0, 0, 0.05);\\n  color: #128c7e;\\n}\\n\\n.dark[_nghost-%COMP%]   .whatsapp-emoji-category[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .whatsapp-emoji-category[_ngcontent-%COMP%]:hover, .dark[_nghost-%COMP%]   .whatsapp-emoji-category.active[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .whatsapp-emoji-category.active[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.1);\\n  color: #25d366;\\n}\\n\\n.whatsapp-emoji-list[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 8px;\\n  display: grid;\\n  grid-template-columns: repeat(8, 1fr);\\n  gap: 4px;\\n  overflow-y: auto;\\n}\\n\\n.whatsapp-emoji-item[_ngcontent-%COMP%] {\\n  width: 36px;\\n  height: 36px;\\n  border-radius: 4px;\\n  background-color: transparent;\\n  border: none;\\n  font-size: 20px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n\\n.whatsapp-emoji-item[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(0, 0, 0, 0.05);\\n}\\n\\n.dark[_nghost-%COMP%]   .whatsapp-emoji-item[_ngcontent-%COMP%]:hover, .dark   [_nghost-%COMP%]   .whatsapp-emoji-item[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(255, 255, 255, 0.1);\\n}\\n\\n\\n\\n.whatsapp-call-modal[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background-color: rgba(0, 0, 0, 0.8);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  z-index: 9999;\\n  animation: _ngcontent-%COMP%_fadeIn 0.3s ease-in-out;\\n}\\n\\n.whatsapp-call-modal-content[_ngcontent-%COMP%] {\\n  background-color: #fff;\\n  border-radius: 12px;\\n  width: 300px;\\n  max-width: 90%;\\n  overflow: hidden;\\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);\\n}\\n\\n.dark[_nghost-%COMP%]   .whatsapp-call-modal-content[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .whatsapp-call-modal-content[_ngcontent-%COMP%] {\\n  background-color: #2a2a2a;\\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);\\n}\\n\\n.whatsapp-call-header[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  text-align: center;\\n}\\n\\n.whatsapp-call-avatar[_ngcontent-%COMP%] {\\n  width: 80px;\\n  height: 80px;\\n  border-radius: 50%;\\n  overflow: hidden;\\n  margin: 0 auto 15px;\\n  border: 3px solid #25d366;\\n  animation: _ngcontent-%COMP%_pulse 1.5s infinite;\\n}\\n\\n.whatsapp-call-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n}\\n\\n.whatsapp-call-name[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  font-weight: 600;\\n  margin-bottom: 5px;\\n  color: #333;\\n}\\n\\n.dark[_nghost-%COMP%]   .whatsapp-call-name[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .whatsapp-call-name[_ngcontent-%COMP%] {\\n  color: #e0e0e0;\\n}\\n\\n.whatsapp-call-status[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  color: #666;\\n  margin-bottom: 0;\\n}\\n\\n.dark[_nghost-%COMP%]   .whatsapp-call-status[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .whatsapp-call-status[_ngcontent-%COMP%] {\\n  color: #aaa;\\n}\\n\\n.whatsapp-call-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  border-top: 1px solid #e0e0e0;\\n}\\n\\n.dark[_nghost-%COMP%]   .whatsapp-call-actions[_ngcontent-%COMP%], .dark   [_nghost-%COMP%]   .whatsapp-call-actions[_ngcontent-%COMP%] {\\n  border-top: 1px solid #3a3a3a;\\n}\\n\\n.whatsapp-call-reject[_ngcontent-%COMP%], .whatsapp-call-accept[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 15px;\\n  border: none;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: background-color 0.2s;\\n}\\n\\n.whatsapp-call-reject[_ngcontent-%COMP%] {\\n  background-color: #f44336;\\n  color: white;\\n}\\n\\n.whatsapp-call-accept[_ngcontent-%COMP%] {\\n  background-color: #25d366;\\n  color: white;\\n}\\n\\n.whatsapp-call-reject[_ngcontent-%COMP%]:hover {\\n  background-color: #d32f2f;\\n}\\n\\n.whatsapp-call-accept[_ngcontent-%COMP%]:hover {\\n  background-color: #1da856;\\n}\\n\\n.whatsapp-call-reject[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .whatsapp-call-accept[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  margin-bottom: 5px;\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0% {\\n    box-shadow: 0 0 0 0 rgba(37, 211, 102, 0.5);\\n  }\\n  70% {\\n    box-shadow: 0 0 0 10px rgba(37, 211, 102, 0);\\n  }\\n  100% {\\n    box-shadow: 0 0 0 0 rgba(37, 211, 102, 0);\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n  }\\n  to {\\n    opacity: 1;\\n  }\\n}\\n\\n.close-fullscreen-btn[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -40px;\\n  right: -40px;\\n  width: 40px;\\n  height: 40px;\\n  background-color: rgba(0, 0, 0, 0.7);\\n  color: white;\\n  border: none;\\n  border-radius: 50%;\\n  font-size: 24px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n\\n.close-fullscreen-btn[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(0, 247, 255, 0.5);\\n  transform: scale(1.1);\\n}\\n\\n.image-fullscreen-container[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  position: relative;\\n}\\n\\n.image-fullscreen-container[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  max-width: 85%;\\n  max-height: 85%;\\n  object-fit: contain;\\n  border-radius: 12px;\\n  box-shadow: 0 0 50px rgba(0, 247, 255, 0.3);\\n  animation: _ngcontent-%COMP%_scaleIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);\\n  border: 1px solid rgba(0, 247, 255, 0.2);\\n}\\n\\n.image-fullscreen-dialog[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 20px;\\n  right: 20px;\\n  background: rgba(0, 0, 0, 0.5);\\n  color: rgba(0, 247, 255, 0.9);\\n  border: 2px solid rgba(0, 247, 255, 0.3);\\n  border-radius: 50%;\\n  width: 50px;\\n  height: 50px;\\n  font-size: 30px;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  cursor: pointer;\\n  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);\\n  z-index: 10000000;\\n  box-shadow: 0 0 20px rgba(0, 247, 255, 0.2);\\n}\\n\\n.image-fullscreen-dialog[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]:hover {\\n  background: rgba(0, 247, 255, 0.3);\\n  transform: scale(1.15) rotate(90deg);\\n  box-shadow: 0 0 30px rgba(0, 247, 255, 0.4);\\n}\\n\\n\\n\\n.futuristic-loading[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 20px;\\n}\\n\\n.futuristic-loading-circle[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 50px;\\n  border-radius: 50%;\\n  border: 3px solid rgba(0, 247, 255, 0.1);\\n  border-top-color: var(--accent-color);\\n  animation: spin 1.5s linear infinite;\\n  margin-bottom: 15px;\\n}\\n\\n.futuristic-loading-text[_ngcontent-%COMP%] {\\n  color: var(--accent-color);\\n  font-size: 0.875rem;\\n  letter-spacing: 0.5px;\\n}\\n\\n.futuristic-loading-more[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  padding: 10px;\\n  background-color: rgba(0, 247, 255, 0.05);\\n  border-radius: var(--border-radius-md);\\n}\\n\\n.futuristic-loading-dots[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 5px;\\n  margin-bottom: 5px;\\n}\\n\\n.futuristic-loading-dot[_ngcontent-%COMP%] {\\n  width: 8px;\\n  height: 8px;\\n  background-color: var(--accent-color);\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_pulse 1.5s infinite ease-in-out;\\n}\\n\\n\\n\\n.futuristic-conversation-start[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 100%;\\n  margin: 20px 0;\\n}\\n\\n.futuristic-conversation-start-line[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 1px;\\n  background: linear-gradient(\\n    to right,\\n    transparent,\\n    var(--accent-color),\\n    transparent\\n  );\\n  opacity: 0.3;\\n}\\n\\n.futuristic-conversation-start-text[_ngcontent-%COMP%] {\\n  margin: 0 10px;\\n  padding: 4px 12px;\\n  background-color: rgba(0, 247, 255, 0.1);\\n  border-radius: var(--border-radius-md);\\n  color: var(--accent-color);\\n  font-size: 0.75rem;\\n  letter-spacing: 1px;\\n  text-transform: uppercase;\\n}\\n\\n\\n\\n.futuristic-error-container[_ngcontent-%COMP%] {\\n  margin: 15px;\\n  padding: 15px;\\n  background: rgba(255, 0, 0, 0.1);\\n  border-left: 3px solid #ff3b30;\\n  border-radius: 5px;\\n  display: flex;\\n  align-items: flex-start;\\n}\\n\\n.futuristic-error-icon[_ngcontent-%COMP%] {\\n  color: #ff3b30;\\n  font-size: 1.25rem;\\n  margin-right: 15px;\\n}\\n\\n.futuristic-error-title[_ngcontent-%COMP%] {\\n  color: #ff3b30;\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n  margin-bottom: 5px;\\n}\\n\\n.futuristic-error-message[_ngcontent-%COMP%] {\\n  color: var(--text-dim);\\n  font-size: 0.8125rem;\\n}\\n\\n\\n\\n.futuristic-message-pending[_ngcontent-%COMP%] {\\n  opacity: 0.7;\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0% {\\n    opacity: 0.6;\\n  }\\n  50% {\\n    opacity: 0.9;\\n  }\\n  100% {\\n    opacity: 0.6;\\n  }\\n}\\n\\n.futuristic-message-sending[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_pulse 1.5s infinite ease-in-out;\\n}\\n\\n\\n\\n.futuristic-message-error[_ngcontent-%COMP%] {\\n  border: 1px solid rgba(239, 68, 68, 0.5);\\n}\\n\\n\\n\\n.futuristic-voice-message-container[_ngcontent-%COMP%] {\\n  padding: 8px !important;\\n  min-width: 200px;\\n  border-radius: 14px;\\n  -webkit-backdrop-filter: blur(12px);\\n          backdrop-filter: blur(12px);\\n  border: 1px solid rgba(255, 255, 255, 0.15);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);\\n  transform: perspective(800px) rotateX(0deg);\\n  transition: all 0.3s ease;\\n}\\n\\n.futuristic-voice-message-container[_ngcontent-%COMP%]:hover {\\n  transform: perspective(800px) rotateX(2deg) translateY(-3px);\\n  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.25);\\n}\\n\\n.futuristic-voice-message[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  position: relative;\\n  z-index: 1;\\n}\\n\\n.futuristic-message-current-user[_ngcontent-%COMP%]   .futuristic-voice-message-container[_ngcontent-%COMP%] {\\n  background: linear-gradient(\\n    135deg,\\n    rgba(0, 247, 255, 0.2),\\n    rgba(131, 56, 236, 0.2)\\n  );\\n  border-color: rgba(255, 255, 255, 0.2);\\n}\\n\\n.futuristic-message-other-user[_ngcontent-%COMP%]   .futuristic-voice-message-container[_ngcontent-%COMP%] {\\n  background: rgba(30, 30, 40, 0.4);\\n  border-color: rgba(0, 247, 255, 0.15);\\n}\\n\\n.futuristic-voice-play-button[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);\\n  box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);\\n  position: relative;\\n  overflow: hidden;\\n  z-index: 2;\\n  animation: pulse-slow 4s infinite;\\n}\\n\\n.futuristic-voice-play-button[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: radial-gradient(\\n    circle at center,\\n    rgba(0, 247, 255, 0.2) 0%,\\n    transparent 70%\\n  );\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n  z-index: -1;\\n}\\n\\n.futuristic-voice-play-button[_ngcontent-%COMP%]:hover::before {\\n  opacity: 1;\\n}\\n\\n.futuristic-message-current-user[_ngcontent-%COMP%]   .futuristic-voice-play-button[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.9);\\n  color: var(--accent-color);\\n  border: 2px solid rgba(0, 247, 255, 0.5);\\n}\\n\\n.futuristic-message-other-user[_ngcontent-%COMP%]   .futuristic-voice-play-button[_ngcontent-%COMP%] {\\n  background-color: rgba(0, 247, 255, 0.9);\\n  color: rgba(10, 10, 20, 0.9);\\n  border: 2px solid rgba(0, 247, 255, 0.2);\\n}\\n\\n.futuristic-voice-play-button[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.2) translateY(-3px);\\n  box-shadow: 0 8px 25px rgba(0, 247, 255, 0.5);\\n  animation: none;\\n}\\n\\n.futuristic-voice-play-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  transition: all 0.3s ease;\\n}\\n\\n.futuristic-voice-play-button[_ngcontent-%COMP%]:hover   i[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n}\\n\\n.futuristic-voice-waveform[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 3px;\\n  height: 36px;\\n  padding: 0 5px;\\n  position: relative;\\n}\\n\\n.futuristic-voice-waveform[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: linear-gradient(\\n    90deg,\\n    transparent,\\n    rgba(0, 247, 255, 0.1),\\n    transparent\\n  );\\n  opacity: 0;\\n  transition: opacity 0.5s ease;\\n  animation: _ngcontent-%COMP%_waveformPulse 2s infinite;\\n  border-radius: 8px;\\n  pointer-events: none;\\n}\\n\\n@keyframes _ngcontent-%COMP%_waveformPulse {\\n  0%,\\n  100% {\\n    opacity: 0;\\n  }\\n  50% {\\n    opacity: 1;\\n  }\\n}\\n\\n.futuristic-voice-bar[_ngcontent-%COMP%] {\\n  width: 3px;\\n  background-color: currentColor;\\n  border-radius: 4px;\\n  transition: height 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);\\n  box-shadow: 0 0 5px rgba(0, 247, 255, 0.3);\\n}\\n\\n.futuristic-message-current-user[_ngcontent-%COMP%]   .futuristic-voice-bar[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.8);\\n}\\n\\n.futuristic-message-other-user[_ngcontent-%COMP%]   .futuristic-voice-bar[_ngcontent-%COMP%] {\\n  background-color: rgba(0, 247, 255, 0.8);\\n}\\n\\n.futuristic-voice-waveform[_ngcontent-%COMP%]:hover   .futuristic-voice-bar[_ngcontent-%COMP%] {\\n  transform: scaleY(1.1);\\n  box-shadow: 0 0 8px rgba(0, 247, 255, 0.5);\\n}\\n\\n\\n\\naudio[_ngcontent-%COMP%]:focus, audio[_ngcontent-%COMP%]:active, audio[_ngcontent-%COMP%]:hover, .voice-message-player[_ngcontent-%COMP%]:focus, .voice-message-player[_ngcontent-%COMP%]:active, .voice-message-player[_ngcontent-%COMP%]:hover, app-voice-message-player[_ngcontent-%COMP%] {\\n  outline: none !important;\\n  border: none !important;\\n  box-shadow: none !important;\\n  background-color: transparent !important;\\n}\\n\\n\\n\\n.futuristic-message-current-user[_ngcontent-%COMP%]   app-voice-message-player[_ngcontent-%COMP%]   .waveform-bar[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.7) !important;\\n}\\n\\n.futuristic-message-current-user[_ngcontent-%COMP%]   app-voice-message-player[_ngcontent-%COMP%]   .waveform-bar.active[_ngcontent-%COMP%] {\\n  background-color: #ffffff !important;\\n  box-shadow: 0 0 5px rgba(255, 255, 255, 0.8) !important;\\n}\\n\\n.futuristic-message-other-user[_ngcontent-%COMP%]   app-voice-message-player[_ngcontent-%COMP%]   .waveform-bar[_ngcontent-%COMP%] {\\n  background-color: rgba(0, 247, 255, 0.5) !important;\\n}\\n\\n.futuristic-message-other-user[_ngcontent-%COMP%]   app-voice-message-player[_ngcontent-%COMP%]   .waveform-bar.active[_ngcontent-%COMP%] {\\n  background-color: var(--accent-color) !important;\\n  box-shadow: 0 0 5px var(--accent-color) !important;\\n}\\n\\n\\n\\n.futuristic-message-current-user[_ngcontent-%COMP%]   app-voice-message-player[_ngcontent-%COMP%]   .play-button[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.9) !important;\\n  color: var(--accent-color) !important;\\n  box-shadow: var(--glow-effect) !important;\\n}\\n\\n.futuristic-message-other-user[_ngcontent-%COMP%]   app-voice-message-player[_ngcontent-%COMP%]   .play-button[_ngcontent-%COMP%] {\\n  background-color: var(--accent-color) !important;\\n  color: var(--dark-bg) !important;\\n  box-shadow: var(--glow-effect) !important;\\n}\\n\\n\\n\\n.image-modal[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_scaleIn 0.3s ease-in-out;\\n  transition: transform 0.2s ease;\\n}\\n\\n\\n\\n.futuristic-typing-indicator[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-end;\\n  margin: 10px 0;\\n  animation: _ngcontent-%COMP%_fadeIn 0.3s ease-out;\\n}\\n\\n.futuristic-typing-bubble[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.1);\\n  border-radius: 16px;\\n  padding: 10px 15px;\\n  margin-left: 10px;\\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);\\n  border: 1px solid rgba(255, 255, 255, 0.1);\\n}\\n\\n.futuristic-typing-dots[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 4px;\\n}\\n\\n.futuristic-typing-dot[_ngcontent-%COMP%] {\\n  width: 8px;\\n  height: 8px;\\n  background-color: var(--accent-color);\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_typingPulse 1.5s infinite ease-in-out;\\n  box-shadow: 0 0 5px var(--accent-color);\\n}\\n\\n@keyframes _ngcontent-%COMP%_typingPulse {\\n  0%,\\n  100% {\\n    transform: translateY(0);\\n    opacity: 0.5;\\n  }\\n  50% {\\n    transform: translateY(-5px);\\n    opacity: 1;\\n    box-shadow: 0 0 8px var(--accent-color);\\n  }\\n}\\n\\n\\n\\n.futuristic-no-messages[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  height: 100%;\\n  padding: 20px;\\n  text-align: center;\\n}\\n\\n.futuristic-no-messages-icon[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  color: var(--accent-color);\\n  margin-bottom: 20px;\\n  opacity: 0.7;\\n}\\n\\n.futuristic-no-messages-text[_ngcontent-%COMP%] {\\n  color: var(--text-dim);\\n  font-size: 1rem;\\n  margin-bottom: 30px;\\n  line-height: 1.5;\\n}\\n\\n\\n\\n.futuristic-file-preview[_ngcontent-%COMP%] {\\n  position: relative;\\n  margin-bottom: 10px;\\n  border-radius: var(--border-radius-md);\\n  overflow: hidden;\\n  max-width: 200px;\\n  border: 2px solid rgba(0, 247, 255, 0.3);\\n  box-shadow: var(--glow-effect);\\n}\\n\\n.futuristic-preview-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  display: block;\\n}\\n\\n.futuristic-remove-button[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 5px;\\n  right: 5px;\\n  background: rgba(0, 0, 0, 0.6);\\n  border: none;\\n  border-radius: 50%;\\n  width: 24px;\\n  height: 24px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: white;\\n  font-size: 12px;\\n  cursor: pointer;\\n  transition: all var(--transition-fast);\\n}\\n\\n.futuristic-remove-button[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 0, 0, 0.7);\\n  transform: scale(1.1);\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\", \"\\n\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_borderFlow {\\n  0% { background-position: 0% 0%; }\\n  100% { background-position: 200% 0%; }\\n}\\n\\n@keyframes _ngcontent-%COMP%_ambientGlow {\\n  0% { opacity: 0.3; transform: scale(0.95); }\\n  100% { opacity: 0.7; transform: scale(1.05); }\\n}\\n\\n@keyframes _ngcontent-%COMP%_rotateHalo {\\n  0% { transform: rotate(0deg); }\\n  100% { transform: rotate(360deg); }\\n}\\n\\n@keyframes _ngcontent-%COMP%_flameBorder {\\n  0% {\\n    filter: brightness(1) blur(0px);\\n    transform: scaleY(0.95);\\n  }\\n  50% {\\n    filter: brightness(1.2) blur(1px);\\n    transform: scaleY(1.05);\\n  }\\n  100% {\\n    filter: brightness(1) blur(0px);\\n    transform: scaleY(0.95);\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_sparkle {\\n  0% { opacity: 0; transform: translateX(0); }\\n  25% { opacity: 0.7; transform: translateX(1px); }\\n  50% { opacity: 0; transform: translateX(0); }\\n  75% { opacity: 0.5; transform: translateX(2px); }\\n  100% { opacity: 0; transform: translateX(0); }\\n}\\n\\n\\n\\n.magic-input-container[_ngcontent-%COMP%] {\\n  padding: 8px 12px;\\n  background-color: rgba(18, 18, 18, 0.7);\\n  min-height: 60px;\\n  position: relative;\\n  z-index: 10;\\n  overflow: hidden;\\n  border-top: 1px solid rgba(0, 247, 255, 0.1);\\n}\\n\\n\\n\\n.magic-input-container[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  height: 1px;\\n  background: linear-gradient(90deg, \\n    transparent 0%, \\n    rgba(0, 247, 255, 0.2) 20%, \\n    rgba(0, 247, 255, 0.8) 50%, \\n    rgba(0, 247, 255, 0.2) 80%, \\n    transparent 100%);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_borderFlow 3s infinite linear;\\n  box-shadow: 0 0 15px rgba(0, 247, 255, 0.7);\\n  z-index: 1;\\n}\\n\\n\\n\\n.magic-input-container[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: radial-gradient(\\n    ellipse at center, \\n    rgba(0, 247, 255, 0.05) 0%, \\n    transparent 70%\\n  );\\n  opacity: 0;\\n  animation: _ngcontent-%COMP%_ambientGlow 4s infinite alternate;\\n  pointer-events: none;\\n  z-index: -1;\\n}\\n\\n\\n\\n.magic-input-field[_ngcontent-%COMP%] {\\n  flex: 1;\\n  font-size: 0.9rem;\\n  padding: 10px 16px;\\n  height: 44px;\\n  border-radius: 22px;\\n  border: 1px solid rgba(0, 247, 255, 0.2);\\n  background-color: rgba(0, 247, 255, 0.05);\\n  color: white;\\n  transition: all 0.3s ease;\\n  position: relative;\\n  overflow: hidden;\\n  -webkit-backdrop-filter: blur(5px);\\n          backdrop-filter: blur(5px);\\n  box-shadow: inset 0 0 10px rgba(0, 247, 255, 0.1);\\n}\\n\\n.magic-input-field[_ngcontent-%COMP%]:focus {\\n  border-color: transparent;\\n  outline: none;\\n  box-shadow: 0 0 0 1px rgba(0, 247, 255, 0.5), \\n              0 0 15px rgba(0, 247, 255, 0.5),\\n              inset 0 0 10px rgba(0, 247, 255, 0.2);\\n  background-color: rgba(0, 247, 255, 0.08);\\n}\\n\\n\\n\\n.magic-send-button[_ngcontent-%COMP%] {\\n  position: relative;\\n  background: linear-gradient(135deg, #00f7ff, #0066ff);\\n  color: white;\\n  border: none;\\n  border-radius: 50%;\\n  width: 44px;\\n  height: 44px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);\\n  box-shadow: 0 0 10px rgba(0, 247, 255, 0.5),\\n              0 0 20px rgba(0, 247, 255, 0.2),\\n              inset 0 0 5px rgba(255, 255, 255, 0.3);\\n  overflow: hidden;\\n  z-index: 2;\\n}\\n\\n\\n\\n.magic-send-button[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: -2px;\\n  left: -2px;\\n  right: -2px;\\n  bottom: -2px;\\n  background: conic-gradient(\\n    from 0deg,\\n    transparent 0%,\\n    rgba(0, 247, 255, 0.8) 25%,\\n    rgba(131, 56, 236, 0.8) 50%,\\n    rgba(0, 247, 255, 0.8) 75%,\\n    transparent 100%\\n  );\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_rotateHalo 3s linear infinite;\\n  z-index: -1;\\n  opacity: 0.5;\\n}\\n\\n.magic-send-button[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-3px) scale(1.1);\\n  box-shadow: 0 0 15px rgba(0, 247, 255, 0.7),\\n              0 0 30px rgba(0, 247, 255, 0.4),\\n              inset 0 0 10px rgba(255, 255, 255, 0.5);\\n}\\n\\n.magic-send-button[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n  transform: none;\\n  box-shadow: none;\\n}\\n\\n\\n\\n.magic-notification-unread[_ngcontent-%COMP%] {\\n  position: relative;\\n  overflow: hidden;\\n  border-left: 5px solid transparent;\\n  background-color: rgba(0, 247, 255, 0.05);\\n}\\n\\n\\n\\n.magic-notification-unread[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 5px;\\n  height: 100%;\\n  background: linear-gradient(to bottom, #00f7ff, #0066ff, #00f7ff);\\n  box-shadow: 0 0 15px rgba(0, 247, 255, 0.8), 0 0 30px rgba(0, 102, 255, 0.4);\\n  animation: _ngcontent-%COMP%_flameBorder 3s ease-in-out infinite;\\n  z-index: 1;\\n}\\n\\n\\n\\n.magic-notification-unread[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 15px;\\n  height: 100%;\\n  background-image: \\n    radial-gradient(circle at 30% 20%, rgba(0, 247, 255, 0.9) 0%, rgba(0, 247, 255, 0) 3%),\\n    radial-gradient(circle at 40% 40%, rgba(0, 247, 255, 0.9) 0%, rgba(0, 247, 255, 0) 4%),\\n    radial-gradient(circle at 20% 60%, rgba(0, 247, 255, 0.9) 0%, rgba(0, 247, 255, 0) 3%),\\n    radial-gradient(circle at 40% 80%, rgba(0, 247, 255, 0.9) 0%, rgba(0, 247, 255, 0) 4%),\\n    radial-gradient(circle at 10% 30%, rgba(0, 247, 255, 0.9) 0%, rgba(0, 247, 255, 0) 3%);\\n  opacity: 0;\\n  z-index: 2;\\n  animation: _ngcontent-%COMP%_sparkle 4s ease-in-out infinite;\\n}\\n\\n.magic-notification-unread[_ngcontent-%COMP%]:hover::before {\\n  animation-duration: 1.5s;\\n  box-shadow: 0 0 20px rgba(0, 247, 255, 0.9), 0 0 40px rgba(0, 102, 255, 0.5);\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\", \"@keyframes _ngcontent-%COMP%_borderFlow {\\n      0% {\\n        background-position: 0% 0%;\\n      }\\n      100% {\\n        background-position: 200% 0%;\\n      }\\n    }\\n\\n    @keyframes _ngcontent-%COMP%_ambientGlow {\\n      0% {\\n        opacity: 0;\\n        transform: scale(0.95);\\n      }\\n      100% {\\n        opacity: 0.5;\\n        transform: scale(1.05);\\n      }\\n    }\\n\\n    @keyframes _ngcontent-%COMP%_rotateHalo {\\n      0% {\\n        transform: rotate(0deg);\\n      }\\n      100% {\\n        transform: rotate(360deg);\\n      }\\n    }\\n\\n    @keyframes _ngcontent-%COMP%_pulseButton {\\n      0% {\\n        box-shadow: 0 0 10px rgba(0, 247, 255, 0.5),\\n          0 0 20px rgba(0, 247, 255, 0.2);\\n        transform: scale(1);\\n      }\\n      50% {\\n        box-shadow: 0 0 15px rgba(0, 247, 255, 0.7),\\n          0 0 30px rgba(0, 247, 255, 0.4);\\n        transform: scale(1.05);\\n      }\\n      100% {\\n        box-shadow: 0 0 10px rgba(0, 247, 255, 0.5),\\n          0 0 20px rgba(0, 247, 255, 0.2);\\n        transform: scale(1);\\n      }\\n    }\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "Subscription", "MessageType", "CallType", "switchMap", "distinctUntilChanged", "filter", "i0", "ɵɵelement", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r1", "otherParticipant", "username", "isOnline", "formatLastActive", "lastActive", "ɵɵlistener", "MessageChatComponent_div_45_Template_a_click_4_listener", "ɵɵrestoreView", "_r21", "ctx_r20", "ɵɵnextContext", "ɵɵresetView", "changeTheme", "MessageChatComponent_div_45_Template_a_click_9_listener", "ctx_r22", "MessageChatComponent_div_45_Template_a_click_14_listener", "ctx_r23", "MessageChatComponent_div_45_Template_a_click_19_listener", "ctx_r24", "ɵɵtextInterpolate", "ctx_r7", "error", "ctx_r28", "formatMessageDate", "message_r26", "timestamp", "ɵɵproperty", "sender", "image", "ɵɵsanitizeUrl", "ɵɵtemplate", "MessageChatComponent_ng_container_54_div_1_div_5_span_6_i_1_Template", "MessageChatComponent_ng_container_54_div_1_div_5_span_6_i_2_Template", "isRead", "MessageChatComponent_ng_container_54_div_1_div_5_span_6_Template", "ɵɵpureFunction3", "_c2", "isPending", "isError", "content", "ctx_r30", "formatMessageTime", "id", "currentUserId", "_id", "senderId", "ɵɵstyleProp", "i_r42", "MessageChatComponent_ng_container_54_div_1_div_6_span_10_i_1_Template", "MessageChatComponent_ng_container_54_div_1_div_6_span_10_i_2_Template", "MessageChatComponent_ng_container_54_div_1_div_6_div_5_Template", "MessageChatComponent_ng_container_54_div_1_div_6_span_10_Template", "ɵɵpureFunction0", "_c3", "ctx_r31", "getVoiceMessageUrl", "getVoiceMessageDuration", "MessageChatComponent_ng_container_54_div_1_div_7_span_9_i_1_Template", "MessageChatComponent_ng_container_54_div_1_div_7_span_9_i_2_Template", "MessageChatComponent_ng_container_54_div_1_div_7_span_9_Template", "ctx_r32", "getImageUrl", "MessageChatComponent_ng_container_54_div_1_div_1_Template", "MessageChatComponent_ng_container_54_div_1_div_3_Template", "MessageChatComponent_ng_container_54_div_1_div_5_Template", "MessageChatComponent_ng_container_54_div_1_div_6_Template", "MessageChatComponent_ng_container_54_div_1_div_7_Template", "ɵɵattribute", "ctx_r25", "shouldShowDateHeader", "i_r27", "ɵɵpureFunction2", "_c4", "hasImage", "isVoiceMessage", "ɵɵelementContainerStart", "MessageChatComponent_ng_container_54_div_1_Template", "ɵɵelementContainerEnd", "ctx_r8", "messages", "MessageChatComponent_ng_template_55_div_0_Template_button_click_7_listener", "_r54", "ctx_r53", "tmp_b_0", "messageForm", "get", "setValue", "MessageChatComponent_ng_template_55_div_0_Template", "ctx_r10", "loading", "ctx_r11", "MessageChatComponent_div_59_Template_button_click_2_listener", "_r56", "ctx_r55", "removeAttachment", "ctx_r12", "previewUrl", "MessageChatComponent_div_60_button_15_Template_button_click_0_listener", "restoredCtx", "_r60", "emoji_r58", "$implicit", "ctx_r59", "insert<PERSON><PERSON><PERSON>", "MessageChatComponent_div_60_button_15_Template", "ctx_r13", "commonEmojis", "MessageChatComponent_app_voice_recorder_69_Template_app_voice_recorder_recordingComplete_0_listener", "$event", "_r62", "ctx_r61", "onVoiceRecordingComplete", "MessageChatComponent_app_voice_recorder_69_Template_app_voice_recorder_recordingCancelled_0_listener", "ctx_r63", "onVoiceRecordingCancelled", "MessageChatComponent_input_70_Template_input_input_0_listener", "_r65", "ctx_r64", "onTyping", "MessageChatComponent_button_71_Template_button_click_0_listener", "_r67", "ctx_r66", "toggleVoiceRecording", "MessageChatComponent_button_72_i_1_Template", "MessageChatComponent_button_72_i_2_Template", "ctx_r18", "isUploading", "invalid", "selectedFile", "MessageChatComponent_div_73_Template_button_click_10_listener", "_r71", "ctx_r70", "rejectCall", "MessageChatComponent_div_73_Template_button_click_14_listener", "ctx_r72", "acceptCall", "ctx_r19", "incomingCall", "caller", "type", "MessageChatComponent", "constructor", "MessageService", "route", "authService", "fb", "statusService", "router", "toastService", "logger", "cdr", "conversation", "currentUsername", "isTyping", "isRecordingVoice", "voiceRecordingDuration", "MAX_MESSAGES_PER_SIDE", "MAX_MESSAGES_TO_LOAD", "MAX_TOTAL_MESSAGES", "currentPage", "isLoadingMore", "hasMoreMessages", "subscriptions", "editingMessageId", "<PERSON><PERSON><PERSON><PERSON>", "showMessageOptions", "showDeleteConfirm", "selectedTheme", "showThemeSelector", "showEmojiPicker", "showCallModal", "isCurrentlyTyping", "TYPING_DELAY", "TYPING_TIMEOUT", "group", "max<PERSON><PERSON><PERSON>", "ngOnInit", "getCurrentUserId", "savedTheme", "localStorage", "getItem", "debug", "loadVoiceMessages", "subscribeToNotifications", "routeSub", "params", "pipe", "getConversation", "subscribe", "next", "handleConversationLoaded", "handleError", "add", "sub", "getVoiceMessages", "voiceMessages", "info", "length", "setTimeout", "detectChanges", "message", "showError", "getFileIcon", "mimeType", "startsWith", "includes", "getFileType", "typeMap", "key", "value", "Object", "entries", "participants", "substring", "find", "p", "conversationMessages", "sort", "a", "b", "timeA", "Date", "getTime", "timeB", "firstMessage", "receiver", "receiverId", "scrollToBottom", "markMessagesAsRead", "subscribeToConversationUpdates", "subscribeToNewMessages", "subscribeToTypingIndicators", "conversationId", "updatedConversation", "newMessage", "markMessageAsRead", "subscribeToTypingIndicator", "event", "userId", "clearTimeout", "typingTimeout", "unreadMessages", "msg", "for<PERSON>ach", "onFileSelected", "file", "target", "files", "size", "validTypes", "reader", "FileReader", "onload", "result", "readAsDataURL", "fileInput", "nativeElement", "typingTimer", "startTyping", "stopTyping", "toggleThemeSelector", "clickHandler", "closest", "document", "removeEventListener", "addEventListener", "theme", "setItem", "sendMessage", "token", "warn", "stopTypingIndicator", "tempMessage", "fileType", "attachments", "url", "toString", "IMAGE", "name", "FILE", "fileToSend", "reset", "sendSub", "undefined", "TEXT", "map", "date", "toLocaleTimeString", "hour", "minute", "hour12", "lastActiveDate", "now", "diffHours", "Math", "abs", "toLocaleDateString", "today", "options", "weekday", "toDateString", "yesterday", "setDate", "getDate", "day", "toUpperCase", "index", "currentMsg", "prevMsg", "currentDate", "getDateFromTimestamp", "prevDate", "getMessageType", "msgType", "AUDIO", "VIDEO", "SYSTEM", "attachment", "attachmentTypeStr", "VOICE_MESSAGE", "VOICE_MESSAGE_LOWER", "some", "att", "metadata", "voiceAttachment", "duration", "getMessageTypeClass", "isCurrentUser", "baseClass", "messageType", "IMAGE_LOWER", "FILE_LOWER", "onScroll", "container", "scrollTop", "showLoadingIndicator", "oldScrollHeight", "scrollHeight", "firstVisibleMessage", "getFirstVisibleMessage", "loadMoreMessages", "requestAnimationFrame", "preserveScrollPosition", "messageElement", "findMessageElement", "scrollIntoView", "block", "newScrollHeight", "scrollDiff", "hideLoadingIndicator", "messagesContainer", "messageElements", "querySelectorAll", "i", "element", "rect", "getBoundingClientRect", "top", "bottom", "clientHeight", "messageId", "getAttribute", "m", "querySelector", "getElementById", "indicator", "createElement", "className", "innerHTML", "prepend", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "oldMessages", "existingIds", "Set", "newMessages", "has", "slice", "isSameTimestamp", "timestamp1", "timestamp2", "time1", "time2", "force", "isScrolledToBottom", "scrollTo", "behavior", "err", "audioBlob", "sendVoiceMessage", "openImageFullscreen", "imageUrl", "window", "open", "ngAfterViewChecked", "ngOnDestroy", "unsubscribe", "goBackToConversations", "navigate", "toggleEmojiPicker", "emoji", "control", "currentValue", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputElement", "focus", "notificationSub", "subscribeToNewNotifications", "notification", "mark<PERSON><PERSON><PERSON>", "callSub", "incomingCall$", "call", "play", "initiateCall", "console", "endCall", "activeCall", "activeCall$", "ɵɵdirectiveInject", "i1", "i2", "ActivatedRoute", "i3", "AuthuserService", "i4", "FormBuilder", "i5", "UserStatusService", "Router", "i6", "ToastService", "i7", "LoggerService", "ChangeDetectorRef", "selectors", "viewQuery", "MessageChatComponent_Query", "rf", "ctx", "MessageChatComponent_Template_button_click_30_listener", "MessageChatComponent_span_35_Template", "MessageChatComponent_div_36_Template", "MessageChatComponent_Template_button_click_38_listener", "MessageChatComponent_Template_button_click_40_listener", "MessageChatComponent_Template_button_click_43_listener", "MessageChatComponent_div_45_Template", "MessageChatComponent_Template_div_scroll_48_listener", "MessageChatComponent_div_50_Template", "MessageChatComponent_div_51_Template", "MessageChatComponent_div_52_Template", "MessageChatComponent_div_53_Template", "MessageChatComponent_ng_container_54_Template", "MessageChatComponent_ng_template_55_Template", "ɵɵtemplateRefExtractor", "MessageChatComponent_div_57_Template", "MessageChatComponent_div_59_Template", "MessageChatComponent_div_60_Template", "MessageChatComponent_Template_form_ngSubmit_61_listener", "MessageChatComponent_Template_button_click_63_listener", "MessageChatComponent_Template_button_click_65_listener", "_r73", "_r14", "ɵɵreference", "click", "MessageChatComponent_Template_input_change_67_listener", "MessageChatComponent_app_voice_recorder_69_Template", "MessageChatComponent_input_70_Template", "MessageChatComponent_button_71_Template", "MessageChatComponent_button_72_Template", "MessageChatComponent_div_73_Template", "_r9", "ɵɵpureFunction1", "_c5", "tmp_18_0", "tmp_19_0"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\messages\\message-chat\\message-chat.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\views\\front\\messages\\message-chat\\message-chat.component.html"], "sourcesContent": ["import {\n  Component,\n  OnIni<PERSON>,\n  On<PERSON><PERSON><PERSON>,\n  <PERSON>Child,\n  ElementRef,\n  AfterViewChecked,\n  ChangeDetectorRef,\n} from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { AuthuserService } from 'src/app/services/authuser.service';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Subscription, combineLatest, Observable, of } from 'rxjs';\nimport { User } from '@app/models/user.model';\nimport { UserStatusService } from 'src/app/services/user-status.service';\nimport {\n  Message,\n  Conversation,\n  Attachment,\n  MessageType,\n  CallType,\n} from 'src/app/models/message.model';\nimport { ToastService } from 'src/app/services/toast.service';\nimport { switchMap, distinctUntilChanged, filter } from 'rxjs/operators';\nimport { MessageService } from '@app/services/message.service';\nimport { LoggerService } from 'src/app/services/logger.service';\n@Component({\n  selector: 'app-message-chat',\n  templateUrl: 'message-chat.component.html',\n  styleUrls: ['./message-chat.component.css', './message-chat-magic.css'],\n})\nexport class MessageChatComponent\n  implements OnInit, OnDestroy, AfterViewChecked\n{\n  @ViewChild('messagesContainer') private messagesContainer!: ElementRef;\n  @ViewChild('fileInput', { static: false })\n  fileInput!: ElementRef<HTMLInputElement>;\n\n  messages: Message[] = [];\n  messageForm: FormGroup;\n  conversation: Conversation | null = null;\n  loading = true;\n  error: any;\n  currentUserId: string | null = null;\n  currentUsername: string = 'You';\n  otherParticipant: User | null = null;\n  selectedFile: File | null = null;\n  previewUrl: string | ArrayBuffer | null = null;\n  isUploading = false;\n  isTyping = false;\n  typingTimeout: any;\n  isRecordingVoice = false;\n  voiceRecordingDuration = 0;\n\n  private readonly MAX_MESSAGES_PER_SIDE = 5; // Nombre maximum de messages à afficher par côté (expéditeur/destinataire)\n  private readonly MAX_MESSAGES_TO_LOAD = 10; // Nombre maximum de messages à charger à la fois (pagination)\n  private readonly MAX_TOTAL_MESSAGES = 100; // Limite totale de messages à conserver en mémoire\n  private currentPage = 1; // Page actuelle pour la pagination\n  isLoadingMore = false; // Indicateur de chargement en cours (public pour le template)\n  hasMoreMessages = true; // Indique s'il y a plus de messages à charger (public pour le template)\n  private subscriptions: Subscription = new Subscription();\n\n  // Variables pour l'édition et la suppression de messages\n  editingMessageId: string | null = null;\n  editingContent: string = '';\n  showMessageOptions: { [messageId: string]: boolean } = {};\n  showDeleteConfirm: { [messageId: string]: boolean } = {};\n\n  // Variables pour le sélecteur de thème\n  selectedTheme: string = 'theme-default'; // Thème par défaut\n  showThemeSelector: boolean = false; // Affichage du sélecteur de thème\n\n  // Variables pour le sélecteur d'émojis\n  showEmojiPicker: boolean = false;\n\n  // Variables pour les appels\n  incomingCall: any = null;\n  showCallModal: boolean = false;\n\n  commonEmojis: string[] = [\n    '😀',\n    '😃',\n    '😄',\n    '😁',\n    '😆',\n    '😅',\n    '😂',\n    '🤣',\n    '😊',\n    '😇',\n    '🙂',\n    '🙃',\n    '😉',\n    '😌',\n    '😍',\n    '🥰',\n    '😘',\n    '😗',\n    '😙',\n    '😚',\n    '😋',\n    '😛',\n    '😝',\n    '😜',\n    '🤪',\n    '🤨',\n    '🧐',\n    '🤓',\n    '😎',\n    '🤩',\n    '😏',\n    '😒',\n    '😞',\n    '😔',\n    '😟',\n    '😕',\n    '🙁',\n    '☹️',\n    '😣',\n    '😖',\n    '😫',\n    '😩',\n    '🥺',\n    '😢',\n    '😭',\n    '😤',\n    '😠',\n    '😡',\n    '🤬',\n    '🤯',\n    '😳',\n    '🥵',\n    '🥶',\n    '😱',\n    '😨',\n    '😰',\n    '😥',\n    '😓',\n    '🤗',\n    '🤔',\n    '👍',\n    '👎',\n    '👏',\n    '🙌',\n    '👐',\n    '🤲',\n    '🤝',\n    '🙏',\n    '✌️',\n    '🤞',\n    '❤️',\n    '🧡',\n    '💛',\n    '💚',\n    '💙',\n    '💜',\n    '🖤',\n    '💔',\n    '💯',\n    '💢',\n  ];\n\n  constructor(\n    private MessageService: MessageService,\n    public route: ActivatedRoute,\n    private authService: AuthuserService,\n    private fb: FormBuilder,\n    public statusService: UserStatusService,\n    public router: Router,\n    private toastService: ToastService,\n    private logger: LoggerService,\n    private cdr: ChangeDetectorRef\n  ) {\n    this.messageForm = this.fb.group({\n      content: ['', [Validators.maxLength(1000)]],\n    });\n  }\n  ngOnInit(): void {\n    this.currentUserId = this.authService.getCurrentUserId();\n\n    // Charger le thème sauvegardé\n    const savedTheme = localStorage.getItem('chat-theme');\n    if (savedTheme) {\n      this.selectedTheme = savedTheme;\n      this.logger.debug('MessageChat', `Loaded saved theme: ${savedTheme}`);\n    }\n\n    // Récupérer les messages vocaux pour assurer leur persistance\n    this.loadVoiceMessages();\n\n    // S'abonner aux notifications en temps réel\n    this.subscribeToNotifications();\n\n    const routeSub = this.route.params\n      .pipe(\n        filter((params) => params['id']),\n        distinctUntilChanged(),\n        switchMap((params) => {\n          this.loading = true;\n          this.messages = [];\n          this.currentPage = 1; // Réinitialiser à la page 1\n          this.hasMoreMessages = true; // Réinitialiser l'indicateur de messages supplémentaires\n\n          this.logger.debug(\n            'MessageChat',\n            `Loading conversation with pagination: page=${this.currentPage}, limit=${this.MAX_MESSAGES_TO_LOAD}`\n          );\n\n          // Charger la conversation avec pagination (page 1, limit 10)\n          return this.MessageService.getConversation(\n            params['id'],\n            this.MAX_MESSAGES_TO_LOAD,\n            this.currentPage // Utiliser la page au lieu de l'offset\n          );\n        })\n      )\n      .subscribe({\n        next: (conversation) => {\n          this.handleConversationLoaded(conversation);\n        },\n        error: (error) => {\n          this.handleError('Failed to load conversation', error);\n        },\n      });\n    this.subscriptions.add(routeSub);\n  }\n\n  /**\n   * Charge les messages vocaux pour assurer leur persistance\n   */\n  private loadVoiceMessages(): void {\n    this.logger.debug('MessageChat', 'Loading voice messages for persistence');\n\n    const sub = this.MessageService.getVoiceMessages().subscribe({\n      next: (voiceMessages) => {\n        this.logger.info(\n          'MessageChat',\n          `Retrieved ${voiceMessages.length} voice messages`\n        );\n\n        // Les messages vocaux sont maintenant chargés et disponibles dans le service\n        // Ils seront automatiquement associés aux conversations correspondantes\n        if (voiceMessages.length > 0) {\n          this.logger.debug(\n            'MessageChat',\n            'Voice messages loaded successfully'\n          );\n\n          // Forcer le rafraîchissement de la vue après le chargement des messages vocaux\n          setTimeout(() => {\n            this.cdr.detectChanges();\n            this.logger.debug(\n              'MessageChat',\n              'View refreshed after loading voice messages'\n            );\n          }, 100);\n        }\n      },\n      error: (error) => {\n        this.logger.error(\n          'MessageChat',\n          'Error loading voice messages:',\n          error\n        );\n        // Ne pas bloquer l'expérience utilisateur si le chargement des messages vocaux échoue\n      },\n    });\n\n    this.subscriptions.add(sub);\n  }\n\n  /**\n   * Gère les erreurs et les affiche à l'utilisateur\n   * @param message Message d'erreur à afficher\n   * @param error Objet d'erreur\n   */\n  private handleError(message: string, error: any): void {\n    this.logger.error('MessageChat', message, error);\n    this.loading = false;\n    this.error = error;\n    this.toastService.showError(message);\n  }\n\n  // logique FileService\n  getFileIcon(mimeType?: string): string {\n    if (!mimeType) return 'fa-file';\n    if (mimeType.startsWith('image/')) return 'fa-image';\n    if (mimeType.includes('pdf')) return 'fa-file-pdf';\n    if (mimeType.includes('word') || mimeType.includes('msword'))\n      return 'fa-file-word';\n    if (mimeType.includes('excel')) return 'fa-file-excel';\n    if (mimeType.includes('powerpoint')) return 'fa-file-powerpoint';\n    if (mimeType.includes('audio')) return 'fa-file-audio';\n    if (mimeType.includes('video')) return 'fa-file-video';\n    if (mimeType.includes('zip') || mimeType.includes('compressed'))\n      return 'fa-file-archive';\n    return 'fa-file';\n  }\n  getFileType(mimeType?: string): string {\n    if (!mimeType) return 'File';\n\n    const typeMap: Record<string, string> = {\n      'image/': 'Image',\n      'application/pdf': 'PDF',\n      'application/msword': 'Word Doc',\n      'application/vnd.openxmlformats-officedocument.wordprocessingml.document':\n        'Word Doc',\n      'application/vnd.ms-excel': 'Excel',\n      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':\n        'Excel',\n      'application/vnd.ms-powerpoint': 'PowerPoint',\n      'application/vnd.openxmlformats-officedocument.presentationml.presentation':\n        'PowerPoint',\n      'audio/': 'Audio',\n      'video/': 'Video',\n      'application/zip': 'ZIP Archive',\n      'application/x-rar-compressed': 'RAR Archive',\n    };\n    for (const [key, value] of Object.entries(typeMap)) {\n      if (mimeType.includes(key)) return value;\n    }\n    return 'File';\n  }\n\n  private handleConversationLoaded(conversation: Conversation): void {\n    this.logger.info(\n      'MessageChat',\n      `Handling loaded conversation: ${conversation.id}`\n    );\n    this.logger.debug(\n      'MessageChat',\n      `Conversation has ${conversation?.messages?.length || 0} messages and ${\n        conversation?.participants?.length || 0\n      } participants`\n    );\n\n    // Log détaillé des messages pour le débogage\n    if (conversation?.messages && conversation.messages.length > 0) {\n      this.logger.debug(\n        'MessageChat',\n        `First message details: id=${\n          conversation.messages[0].id\n        }, content=${conversation.messages[0].content?.substring(\n          0,\n          20\n        )}, sender=${conversation.messages[0].sender?.username}`\n      );\n    }\n\n    this.conversation = conversation;\n\n    // Si la conversation n'a pas de messages, initialiser un tableau vide\n    if (!conversation?.messages || conversation.messages.length === 0) {\n      this.logger.debug('MessageChat', 'No messages found in conversation');\n\n      // Récupérer les participants\n      this.otherParticipant =\n        conversation?.participants?.find(\n          (p) => p.id !== this.currentUserId && p._id !== this.currentUserId\n        ) || null;\n\n      // Initialiser un tableau vide pour les messages\n      this.messages = [];\n\n      this.logger.debug('MessageChat', 'Initialized empty messages array');\n    } else {\n      // Récupérer les messages de la conversation\n      const conversationMessages = [...(conversation?.messages || [])];\n\n      // Trier les messages par date (du plus ancien au plus récent)\n      conversationMessages.sort((a, b) => {\n        const timeA =\n          a.timestamp instanceof Date\n            ? a.timestamp.getTime()\n            : new Date(a.timestamp as string).getTime();\n        const timeB =\n          b.timestamp instanceof Date\n            ? b.timestamp.getTime()\n            : new Date(b.timestamp as string).getTime();\n        return timeA - timeB;\n      });\n\n      // Log détaillé pour comprendre la structure des messages\n      if (conversationMessages.length > 0) {\n        const firstMessage = conversationMessages[0];\n        this.logger.debug(\n          'MessageChat',\n          `Message structure: sender.id=${firstMessage.sender?.id}, sender._id=${firstMessage.sender?._id}, senderId=${firstMessage.senderId}, receiver.id=${firstMessage.receiver?.id}, receiver._id=${firstMessage.receiver?._id}, receiverId=${firstMessage.receiverId}`\n        );\n      }\n\n      // Utiliser directement tous les messages triés sans filtrage supplémentaire\n      this.messages = conversationMessages;\n\n      this.logger.debug(\n        'MessageChat',\n        `Using all ${this.messages.length} messages from conversation`\n      );\n\n      this.logger.debug(\n        'MessageChat',\n        `Using ${conversationMessages.length} messages from conversation, showing last ${this.messages.length}`\n      );\n    }\n\n    this.otherParticipant =\n      conversation?.participants?.find(\n        (p) => p.id !== this.currentUserId && p._id !== this.currentUserId\n      ) || null;\n\n    this.logger.debug(\n      'MessageChat',\n      `Other participant identified: ${\n        this.otherParticipant?.username || 'Unknown'\n      }`\n    );\n\n    this.loading = false;\n    setTimeout(() => this.scrollToBottom(), 100);\n\n    this.logger.debug('MessageChat', `Marking unread messages as read`);\n    this.markMessagesAsRead();\n\n    if (this.conversation?.id) {\n      this.logger.debug(\n        'MessageChat',\n        `Setting up subscriptions for conversation: ${this.conversation.id}`\n      );\n      this.subscribeToConversationUpdates(this.conversation.id);\n      this.subscribeToNewMessages(this.conversation.id);\n      this.subscribeToTypingIndicators(this.conversation.id);\n    }\n\n    this.logger.info('MessageChat', `Conversation loaded successfully`);\n  }\n\n  private subscribeToConversationUpdates(conversationId: string): void {\n    const sub = this.MessageService.subscribeToConversationUpdates(\n      conversationId\n    ).subscribe({\n      next: (updatedConversation) => {\n        this.conversation = updatedConversation;\n        this.messages = updatedConversation.messages\n          ? [...updatedConversation.messages]\n          : [];\n        this.scrollToBottom();\n      },\n      error: (error) => {\n        this.toastService.showError('Connection to conversation updates lost');\n      },\n    });\n    this.subscriptions.add(sub);\n  }\n\n  private subscribeToNewMessages(conversationId: string): void {\n    const sub = this.MessageService.subscribeToNewMessages(\n      conversationId\n    ).subscribe({\n      next: (newMessage) => {\n        if (newMessage?.conversationId === this.conversation?.id) {\n          // Ajouter le nouveau message à la liste complète\n          this.messages = [...this.messages, newMessage].sort((a, b) => {\n            const timeA =\n              a.timestamp instanceof Date\n                ? a.timestamp.getTime()\n                : new Date(a.timestamp as string).getTime();\n            const timeB =\n              b.timestamp instanceof Date\n                ? b.timestamp.getTime()\n                : new Date(b.timestamp as string).getTime();\n            return timeA - timeB; // Tri par ordre croissant pour l'affichage\n          });\n\n          this.logger.debug(\n            'MessageChat',\n            `Added new message, now showing ${this.messages.length} messages`\n          );\n\n          setTimeout(() => this.scrollToBottom(), 100);\n\n          // Marquer le message comme lu s'il vient d'un autre utilisateur\n          if (\n            newMessage.sender?.id !== this.currentUserId &&\n            newMessage.sender?._id !== this.currentUserId\n          ) {\n            if (newMessage.id) {\n              this.MessageService.markMessageAsRead(newMessage.id).subscribe();\n            }\n          }\n        }\n      },\n      error: (error) => {\n        this.toastService.showError('Connection to new messages lost');\n      },\n    });\n    this.subscriptions.add(sub);\n  }\n\n  private subscribeToTypingIndicators(conversationId: string): void {\n    const sub = this.MessageService.subscribeToTypingIndicator(\n      conversationId\n    ).subscribe({\n      next: (event) => {\n        if (event.userId !== this.currentUserId) {\n          this.isTyping = event.isTyping;\n          if (this.isTyping) {\n            clearTimeout(this.typingTimeout);\n            this.typingTimeout = setTimeout(() => {\n              this.isTyping = false;\n            }, 2000);\n          }\n        }\n      },\n    });\n    this.subscriptions.add(sub);\n  }\n\n  private markMessagesAsRead(): void {\n    const unreadMessages = this.messages.filter(\n      (msg) =>\n        !msg.isRead &&\n        (msg.receiver?.id === this.currentUserId ||\n          msg.receiver?._id === this.currentUserId)\n    );\n\n    unreadMessages.forEach((msg) => {\n      if (msg.id) {\n        const sub = this.MessageService.markMessageAsRead(msg.id).subscribe({\n          error: (error) => {\n            this.logger.error(\n              'MessageChat',\n              'Error marking message as read:',\n              error\n            );\n          },\n        });\n        this.subscriptions.add(sub);\n      }\n    });\n  }\n\n  onFileSelected(event: any): void {\n    const file = event.target.files[0];\n    if (!file) return;\n\n    // Validate file size (e.g., 5MB max)\n    if (file.size > 5 * 1024 * 1024) {\n      this.toastService.showError('File size should be less than 5MB');\n      return;\n    }\n\n    // Validate file type\n    const validTypes = [\n      'image/jpeg',\n      'image/png',\n      'image/gif',\n      'application/pdf',\n      'application/msword',\n      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',\n    ];\n    if (!validTypes.includes(file.type)) {\n      this.toastService.showError(\n        'Invalid file type. Only images, PDFs and Word docs are allowed'\n      );\n      return;\n    }\n\n    this.selectedFile = file;\n    const reader = new FileReader();\n    reader.onload = () => {\n      this.previewUrl = reader.result;\n    };\n    reader.readAsDataURL(file);\n  }\n\n  removeAttachment(): void {\n    this.selectedFile = null;\n    this.previewUrl = null;\n    if (this.fileInput?.nativeElement) {\n      this.fileInput.nativeElement.value = '';\n    }\n  }\n\n  private typingTimer: any;\n  private isCurrentlyTyping = false;\n  private readonly TYPING_DELAY = 500; // Délai en ms avant d'envoyer l'événement de frappe\n  private readonly TYPING_TIMEOUT = 3000; // Délai en ms avant d'arrêter l'indicateur de frappe\n\n  /**\n   * Gère l'événement de frappe de l'utilisateur\n   * Envoie un indicateur de frappe avec un délai pour éviter trop de requêtes\n   */\n  onTyping(): void {\n    if (!this.conversation?.id || !this.currentUserId) {\n      return;\n    }\n\n    // Stocker l'ID de conversation pour éviter les erreurs TypeScript\n    const conversationId = this.conversation.id;\n\n    // Annuler le timer précédent\n    clearTimeout(this.typingTimer);\n\n    // Si l'utilisateur n'est pas déjà en train de taper, envoyer l'événement immédiatement\n    if (!this.isCurrentlyTyping) {\n      this.isCurrentlyTyping = true;\n      this.logger.debug('MessageChat', 'Starting typing indicator');\n\n      this.MessageService.startTyping(conversationId).subscribe({\n        next: () => {\n          this.logger.debug(\n            'MessageChat',\n            'Typing indicator started successfully'\n          );\n        },\n        error: (error) => {\n          this.logger.error(\n            'MessageChat',\n            'Error starting typing indicator:',\n            error\n          );\n        },\n      });\n    }\n\n    // Définir un timer pour arrêter l'indicateur de frappe après un délai d'inactivité\n    this.typingTimer = setTimeout(() => {\n      if (this.isCurrentlyTyping) {\n        this.isCurrentlyTyping = false;\n        this.logger.debug(\n          'MessageChat',\n          'Stopping typing indicator due to inactivity'\n        );\n\n        this.MessageService.stopTyping(conversationId).subscribe({\n          next: () => {\n            this.logger.debug(\n              'MessageChat',\n              'Typing indicator stopped successfully'\n            );\n          },\n          error: (error) => {\n            this.logger.error(\n              'MessageChat',\n              'Error stopping typing indicator:',\n              error\n            );\n          },\n        });\n      }\n    }, this.TYPING_TIMEOUT);\n  }\n\n  /**\n   * Affiche ou masque le sélecteur de thème\n   */\n  toggleThemeSelector(): void {\n    this.showThemeSelector = !this.showThemeSelector;\n\n    // Fermer le sélecteur de thème lorsqu'on clique ailleurs\n    if (this.showThemeSelector) {\n      setTimeout(() => {\n        const clickHandler = (event: MouseEvent) => {\n          const target = event.target as HTMLElement;\n          if (!target.closest('.theme-selector')) {\n            this.showThemeSelector = false;\n            document.removeEventListener('click', clickHandler);\n          }\n        };\n        document.addEventListener('click', clickHandler);\n      }, 0);\n    }\n  }\n\n  /**\n   * Change le thème de la conversation\n   * @param theme Nom du thème à appliquer\n   */\n  changeTheme(theme: string): void {\n    this.selectedTheme = theme;\n    this.showThemeSelector = false;\n\n    // Sauvegarder le thème dans le localStorage pour le conserver entre les sessions\n    localStorage.setItem('chat-theme', theme);\n\n    this.logger.debug('MessageChat', `Theme changed to: ${theme}`);\n  }\n\n  sendMessage(): void {\n    this.logger.info('MessageChat', `Attempting to send message`);\n\n    // Vérifier l'authentification\n    const token = localStorage.getItem('token');\n    this.logger.debug(\n      'MessageChat',\n      `Authentication check: token=${!!token}, userId=${this.currentUserId}`\n    );\n\n    if (\n      (this.messageForm.invalid && !this.selectedFile) ||\n      !this.currentUserId ||\n      !this.otherParticipant?.id\n    ) {\n      this.logger.warn(\n        'MessageChat',\n        `Cannot send message: form invalid or missing user IDs`\n      );\n      return;\n    }\n\n    // Arrêter l'indicateur de frappe lorsqu'un message est envoyé\n    this.stopTypingIndicator();\n\n    const content = this.messageForm.get('content')?.value;\n\n    // Créer un message temporaire pour l'affichage immédiat (comme dans Facebook Messenger)\n    const tempMessage: Message = {\n      id: 'temp-' + new Date().getTime(),\n      content: content || '',\n      sender: {\n        id: this.currentUserId || '',\n        username: this.currentUsername,\n      },\n      receiver: {\n        id: this.otherParticipant.id,\n        username: this.otherParticipant.username || 'Recipient',\n      },\n      timestamp: new Date(),\n      isRead: false,\n      isPending: true, // Marquer comme en attente\n    };\n\n    // Si un fichier est sélectionné, ajouter l'aperçu au message temporaire\n    if (this.selectedFile) {\n      // Déterminer le type de fichier\n      let fileType = 'file';\n      if (this.selectedFile.type.startsWith('image/')) {\n        fileType = 'image';\n\n        // Pour les images, ajouter un aperçu immédiat\n        if (this.previewUrl) {\n          tempMessage.attachments = [\n            {\n              id: 'temp-attachment',\n              url: this.previewUrl ? this.previewUrl.toString() : '',\n              type: MessageType.IMAGE,\n              name: this.selectedFile.name,\n              size: this.selectedFile.size,\n            },\n          ];\n        }\n      }\n\n      // Définir le type de message en fonction du type de fichier\n      if (fileType === 'image') {\n        tempMessage.type = MessageType.IMAGE;\n      } else if (fileType === 'file') {\n        tempMessage.type = MessageType.FILE;\n      }\n    }\n\n    // Ajouter immédiatement le message temporaire à la liste\n    this.messages = [...this.messages, tempMessage];\n\n    // Réinitialiser le formulaire immédiatement pour une meilleure expérience utilisateur\n    const fileToSend = this.selectedFile; // Sauvegarder une référence\n    this.messageForm.reset();\n    this.removeAttachment();\n\n    // Forcer le défilement vers le bas immédiatement\n    setTimeout(() => this.scrollToBottom(true), 50);\n\n    // Maintenant, envoyer le message au serveur\n    this.isUploading = true;\n\n    const sendSub = this.MessageService.sendMessage(\n      this.otherParticipant.id,\n      content,\n      fileToSend || undefined,\n      MessageType.TEXT\n    ).subscribe({\n      next: (message) => {\n        this.logger.info(\n          'MessageChat',\n          `Message sent successfully: ${message?.id || 'unknown'}`\n        );\n\n        // Remplacer le message temporaire par le message réel\n        this.messages = this.messages.map((msg) =>\n          msg.id === tempMessage.id ? message : msg\n        );\n\n        this.isUploading = false;\n      },\n      error: (error) => {\n        this.logger.error('MessageChat', `Error sending message:`, error);\n\n        // Marquer le message temporaire comme échoué\n        this.messages = this.messages.map((msg) => {\n          if (msg.id === tempMessage.id) {\n            return {\n              ...msg,\n              isPending: false,\n              isError: true,\n            };\n          }\n          return msg;\n        });\n\n        this.isUploading = false;\n        this.toastService.showError('Failed to send message');\n      },\n    });\n\n    this.subscriptions.add(sendSub);\n  }\n\n  formatMessageTime(timestamp: string | Date | undefined): string {\n    if (!timestamp) {\n      return 'Unknown time';\n    }\n    try {\n      const date = timestamp instanceof Date ? timestamp : new Date(timestamp);\n      // Format heure:minute sans les secondes, comme dans l'image de référence\n      return date.toLocaleTimeString([], {\n        hour: '2-digit',\n        minute: '2-digit',\n        hour12: false,\n      });\n    } catch (error) {\n      this.logger.error('MessageChat', 'Error formatting message time:', error);\n      return 'Invalid time';\n    }\n  }\n\n  formatLastActive(lastActive: string | Date | undefined): string {\n    if (!lastActive) return 'Offline';\n    const lastActiveDate =\n      lastActive instanceof Date ? lastActive : new Date(lastActive);\n    const now = new Date();\n    const diffHours =\n      Math.abs(now.getTime() - lastActiveDate.getTime()) / (1000 * 60 * 60);\n\n    if (diffHours < 24) {\n      return `Active ${lastActiveDate.toLocaleTimeString([], {\n        hour: '2-digit',\n        minute: '2-digit',\n      })}`;\n    }\n    return `Active ${lastActiveDate.toLocaleDateString()}`;\n  }\n\n  formatMessageDate(timestamp: string | Date | undefined): string {\n    if (!timestamp) {\n      return 'Unknown date';\n    }\n\n    try {\n      const date = timestamp instanceof Date ? timestamp : new Date(timestamp);\n      const today = new Date();\n\n      // Format pour l'affichage comme dans l'image de référence\n      const options: Intl.DateTimeFormatOptions = {\n        weekday: 'short',\n        hour: '2-digit',\n        minute: '2-digit',\n      };\n\n      if (date.toDateString() === today.toDateString()) {\n        return date.toLocaleTimeString([], {\n          hour: '2-digit',\n          minute: '2-digit',\n        });\n      }\n\n      const yesterday = new Date(today);\n      yesterday.setDate(yesterday.getDate() - 1);\n\n      if (date.toDateString() === yesterday.toDateString()) {\n        return `LUN., ${date.toLocaleTimeString([], {\n          hour: '2-digit',\n          minute: '2-digit',\n        })}`;\n      }\n\n      // Format pour les autres jours (comme dans l'image)\n      const day = date\n        .toLocaleDateString('fr-FR', { weekday: 'short' })\n        .toUpperCase();\n      return `${day}., ${date.toLocaleTimeString([], {\n        hour: '2-digit',\n        minute: '2-digit',\n      })}`;\n    } catch (error) {\n      this.logger.error('MessageChat', 'Error formatting message date:', error);\n      return 'Invalid date';\n    }\n  }\n\n  shouldShowDateHeader(index: number): boolean {\n    if (index === 0) return true;\n\n    try {\n      const currentMsg = this.messages[index];\n      const prevMsg = this.messages[index - 1];\n\n      if (!currentMsg?.timestamp || !prevMsg?.timestamp) {\n        return true;\n      }\n\n      const currentDate = this.getDateFromTimestamp(currentMsg.timestamp);\n      const prevDate = this.getDateFromTimestamp(prevMsg.timestamp);\n\n      return currentDate !== prevDate;\n    } catch (error) {\n      this.logger.error('MessageChat', 'Error checking date header:', error);\n      return false;\n    }\n  }\n\n  private getDateFromTimestamp(timestamp: string | Date | undefined): string {\n    if (!timestamp) {\n      return 'unknown-date';\n    }\n\n    try {\n      return (\n        timestamp instanceof Date ? timestamp : new Date(timestamp)\n      ).toDateString();\n    } catch (error) {\n      this.logger.error(\n        'MessageChat',\n        'Error getting date from timestamp:',\n        error\n      );\n      return 'invalid-date';\n    }\n  }\n  getMessageType(message: Message | null | undefined): MessageType {\n    if (!message) {\n      return MessageType.TEXT;\n    }\n\n    try {\n      // Vérifier d'abord le type de message explicite\n      if (message.type) {\n        // Convertir les types en minuscules en leurs équivalents en majuscules\n        const msgType = message.type.toString();\n        if (msgType === 'text' || msgType === 'TEXT') {\n          return MessageType.TEXT;\n        } else if (msgType === 'image' || msgType === 'IMAGE') {\n          return MessageType.IMAGE;\n        } else if (msgType === 'file' || msgType === 'FILE') {\n          return MessageType.FILE;\n        } else if (msgType === 'audio' || msgType === 'AUDIO') {\n          return MessageType.AUDIO;\n        } else if (msgType === 'video' || msgType === 'VIDEO') {\n          return MessageType.VIDEO;\n        } else if (msgType === 'system' || msgType === 'SYSTEM') {\n          return MessageType.SYSTEM;\n        }\n      }\n\n      // Ensuite, vérifier les pièces jointes\n      if (message.attachments?.length) {\n        const attachment = message.attachments[0];\n        if (attachment && attachment.type) {\n          const attachmentTypeStr = attachment.type.toString();\n\n          // Gérer les différentes formes de types d'attachements\n          if (attachmentTypeStr === 'image' || attachmentTypeStr === 'IMAGE') {\n            return MessageType.IMAGE;\n          } else if (\n            attachmentTypeStr === 'file' ||\n            attachmentTypeStr === 'FILE'\n          ) {\n            return MessageType.FILE;\n          } else if (\n            attachmentTypeStr === 'audio' ||\n            attachmentTypeStr === 'AUDIO'\n          ) {\n            return MessageType.AUDIO;\n          } else if (\n            attachmentTypeStr === 'video' ||\n            attachmentTypeStr === 'VIDEO'\n          ) {\n            return MessageType.VIDEO;\n          }\n        }\n\n        // Type par défaut pour les pièces jointes\n        return MessageType.FILE;\n      }\n\n      // Type par défaut\n      return MessageType.TEXT;\n    } catch (error) {\n      this.logger.error('MessageChat', 'Error getting message type:', error);\n      return MessageType.TEXT;\n    }\n  }\n\n  // Méthode auxiliaire pour vérifier si un message contient une image\n  hasImage(message: Message | null | undefined): boolean {\n    if (!message || !message.attachments || message.attachments.length === 0) {\n      return false;\n    }\n\n    const attachment = message.attachments[0];\n    if (!attachment || !attachment.type) {\n      return false;\n    }\n\n    const type = attachment.type.toString();\n    return type === 'IMAGE' || type === 'image';\n  }\n\n  /**\n   * Vérifie si le message est un message vocal\n   */\n  isVoiceMessage(message: Message | null | undefined): boolean {\n    if (!message) {\n      return false;\n    }\n\n    // Vérifier le type du message\n    if (\n      message.type === MessageType.VOICE_MESSAGE ||\n      message.type === MessageType.VOICE_MESSAGE_LOWER\n    ) {\n      return true;\n    }\n\n    // Vérifier les pièces jointes\n    if (message.attachments && message.attachments.length > 0) {\n      return message.attachments.some((att) => {\n        const type = att.type?.toString();\n        return (\n          type === 'VOICE_MESSAGE' ||\n          type === 'voice_message' ||\n          (message.metadata?.isVoiceMessage &&\n            (type === 'AUDIO' || type === 'audio'))\n        );\n      });\n    }\n\n    // Vérifier les métadonnées\n    return !!message.metadata?.isVoiceMessage;\n  }\n\n  /**\n   * Récupère l'URL du message vocal\n   */\n  getVoiceMessageUrl(message: Message | null | undefined): string {\n    if (!message || !message.attachments || message.attachments.length === 0) {\n      return '';\n    }\n\n    // Chercher une pièce jointe de type message vocal ou audio\n    const voiceAttachment = message.attachments.find((att) => {\n      const type = att.type?.toString();\n      return (\n        type === 'VOICE_MESSAGE' ||\n        type === 'voice_message' ||\n        type === 'AUDIO' ||\n        type === 'audio'\n      );\n    });\n\n    return voiceAttachment?.url || '';\n  }\n\n  /**\n   * Récupère la durée du message vocal\n   */\n  getVoiceMessageDuration(message: Message | null | undefined): number {\n    if (!message) {\n      return 0;\n    }\n\n    // Essayer d'abord de récupérer la durée depuis les métadonnées\n    if (message.metadata?.duration) {\n      return message.metadata.duration;\n    }\n\n    // Sinon, essayer de récupérer depuis les pièces jointes\n    if (message.attachments && message.attachments.length > 0) {\n      const voiceAttachment = message.attachments.find((att) => {\n        const type = att.type?.toString();\n        return (\n          type === 'VOICE_MESSAGE' ||\n          type === 'voice_message' ||\n          type === 'AUDIO' ||\n          type === 'audio'\n        );\n      });\n\n      if (voiceAttachment && voiceAttachment.duration) {\n        return voiceAttachment.duration;\n      }\n    }\n\n    return 0;\n  }\n\n  // Méthode pour obtenir l'URL de l'image en toute sécurité\n  getImageUrl(message: Message | null | undefined): string {\n    if (!message || !message.attachments || message.attachments.length === 0) {\n      return '';\n    }\n\n    const attachment = message.attachments[0];\n    return attachment?.url || '';\n  }\n\n  getMessageTypeClass(message: Message | null | undefined): string {\n    if (!message) {\n      return 'bg-gray-100 rounded-lg px-4 py-2';\n    }\n\n    try {\n      const isCurrentUser =\n        message.sender?.id === this.currentUserId ||\n        message.sender?._id === this.currentUserId ||\n        message.senderId === this.currentUserId;\n\n      // Utiliser une couleur plus foncée pour les messages de l'utilisateur actuel (à droite)\n      // et une couleur plus claire pour les messages des autres utilisateurs (à gauche)\n      // Couleurs et forme adaptées exactement à l'image de référence mobile\n      const baseClass = isCurrentUser\n        ? 'bg-blue-500 text-white rounded-2xl rounded-br-sm'\n        : 'bg-gray-200 text-gray-800 rounded-2xl rounded-bl-sm';\n\n      const messageType = this.getMessageType(message);\n\n      // Vérifier si le message contient une image\n      if (message.attachments && message.attachments.length > 0) {\n        const attachment = message.attachments[0];\n        if (attachment && attachment.type) {\n          const attachmentTypeStr = attachment.type.toString();\n          if (attachmentTypeStr === 'IMAGE' || attachmentTypeStr === 'image') {\n            // Pour les images, on utilise un style sans bordure\n            return `p-1 max-w-xs`;\n          } else if (\n            attachmentTypeStr === 'FILE' ||\n            attachmentTypeStr === 'file'\n          ) {\n            return `${baseClass} p-3`;\n          }\n        }\n      }\n\n      // Vérifier le type de message\n      if (\n        messageType === MessageType.IMAGE ||\n        messageType === MessageType.IMAGE_LOWER\n      ) {\n        // Pour les images, on utilise un style sans bordure\n        return `p-1 max-w-xs`;\n      } else if (\n        messageType === MessageType.FILE ||\n        messageType === MessageType.FILE_LOWER\n      ) {\n        return `${baseClass} p-3`;\n      }\n\n      // Type par défaut (texte)\n      return `${baseClass} px-4 py-3 whitespace-normal break-words min-w-[120px]`;\n    } catch (error) {\n      this.logger.error(\n        'MessageChat',\n        'Error getting message type class:',\n        error\n      );\n      return 'bg-gray-100 rounded-lg px-4 py-2 whitespace-normal break-words';\n    }\n  }\n\n  // La méthode ngAfterViewChecked est implémentée plus bas dans le fichier\n\n  // Méthode pour détecter le défilement vers le haut et charger plus de messages\n  onScroll(event: any): void {\n    const container = event.target;\n    const scrollTop = container.scrollTop;\n\n    // Si on est proche du haut de la liste et qu'on n'est pas déjà en train de charger\n    if (\n      scrollTop < 50 &&\n      !this.isLoadingMore &&\n      this.conversation?.id &&\n      this.hasMoreMessages\n    ) {\n      // Afficher un indicateur de chargement en haut de la liste\n      this.showLoadingIndicator();\n\n      // Sauvegarder la hauteur actuelle et la position des messages\n      const oldScrollHeight = container.scrollHeight;\n      const firstVisibleMessage = this.getFirstVisibleMessage();\n\n      // Marquer comme chargement en cours\n      this.isLoadingMore = true;\n\n      // Charger plus de messages avec un délai réduit\n      this.loadMoreMessages();\n\n      // Maintenir la position de défilement pour que l'utilisateur reste au même endroit\n      // en utilisant le premier message visible comme ancre\n      requestAnimationFrame(() => {\n        const preserveScrollPosition = () => {\n          if (firstVisibleMessage) {\n            const messageElement = this.findMessageElement(\n              firstVisibleMessage.id\n            );\n            if (messageElement) {\n              // Faire défiler jusqu'à l'élément qui était visible avant\n              messageElement.scrollIntoView({ block: 'center' });\n            } else {\n              // Fallback: utiliser la différence de hauteur\n              const newScrollHeight = container.scrollHeight;\n              const scrollDiff = newScrollHeight - oldScrollHeight;\n              container.scrollTop = scrollTop + scrollDiff;\n            }\n          }\n\n          // Masquer l'indicateur de chargement\n          this.hideLoadingIndicator();\n        };\n\n        // Attendre que le DOM soit mis à jour\n        setTimeout(preserveScrollPosition, 100);\n      });\n    }\n  }\n\n  // Méthode pour trouver le premier message visible dans la vue\n  private getFirstVisibleMessage(): Message | null {\n    if (!this.messagesContainer?.nativeElement || !this.messages.length)\n      return null;\n\n    const container = this.messagesContainer.nativeElement;\n    const messageElements = container.querySelectorAll('.message-item');\n\n    for (let i = 0; i < messageElements.length; i++) {\n      const element = messageElements[i];\n      const rect = element.getBoundingClientRect();\n\n      // Si l'élément est visible dans la vue\n      if (rect.top >= 0 && rect.bottom <= container.clientHeight) {\n        const messageId = element.getAttribute('data-message-id');\n        return this.messages.find((m) => m.id === messageId) || null;\n      }\n    }\n\n    return null;\n  }\n\n  // Méthode pour trouver un élément de message par ID\n  private findMessageElement(\n    messageId: string | undefined\n  ): HTMLElement | null {\n    if (!this.messagesContainer?.nativeElement || !messageId) return null;\n    return this.messagesContainer.nativeElement.querySelector(\n      `[data-message-id=\"${messageId}\"]`\n    );\n  }\n\n  // Afficher un indicateur de chargement en haut de la liste\n  private showLoadingIndicator(): void {\n    // Créer l'indicateur s'il n'existe pas déjà\n    if (!document.getElementById('message-loading-indicator')) {\n      const indicator = document.createElement('div');\n      indicator.id = 'message-loading-indicator';\n      indicator.className = 'text-center py-2 text-gray-500 text-sm';\n      indicator.innerHTML =\n        '<i class=\"fas fa-spinner fa-spin mr-2\"></i> Loading older messages...';\n\n      if (this.messagesContainer?.nativeElement) {\n        this.messagesContainer.nativeElement.prepend(indicator);\n      }\n    }\n  }\n\n  // Masquer l'indicateur de chargement\n  private hideLoadingIndicator(): void {\n    const indicator = document.getElementById('message-loading-indicator');\n    if (indicator && indicator.parentNode) {\n      indicator.parentNode.removeChild(indicator);\n    }\n  }\n\n  // Méthode pour charger plus de messages (style Facebook Messenger)\n  loadMoreMessages(): void {\n    if (this.isLoadingMore || !this.conversation?.id || !this.hasMoreMessages)\n      return;\n\n    // Marquer comme chargement en cours\n    this.isLoadingMore = true;\n\n    // Augmenter la page pour charger les messages plus anciens\n    this.currentPage++;\n\n    // Charger plus de messages depuis le serveur avec pagination\n    this.MessageService.getConversation(\n      this.conversation.id,\n      this.MAX_MESSAGES_TO_LOAD,\n      this.currentPage\n    ).subscribe({\n      next: (conversation) => {\n        if (\n          conversation &&\n          conversation.messages &&\n          conversation.messages.length > 0\n        ) {\n          // Sauvegarder les messages actuels\n          const oldMessages = [...this.messages];\n\n          // Créer un Set des IDs existants pour une recherche de doublons plus rapide\n          const existingIds = new Set(oldMessages.map((msg) => msg.id));\n\n          // Filtrer et trier les nouveaux messages plus efficacement\n          const newMessages = conversation.messages\n            .filter((msg) => !existingIds.has(msg.id))\n            .sort((a, b) => {\n              const timeA = new Date(a.timestamp as string).getTime();\n              const timeB = new Date(b.timestamp as string).getTime();\n              return timeA - timeB;\n            });\n\n          if (newMessages.length > 0) {\n            // Ajouter les nouveaux messages au début de la liste\n            this.messages = [...newMessages, ...oldMessages];\n\n            // Limiter le nombre total de messages pour éviter les problèmes de performance\n            if (this.messages.length > this.MAX_TOTAL_MESSAGES) {\n              this.messages = this.messages.slice(0, this.MAX_TOTAL_MESSAGES);\n            }\n\n            // Vérifier s'il y a plus de messages à charger\n            this.hasMoreMessages =\n              newMessages.length >= this.MAX_MESSAGES_TO_LOAD;\n          } else {\n            // Si aucun nouveau message n'est chargé, c'est qu'on a atteint le début de la conversation\n            this.hasMoreMessages = false;\n          }\n        } else {\n          this.hasMoreMessages = false;\n        }\n\n        // Désactiver le flag de chargement après un court délai\n        // pour permettre au DOM de se mettre à jour\n        setTimeout(() => {\n          this.isLoadingMore = false;\n        }, 200);\n      },\n      error: (error) => {\n        this.logger.error('MessageChat', 'Error loading more messages:', error);\n        this.isLoadingMore = false;\n        this.hideLoadingIndicator();\n        this.toastService.showError('Failed to load more messages');\n      },\n    });\n  }\n\n  // Méthode utilitaire pour comparer les timestamps\n  private isSameTimestamp(\n    timestamp1: string | Date | undefined,\n    timestamp2: string | Date | undefined\n  ): boolean {\n    if (!timestamp1 || !timestamp2) return false;\n\n    try {\n      const time1 =\n        timestamp1 instanceof Date\n          ? timestamp1.getTime()\n          : new Date(timestamp1 as string).getTime();\n      const time2 =\n        timestamp2 instanceof Date\n          ? timestamp2.getTime()\n          : new Date(timestamp2 as string).getTime();\n      return Math.abs(time1 - time2) < 1000; // Tolérance d'une seconde\n    } catch (error) {\n      return false;\n    }\n  }\n\n  scrollToBottom(force: boolean = false): void {\n    try {\n      if (!this.messagesContainer?.nativeElement) return;\n\n      // Utiliser requestAnimationFrame pour s'assurer que le DOM est prêt\n      requestAnimationFrame(() => {\n        const container = this.messagesContainer.nativeElement;\n        const isScrolledToBottom =\n          container.scrollHeight - container.clientHeight <=\n          container.scrollTop + 150;\n\n        // Faire défiler vers le bas si:\n        // - force est true (pour les nouveaux messages envoyés par l'utilisateur)\n        // - ou si l'utilisateur est déjà proche du bas\n        if (force || isScrolledToBottom) {\n          // Utiliser une animation fluide pour le défilement (comme dans Messenger)\n          container.scrollTo({\n            top: container.scrollHeight,\n            behavior: 'smooth',\n          });\n        }\n      });\n    } catch (err) {\n      this.logger.error('MessageChat', 'Error scrolling to bottom:', err);\n    }\n  }\n\n  // Méthode pour ouvrir l'image en plein écran (style Messenger)\n  /**\n   * Active/désactive l'enregistrement vocal\n   */\n  toggleVoiceRecording(): void {\n    this.isRecordingVoice = !this.isRecordingVoice;\n\n    if (!this.isRecordingVoice) {\n      // Si on désactive l'enregistrement, réinitialiser la durée\n      this.voiceRecordingDuration = 0;\n    }\n  }\n\n  /**\n   * Gère la fin de l'enregistrement vocal\n   * @param audioBlob Blob audio enregistré\n   */\n  onVoiceRecordingComplete(audioBlob: Blob): void {\n    this.logger.debug(\n      'MessageChat',\n      'Voice recording complete, size:',\n      audioBlob.size\n    );\n\n    if (!this.conversation?.id && !this.otherParticipant?.id) {\n      this.toastService.showError('No conversation or recipient selected');\n      this.isRecordingVoice = false;\n      return;\n    }\n\n    // Récupérer l'ID du destinataire\n    const receiverId = this.otherParticipant?.id || '';\n\n    // Envoyer le message vocal\n    this.MessageService.sendVoiceMessage(\n      receiverId,\n      audioBlob,\n      this.conversation?.id,\n      this.voiceRecordingDuration\n    ).subscribe({\n      next: (message) => {\n        this.logger.debug('MessageChat', 'Voice message sent:', message);\n        this.isRecordingVoice = false;\n        this.voiceRecordingDuration = 0;\n        this.scrollToBottom(true);\n      },\n      error: (error) => {\n        this.logger.error('MessageChat', 'Error sending voice message:', error);\n        this.toastService.showError('Failed to send voice message');\n        this.isRecordingVoice = false;\n      },\n    });\n  }\n\n  /**\n   * Gère l'annulation de l'enregistrement vocal\n   */\n  onVoiceRecordingCancelled(): void {\n    this.logger.debug('MessageChat', 'Voice recording cancelled');\n    this.isRecordingVoice = false;\n    this.voiceRecordingDuration = 0;\n  }\n\n  /**\n   * Ouvre une image en plein écran (méthode conservée pour compatibilité)\n   * @param imageUrl URL de l'image à afficher\n   */\n  openImageFullscreen(imageUrl: string): void {\n    // Ouvrir l'image dans un nouvel onglet\n    window.open(imageUrl, '_blank');\n    this.logger.debug('MessageChat', `Image opened in new tab: ${imageUrl}`);\n  }\n\n  /**\n   * Détecte les changements après chaque vérification de la vue\n   * Cela permet de s'assurer que les messages vocaux sont correctement affichés\n   * et que le défilement est maintenu\n   */\n  ngAfterViewChecked(): void {\n    // Faire défiler vers le bas si nécessaire\n    this.scrollToBottom();\n\n    // Forcer la détection des changements pour les messages vocaux\n    // Cela garantit que les messages vocaux sont correctement affichés même après avoir quitté la conversation\n    if (this.messages.some((msg) => msg.type === MessageType.VOICE_MESSAGE)) {\n      // Utiliser setTimeout pour éviter l'erreur ExpressionChangedAfterItHasBeenCheckedError\n      setTimeout(() => {\n        this.cdr.detectChanges();\n      }, 0);\n    }\n  }\n\n  /**\n   * Arrête l'indicateur de frappe\n   */\n  private stopTypingIndicator(): void {\n    if (this.isCurrentlyTyping && this.conversation?.id) {\n      this.isCurrentlyTyping = false;\n      clearTimeout(this.typingTimer);\n\n      this.logger.debug('MessageChat', 'Stopping typing indicator');\n\n      // Utiliser l'opérateur de chaînage optionnel pour éviter les erreurs TypeScript\n      const conversationId = this.conversation?.id;\n      if (conversationId) {\n        this.MessageService.stopTyping(conversationId).subscribe({\n          next: () => {\n            this.logger.debug(\n              'MessageChat',\n              'Typing indicator stopped successfully'\n            );\n          },\n          error: (error) => {\n            this.logger.error(\n              'MessageChat',\n              'Error stopping typing indicator:',\n              error\n            );\n          },\n        });\n      }\n    }\n  }\n\n  ngOnDestroy(): void {\n    // Arrêter l'indicateur de frappe lorsque l'utilisateur quitte la conversation\n    this.stopTypingIndicator();\n\n    this.subscriptions.unsubscribe();\n    clearTimeout(this.typingTimeout);\n  }\n\n  /**\n   * Navigue vers la liste des conversations\n   */\n  goBackToConversations(): void {\n    this.router.navigate(['/messages/conversations']);\n  }\n\n  /**\n   * Bascule l'affichage du sélecteur d'émojis\n   */\n  toggleEmojiPicker(): void {\n    this.showEmojiPicker = !this.showEmojiPicker;\n    if (this.showEmojiPicker) {\n      this.showThemeSelector = false;\n    }\n  }\n\n  /**\n   * Insère un emoji dans le champ de message\n   * @param emoji Emoji à insérer\n   */\n  insertEmoji(emoji: string): void {\n    const control = this.messageForm.get('content');\n    if (control) {\n      const currentValue = control.value || '';\n      control.setValue(currentValue + emoji);\n      control.markAsDirty();\n      // Garder le focus sur le champ de saisie\n      setTimeout(() => {\n        const inputElement = document.querySelector(\n          '.whatsapp-input-field'\n        ) as HTMLInputElement;\n        if (inputElement) {\n          inputElement.focus();\n        }\n      }, 0);\n    }\n  }\n\n  /**\n   * S'abonne aux notifications en temps réel\n   */\n  private subscribeToNotifications(): void {\n    // S'abonner aux nouvelles notifications\n    const notificationSub =\n      this.MessageService.subscribeToNewNotifications().subscribe({\n        next: (notification) => {\n          this.logger.debug(\n            'MessageChat',\n            `Nouvelle notification reçue: ${notification.type}`\n          );\n\n          // Si c'est une notification de message et que nous sommes dans la conversation concernée\n          if (\n            notification.type === 'NEW_MESSAGE' &&\n            notification.conversationId === this.conversation?.id\n          ) {\n            // Marquer automatiquement comme lue\n            if (notification.id) {\n              this.MessageService.markAsRead([notification.id]).subscribe();\n            }\n          }\n        },\n        error: (error) => {\n          this.logger.error(\n            'MessageChat',\n            'Erreur lors de la réception des notifications:',\n            error\n          );\n        },\n      });\n    this.subscriptions.add(notificationSub);\n\n    // S'abonner aux appels entrants\n    const callSub = this.MessageService.incomingCall$.subscribe({\n      next: (call) => {\n        if (call) {\n          this.logger.debug(\n            'MessageChat',\n            `Appel entrant de: ${call.caller.username}`\n          );\n          this.incomingCall = call;\n          this.showCallModal = true;\n\n          // Jouer la sonnerie\n          this.MessageService.play('ringtone');\n        } else {\n          this.showCallModal = false;\n          this.incomingCall = null;\n        }\n      },\n    });\n    this.subscriptions.add(callSub);\n  }\n\n  /**\n   * Initie un appel audio ou vidéo avec l'autre participant\n   * @param type Type d'appel (AUDIO ou VIDEO)\n   */\n  initiateCall(type: 'AUDIO' | 'VIDEO'): void {\n    if (!this.otherParticipant || !this.otherParticipant.id) {\n      console.error(\"Impossible d'initier un appel: participant invalide\");\n      return;\n    }\n\n    this.logger.info(\n      'MessageChat',\n      `Initiation d'un appel ${type} avec ${this.otherParticipant.username}`\n    );\n\n    // Utiliser le service d'appel pour initier l'appel\n    this.MessageService.initiateCall(\n      this.otherParticipant.id,\n      type === 'AUDIO' ? CallType.AUDIO : CallType.VIDEO,\n      this.conversation?.id\n    ).subscribe({\n      next: (call) => {\n        this.logger.info('MessageChat', 'Appel initié avec succès:', call);\n        // Ici, vous pourriez ouvrir une fenêtre d'appel ou rediriger vers une page d'appel\n      },\n      error: (error) => {\n        this.logger.error(\n          'MessageChat',\n          \"Erreur lors de l'initiation de l'appel:\",\n          error\n        );\n        this.toastService.showError(\n          \"Impossible d'initier l'appel. Veuillez réessayer.\"\n        );\n      },\n    });\n  }\n\n  /**\n   * Accepte un appel entrant\n   */\n  acceptCall(): void {\n    if (!this.incomingCall) {\n      this.logger.error('MessageChat', 'Aucun appel entrant à accepter');\n      return;\n    }\n\n    this.logger.info(\n      'MessageChat',\n      `Acceptation de l'appel de ${this.incomingCall.caller.username}`\n    );\n\n    this.MessageService.acceptCall(this.incomingCall.id).subscribe({\n      next: (call) => {\n        this.logger.info('MessageChat', 'Appel accepté avec succès:', call);\n        this.showCallModal = false;\n        // Ici, vous pourriez ouvrir une fenêtre d'appel ou rediriger vers une page d'appel\n      },\n      error: (error) => {\n        this.logger.error(\n          'MessageChat',\n          \"Erreur lors de l'acceptation de l'appel:\",\n          error\n        );\n        this.toastService.showError(\n          \"Impossible d'accepter l'appel. Veuillez réessayer.\"\n        );\n        this.showCallModal = false;\n        this.incomingCall = null;\n      },\n    });\n  }\n\n  /**\n   * Rejette un appel entrant\n   */\n  rejectCall(): void {\n    if (!this.incomingCall) {\n      this.logger.error('MessageChat', 'Aucun appel entrant à rejeter');\n      return;\n    }\n\n    this.logger.info(\n      'MessageChat',\n      `Rejet de l'appel de ${this.incomingCall.caller.username}`\n    );\n\n    this.MessageService.rejectCall(this.incomingCall.id).subscribe({\n      next: (call) => {\n        this.logger.info('MessageChat', 'Appel rejeté avec succès:', call);\n        this.showCallModal = false;\n        this.incomingCall = null;\n      },\n      error: (error) => {\n        this.logger.error(\n          'MessageChat',\n          \"Erreur lors du rejet de l'appel:\",\n          error\n        );\n        this.showCallModal = false;\n        this.incomingCall = null;\n      },\n    });\n  }\n\n  /**\n   * Termine un appel en cours\n   */\n  endCall(): void {\n    // Utiliser une variable pour stocker la dernière valeur de l'observable\n    let activeCall: any = null;\n\n    // S'abonner à l'observable pour obtenir la valeur actuelle\n    const sub = this.MessageService.activeCall$.subscribe((call) => {\n      activeCall = call;\n\n      if (!activeCall) {\n        this.logger.error('MessageChat', 'Aucun appel actif à terminer');\n        return;\n      }\n\n      this.logger.info('MessageChat', `Fin de l'appel`);\n\n      this.MessageService.endCall(activeCall.id).subscribe({\n        next: (call) => {\n          this.logger.info('MessageChat', 'Appel terminé avec succès:', call);\n        },\n        error: (error) => {\n          this.logger.error(\n            'MessageChat',\n            \"Erreur lors de la fin de l'appel:\",\n            error\n          );\n        },\n      });\n    });\n\n    // Se désabonner immédiatement après avoir obtenu la valeur\n    sub.unsubscribe();\n  }\n}\n", "<div\n  class=\"flex flex-col h-full bg-[#edf1f4] dark:bg-[#121212] relative overflow-hidden futuristic-chat-container dark\"\n  [ngClass]=\"selectedTheme\"\n>\n  <!-- Animations CSS -->\n  <style>\n    @keyframes borderFlow {\n      0% {\n        background-position: 0% 0%;\n      }\n      100% {\n        background-position: 200% 0%;\n      }\n    }\n\n    @keyframes ambientGlow {\n      0% {\n        opacity: 0;\n        transform: scale(0.95);\n      }\n      100% {\n        opacity: 0.5;\n        transform: scale(1.05);\n      }\n    }\n\n    @keyframes rotateHalo {\n      0% {\n        transform: rotate(0deg);\n      }\n      100% {\n        transform: rotate(360deg);\n      }\n    }\n\n    @keyframes pulseButton {\n      0% {\n        box-shadow: 0 0 10px rgba(0, 247, 255, 0.5),\n          0 0 20px rgba(0, 247, 255, 0.2);\n        transform: scale(1);\n      }\n      50% {\n        box-shadow: 0 0 15px rgba(0, 247, 255, 0.7),\n          0 0 30px rgba(0, 247, 255, 0.4);\n        transform: scale(1.05);\n      }\n      100% {\n        box-shadow: 0 0 10px rgba(0, 247, 255, 0.5),\n          0 0 20px rgba(0, 247, 255, 0.2);\n        transform: scale(1);\n      }\n    }\n  </style>\n  <!-- Background decorative elements - Grid pattern only -->\n  <div class=\"absolute inset-0 overflow-hidden pointer-events-none\">\n    <!-- Animated grid pattern -->\n    <div class=\"absolute inset-0 opacity-5 dark:opacity-[0.03]\">\n      <div class=\"h-full grid grid-cols-12\">\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-r border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n      </div>\n      <div class=\"w-full grid grid-rows-12\">\n        <div class=\"border-b border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-b border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-b border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-b border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-b border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-b border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-b border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-b border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-b border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-b border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n        <div class=\"border-b border-[#4f5fad] dark:border-[#6d78c9]\"></div>\n      </div>\n    </div>\n\n    <!-- Horizontal scan line effect for dark mode -->\n    <div class=\"absolute inset-0 opacity-0 dark:opacity-100 overflow-hidden\">\n      <div class=\"h-px w-full bg-[#00f7ff]/20 absolute animate-scan\"></div>\n    </div>\n  </div>\n\n  <!-- En-tête style WhatsApp -->\n  <div class=\"whatsapp-chat-header\">\n    <button (click)=\"goBackToConversations()\" class=\"whatsapp-action-button\">\n      <i class=\"fas fa-arrow-left\"></i>\n    </button>\n\n    <div class=\"whatsapp-user-info\">\n      <div class=\"whatsapp-avatar\">\n        <img\n          [src]=\"otherParticipant?.image || 'assets/images/default-avatar.png'\"\n          alt=\"User avatar\"\n        />\n        <span\n          *ngIf=\"otherParticipant?.isOnline\"\n          class=\"whatsapp-online-indicator\"\n        ></span>\n      </div>\n\n      <!-- Nom et statut style WhatsApp -->\n      <div *ngIf=\"otherParticipant\" class=\"whatsapp-user-details\">\n        <span class=\"whatsapp-username\">\n          {{ otherParticipant.username }}\n        </span>\n        <span class=\"whatsapp-status\">\n          {{\n            otherParticipant.isOnline\n              ? \"En ligne\"\n              : formatLastActive(otherParticipant.lastActive)\n          }}\n        </span>\n      </div>\n    </div>\n\n    <div class=\"whatsapp-actions\">\n      <!-- Bouton d'appel audio -->\n      <button class=\"whatsapp-action-button\" (click)=\"initiateCall('AUDIO')\">\n        <i class=\"fas fa-phone-alt\"></i>\n      </button>\n\n      <!-- Bouton d'appel vidéo -->\n      <button class=\"whatsapp-action-button\" (click)=\"initiateCall('VIDEO')\">\n        <i class=\"fas fa-video\"></i>\n      </button>\n\n      <!-- Sélecteur de thème -->\n      <div class=\"relative\">\n        <button (click)=\"toggleThemeSelector()\" class=\"whatsapp-action-button\">\n          <i class=\"fas fa-palette\"></i>\n        </button>\n\n        <!-- Menu déroulant des thèmes -->\n        <div\n          *ngIf=\"showThemeSelector\"\n          class=\"absolute right-0 mt-2 w-48 bg-white dark:bg-[#1e1e1e] rounded-lg shadow-lg z-50 border border-[#edf1f4]/50 dark:border-[#2a2a2a] overflow-hidden\"\n        >\n          <div\n            class=\"p-2 text-xs text-[#6d6870] dark:text-[#a0a0a0] border-b border-[#edf1f4]/50 dark:border-[#2a2a2a]\"\n          >\n            Choisir un thème\n          </div>\n          <div class=\"p-1\">\n            <a\n              href=\"javascript:void(0)\"\n              (click)=\"changeTheme('theme-default')\"\n              class=\"block w-full text-left px-3 py-2 text-sm rounded-md hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 transition-colors\"\n            >\n              <div class=\"flex items-center\">\n                <div class=\"w-4 h-4 rounded-full bg-[#4f5fad] mr-2\"></div>\n                <div>Par défaut</div>\n              </div>\n            </a>\n            <a\n              href=\"javascript:void(0)\"\n              (click)=\"changeTheme('theme-feminine')\"\n              class=\"block w-full text-left px-3 py-2 text-sm rounded-md hover:bg-[#ff6b9d]/10 dark:hover:bg-[#ff6b9d]/10 transition-colors\"\n            >\n              <div class=\"flex items-center\">\n                <div class=\"w-4 h-4 rounded-full bg-[#ff6b9d] mr-2\"></div>\n                <div>Rose</div>\n              </div>\n            </a>\n            <a\n              href=\"javascript:void(0)\"\n              (click)=\"changeTheme('theme-masculine')\"\n              class=\"block w-full text-left px-3 py-2 text-sm rounded-md hover:bg-[#3d85c6]/10 dark:hover:bg-[#3d85c6]/10 transition-colors\"\n            >\n              <div class=\"flex items-center\">\n                <div class=\"w-4 h-4 rounded-full bg-[#3d85c6] mr-2\"></div>\n                <div>Bleu</div>\n              </div>\n            </a>\n            <a\n              href=\"javascript:void(0)\"\n              (click)=\"changeTheme('theme-neutral')\"\n              class=\"block w-full text-left px-3 py-2 text-sm rounded-md hover:bg-[#6aa84f]/10 dark:hover:bg-[#6aa84f]/10 transition-colors\"\n            >\n              <div class=\"flex items-center\">\n                <div class=\"w-4 h-4 rounded-full bg-[#6aa84f] mr-2\"></div>\n                <div>Vert</div>\n              </div>\n            </a>\n          </div>\n        </div>\n      </div>\n\n      <button class=\"whatsapp-action-button\">\n        <i class=\"fas fa-ellipsis-v\"></i>\n      </button>\n    </div>\n  </div>\n\n  <!-- Zone de messages futuriste -->\n  <div\n    #messagesContainer\n    class=\"futuristic-messages-container\"\n    (scroll)=\"onScroll($event)\"\n  >\n    <!-- État de chargement (initial) -->\n    <div *ngIf=\"loading\" class=\"flex justify-center items-center h-full\">\n      <div class=\"flex flex-col items-center\">\n        <div class=\"relative\">\n          <div\n            class=\"w-12 h-12 border-4 border-[#4f5fad]/20 dark:border-[#6d78c9]/20 border-t-[#4f5fad] dark:border-t-[#6d78c9] rounded-full animate-spin mb-3\"\n          ></div>\n          <!-- Glow effect -->\n          <div\n            class=\"absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 blur-xl rounded-full transform scale-150 -z-10\"\n          ></div>\n        </div>\n        <div\n          class=\"text-[#6d6870] dark:text-[#a0a0a0] mt-3 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent font-medium\"\n        >\n          Initializing communication...\n        </div>\n      </div>\n    </div>\n\n    <!-- Indicateur de chargement de messages supplémentaires -->\n    <div\n      *ngIf=\"isLoadingMore\"\n      class=\"flex justify-center py-2 sticky top-0 z-10\"\n    >\n      <div\n        class=\"flex flex-col items-center backdrop-blur-sm bg-white/50 dark:bg-[#1e1e1e]/50 px-4 py-2 rounded-full shadow-sm\"\n      >\n        <div class=\"flex space-x-2 mb-1\">\n          <div\n            class=\"w-2 h-2 bg-[#4f5fad] dark:bg-[#6d78c9] rounded-full animate-pulse shadow-[0_0_5px_rgba(79,95,173,0.5)] dark:shadow-[0_0_5px_rgba(109,120,201,0.5)]\"\n          ></div>\n          <div\n            class=\"w-2 h-2 bg-[#4f5fad] dark:bg-[#6d78c9] rounded-full animate-pulse shadow-[0_0_5px_rgba(79,95,173,0.5)] dark:shadow-[0_0_5px_rgba(109,120,201,0.5)]\"\n            style=\"animation-delay: 0.2s\"\n          ></div>\n          <div\n            class=\"w-2 h-2 bg-[#4f5fad] dark:bg-[#6d78c9] rounded-full animate-pulse shadow-[0_0_5px_rgba(79,95,173,0.5)] dark:shadow-[0_0_5px_rgba(109,120,201,0.5)]\"\n            style=\"animation-delay: 0.4s\"\n          ></div>\n        </div>\n        <div class=\"text-xs text-[#6d6870] dark:text-[#a0a0a0]\">\n          Retrieving data...\n        </div>\n      </div>\n    </div>\n\n    <!-- Indicateur de début de conversation -->\n    <div\n      *ngIf=\"!hasMoreMessages && messages.length > 0\"\n      class=\"flex justify-center py-2 mb-2\"\n    >\n      <div class=\"flex items-center w-full max-w-xs\">\n        <div\n          class=\"flex-1 h-px bg-gradient-to-r from-transparent via-[#4f5fad]/20 dark:via-[#6d78c9]/20 to-transparent\"\n        ></div>\n        <div\n          class=\"px-3 text-xs bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent font-medium\"\n        >\n          Communication Initialized\n        </div>\n        <div\n          class=\"flex-1 h-px bg-gradient-to-r from-transparent via-[#4f5fad]/20 dark:via-[#6d78c9]/20 to-transparent\"\n        ></div>\n      </div>\n    </div>\n\n    <!-- État d'erreur -->\n    <div\n      *ngIf=\"error\"\n      class=\"bg-[#ff6b69]/10 dark:bg-[#ff6b69]/5 border border-[#ff6b69] dark:border-[#ff6b69]/30 rounded-lg p-4 mx-auto max-w-md my-4 backdrop-blur-sm\"\n    >\n      <div class=\"flex items-start\">\n        <div class=\"text-[#ff6b69] dark:text-[#ff8785] mr-3 text-xl relative\">\n          <i class=\"fas fa-exclamation-triangle\"></i>\n          <!-- Glow effect -->\n          <div\n            class=\"absolute inset-0 bg-[#ff6b69]/20 dark:bg-[#ff8785]/20 blur-xl rounded-full transform scale-150 -z-10\"\n          ></div>\n        </div>\n        <div>\n          <h3 class=\"font-medium text-[#ff6b69] dark:text-[#ff8785] mb-1\">\n            System Error: Communication Failure\n          </h3>\n          <p class=\"text-sm text-[#6d6870] dark:text-[#a0a0a0]\">{{ error }}</p>\n        </div>\n      </div>\n    </div>\n\n    <!-- Messages -->\n    <ng-container *ngIf=\"messages && messages.length > 0; else noMessages\">\n      <div\n        *ngFor=\"let message of messages; let i = index\"\n        class=\"futuristic-message-wrapper\"\n        [attr.data-message-id]=\"message.id\"\n      >\n        <!-- Séparateur de date futuriste -->\n        <div *ngIf=\"shouldShowDateHeader(i)\" class=\"futuristic-date-separator\">\n          <div class=\"futuristic-date-line\"></div>\n          <div class=\"futuristic-date-text\">\n            {{ formatMessageDate(message?.timestamp) }}\n          </div>\n          <div class=\"futuristic-date-line\"></div>\n        </div>\n\n        <!-- Conteneur de message avec alignement -->\n        <div\n          class=\"futuristic-message\"\n          [ngClass]=\"{\n            'futuristic-message-current-user':\n              message?.sender?.id === currentUserId ||\n              message?.sender?._id === currentUserId ||\n              message?.senderId === currentUserId,\n            'futuristic-message-other-user': !(\n              message?.sender?.id === currentUserId ||\n              message?.sender?._id === currentUserId ||\n              message?.senderId === currentUserId\n            )\n          }\"\n        >\n          <!-- Avatar pour les messages reçus -->\n          <div\n            *ngIf=\"\n              !(\n                message?.sender?.id === currentUserId ||\n                message?.sender?._id === currentUserId ||\n                message?.senderId === currentUserId\n              )\n            \"\n            class=\"futuristic-avatar\"\n          >\n            <img\n              [src]=\"\n                message?.sender?.image || 'assets/images/default-avatar.png'\n              \"\n              alt=\"User avatar\"\n              onerror=\"this.src='assets/images/default-avatar.png'\"\n            />\n          </div>\n\n          <!-- Contenu du message -->\n          <div class=\"futuristic-message-content\">\n            <!-- Contenu textuel -->\n            <div\n              *ngIf=\"\n                message?.content &&\n                !hasImage(message) &&\n                !isVoiceMessage(message)\n              \"\n              class=\"futuristic-message-bubble\"\n              [ngClass]=\"{\n                'futuristic-message-pending': message.isPending,\n                'futuristic-message-sending':\n                  message.isPending && !message.isError,\n                'futuristic-message-error': message.isError\n              }\"\n            >\n              <div class=\"futuristic-message-text\">\n                {{ message.content }}\n              </div>\n\n              <!-- Heure du message avec statut de lecture -->\n              <div class=\"futuristic-message-info\">\n                <span class=\"futuristic-message-time\">\n                  {{ formatMessageTime(message?.timestamp) }}\n                </span>\n                <span\n                  *ngIf=\"\n                    message?.sender?.id === currentUserId ||\n                    message?.sender?._id === currentUserId ||\n                    message?.senderId === currentUserId\n                  \"\n                  class=\"futuristic-message-status\"\n                >\n                  <i *ngIf=\"message?.isRead\" class=\"fas fa-check-double\"></i>\n                  <i *ngIf=\"!message?.isRead\" class=\"fas fa-check\"></i>\n                </span>\n              </div>\n            </div>\n\n            <!-- Message vocal -->\n            <div\n              *ngIf=\"isVoiceMessage(message)\"\n              class=\"futuristic-message-bubble futuristic-voice-message-container\"\n              [ngClass]=\"{\n                'futuristic-message-pending': message.isPending,\n                'futuristic-message-sending':\n                  message.isPending && !message.isError,\n                'futuristic-message-error': message.isError\n              }\"\n            >\n              <div class=\"futuristic-voice-message\">\n                <div class=\"futuristic-voice-play-button\">\n                  <i class=\"fas fa-play\"></i>\n                </div>\n                <div class=\"futuristic-voice-waveform\">\n                  <div\n                    *ngFor=\"\n                      let i of [\n                        0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14\n                      ]\n                    \"\n                    class=\"futuristic-voice-bar\"\n                    [style.height.px]=\"\n                      5 + (i % 3 === 0 ? 20 : i % 3 === 1 ? 15 : 10)\n                    \"\n                  ></div>\n                </div>\n              </div>\n\n              <app-voice-message-player\n                [audioUrl]=\"getVoiceMessageUrl(message)\"\n                [duration]=\"getVoiceMessageDuration(message)\"\n                class=\"messenger-style-voice-message\"\n                style=\"display: none\"\n              ></app-voice-message-player>\n\n              <!-- Heure du message avec statut de lecture -->\n              <div class=\"futuristic-message-info\">\n                <span class=\"futuristic-message-time\">\n                  {{ formatMessageTime(message?.timestamp) }}\n                </span>\n                <span\n                  *ngIf=\"\n                    message?.sender?.id === currentUserId ||\n                    message?.sender?._id === currentUserId ||\n                    message?.senderId === currentUserId\n                  \"\n                  class=\"futuristic-message-status\"\n                >\n                  <i *ngIf=\"message?.isRead\" class=\"fas fa-check-double\"></i>\n                  <i *ngIf=\"!message?.isRead\" class=\"fas fa-check\"></i>\n                </span>\n              </div>\n            </div>\n\n            <!-- Contenu image -->\n            <div\n              *ngIf=\"hasImage(message)\"\n              class=\"futuristic-message-image-container\"\n              [ngClass]=\"{\n                'futuristic-message-pending': message.isPending,\n                'futuristic-message-sending':\n                  message.isPending && !message.isError,\n                'futuristic-message-error': message.isError\n              }\"\n            >\n              <div class=\"futuristic-image-wrapper\">\n                <a\n                  [href]=\"getImageUrl(message)\"\n                  target=\"_blank\"\n                  class=\"futuristic-message-image-link\"\n                >\n                  <img\n                    [src]=\"getImageUrl(message)\"\n                    class=\"futuristic-message-image\"\n                    alt=\"Image\"\n                  />\n                </a>\n                <div class=\"futuristic-image-overlay\">\n                  <i class=\"fas fa-expand\"></i>\n                </div>\n              </div>\n\n              <!-- Heure du message avec statut de lecture -->\n              <div class=\"futuristic-message-info\">\n                <span class=\"futuristic-message-time\">\n                  {{ formatMessageTime(message?.timestamp) }}\n                </span>\n                <span\n                  *ngIf=\"\n                    message?.sender?.id === currentUserId ||\n                    message?.sender?._id === currentUserId ||\n                    message?.senderId === currentUserId\n                  \"\n                  class=\"futuristic-message-status\"\n                >\n                  <i *ngIf=\"message?.isRead\" class=\"fas fa-check-double\"></i>\n                  <i *ngIf=\"!message?.isRead\" class=\"fas fa-check\"></i>\n                </span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </ng-container>\n\n    <!-- Template pour aucun message -->\n    <ng-template #noMessages>\n      <div *ngIf=\"!loading && !error\" class=\"futuristic-no-messages\">\n        <div class=\"futuristic-no-messages-icon\">\n          <i class=\"fas fa-satellite-dish\"></i>\n        </div>\n        <div class=\"futuristic-no-messages-text\">\n          Aucun message dans cette conversation.\n          <br />Établissez le premier contact pour commencer.\n        </div>\n        <button\n          (click)=\"messageForm.get('content')?.setValue('Bonjour!')\"\n          class=\"futuristic-start-button\"\n        >\n          <i class=\"fas fa-paper-plane\"></i>\n          Initialiser la communication\n        </button>\n      </div>\n    </ng-template>\n\n    <!-- Indicateur de frappe -->\n    <div *ngIf=\"isTyping\" class=\"futuristic-typing-indicator\">\n      <div class=\"futuristic-avatar\">\n        <img\n          [src]=\"otherParticipant?.image || 'assets/images/default-avatar.png'\"\n          alt=\"User avatar\"\n        />\n      </div>\n      <div class=\"futuristic-typing-bubble\">\n        <div class=\"futuristic-typing-dots\">\n          <div class=\"futuristic-typing-dot\"></div>\n          <div\n            class=\"futuristic-typing-dot\"\n            style=\"animation-delay: 0.2s\"\n          ></div>\n          <div\n            class=\"futuristic-typing-dot\"\n            style=\"animation-delay: 0.4s\"\n          ></div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Zone de saisie style WhatsApp -->\n  <div class=\"whatsapp-input-container\">\n    <!-- Aperçu du fichier -->\n    <div *ngIf=\"previewUrl\" class=\"whatsapp-file-preview\">\n      <img [src]=\"previewUrl\" class=\"whatsapp-preview-image\" />\n      <button (click)=\"removeAttachment()\" class=\"whatsapp-remove-button\">\n        <i class=\"fas fa-times\"></i>\n      </button>\n    </div>\n\n    <!-- Sélecteur d'émojis -->\n    <div *ngIf=\"showEmojiPicker\" class=\"whatsapp-emoji-picker\">\n      <div class=\"whatsapp-emoji-categories\">\n        <button class=\"whatsapp-emoji-category active\">\n          <i class=\"far fa-smile\"></i>\n        </button>\n        <button class=\"whatsapp-emoji-category\">\n          <i class=\"fas fa-cat\"></i>\n        </button>\n        <button class=\"whatsapp-emoji-category\">\n          <i class=\"fas fa-hamburger\"></i>\n        </button>\n        <button class=\"whatsapp-emoji-category\">\n          <i class=\"fas fa-futbol\"></i>\n        </button>\n        <button class=\"whatsapp-emoji-category\">\n          <i class=\"fas fa-car\"></i>\n        </button>\n        <button class=\"whatsapp-emoji-category\">\n          <i class=\"fas fa-lightbulb\"></i>\n        </button>\n      </div>\n      <div class=\"whatsapp-emoji-list\">\n        <button\n          *ngFor=\"let emoji of commonEmojis\"\n          class=\"whatsapp-emoji-item\"\n          (click)=\"insertEmoji(emoji)\"\n        >\n          {{ emoji }}\n        </button>\n      </div>\n    </div>\n\n    <form\n      [formGroup]=\"messageForm\"\n      (ngSubmit)=\"sendMessage()\"\n      class=\"whatsapp-input-form\"\n    >\n      <!-- Boutons d'outils style WhatsApp -->\n      <div class=\"whatsapp-input-tools\">\n        <button\n          type=\"button\"\n          (click)=\"toggleEmojiPicker()\"\n          class=\"whatsapp-tool-button\"\n          [ngClass]=\"{ active: showEmojiPicker }\"\n        >\n          <i class=\"far fa-smile\"></i>\n        </button>\n        <button\n          type=\"button\"\n          (click)=\"fileInput.click()\"\n          class=\"whatsapp-tool-button\"\n        >\n          <i class=\"fas fa-paperclip\"></i>\n          <input\n            #fileInput\n            type=\"file\"\n            (change)=\"onFileSelected($event)\"\n            class=\"hidden\"\n            accept=\"image/*\"\n          />\n        </button>\n      </div>\n\n      <!-- Composant d'enregistrement vocal -->\n      <app-voice-recorder\n        *ngIf=\"isRecordingVoice\"\n        (recordingComplete)=\"onVoiceRecordingComplete($event)\"\n        (recordingCancelled)=\"onVoiceRecordingCancelled()\"\n        [maxDuration]=\"60\"\n      ></app-voice-recorder>\n\n      <input\n        *ngIf=\"!isRecordingVoice\"\n        formControlName=\"content\"\n        type=\"text\"\n        placeholder=\"Message\"\n        (input)=\"onTyping()\"\n        class=\"whatsapp-input-field\"\n      />\n\n      <button\n        type=\"button\"\n        *ngIf=\"!isRecordingVoice && messageForm.get('content')?.value === ''\"\n        (click)=\"toggleVoiceRecording()\"\n        class=\"whatsapp-voice-button\"\n      >\n        <i class=\"fas fa-microphone\"></i>\n      </button>\n\n      <button\n        type=\"submit\"\n        *ngIf=\"!isRecordingVoice && messageForm.get('content')?.value !== ''\"\n        [disabled]=\"isUploading || (messageForm.invalid && !selectedFile)\"\n        class=\"whatsapp-send-button\"\n      >\n        <i *ngIf=\"!isUploading\" class=\"fas fa-paper-plane\"></i>\n        <i *ngIf=\"isUploading\" class=\"fas fa-spinner fa-spin\"></i>\n      </button>\n    </form>\n  </div>\n\n  <!-- Modal d'appel entrant -->\n  <div *ngIf=\"showCallModal && incomingCall\" class=\"whatsapp-call-modal\">\n    <div class=\"whatsapp-call-modal-content\">\n      <div class=\"whatsapp-call-header\">\n        <div class=\"whatsapp-call-avatar\">\n          <img\n            [src]=\"\n              incomingCall.caller?.image || 'assets/images/default-avatar.png'\n            \"\n            alt=\"Caller avatar\"\n          />\n        </div>\n        <h3 class=\"whatsapp-call-name\">{{ incomingCall.caller?.username }}</h3>\n        <p class=\"whatsapp-call-status\">\n          {{\n            incomingCall.type === \"AUDIO\"\n              ? \"Appel audio entrant\"\n              : \"Appel vidéo entrant\"\n          }}\n        </p>\n      </div>\n      <div class=\"whatsapp-call-actions\">\n        <button (click)=\"rejectCall()\" class=\"whatsapp-call-reject\">\n          <i class=\"fas fa-phone-slash\"></i>\n          <span>Rejeter</span>\n        </button>\n        <button (click)=\"acceptCall()\" class=\"whatsapp-call-accept\">\n          <i class=\"fas fa-phone\"></i>\n          <span>Accepter</span>\n        </button>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AAWA,SAAiCA,UAAU,QAAQ,gBAAgB;AACnE,SAASC,YAAY,QAAuC,MAAM;AAGlE,SAIEC,WAAW,EACXC,QAAQ,QACH,8BAA8B;AAErC,SAASC,SAAS,EAAEC,oBAAoB,EAAEC,MAAM,QAAQ,gBAAgB;;;;;;;;;;;;;;;;ICgFhEC,EAAA,CAAAC,SAAA,eAGQ;;;;;IAIVD,EAAA,CAAAE,cAAA,cAA4D;IAExDF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACPJ,EAAA,CAAAE,cAAA,eAA8B;IAC5BF,EAAA,CAAAG,MAAA,GAKF;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IARLJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAC,gBAAA,CAAAC,QAAA,MACF;IAEET,EAAA,CAAAK,SAAA,GAKF;IALEL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAC,gBAAA,CAAAE,QAAA,gBAAAH,MAAA,CAAAI,gBAAA,CAAAJ,MAAA,CAAAC,gBAAA,CAAAI,UAAA,OAKF;;;;;;IAsBAZ,EAAA,CAAAE,cAAA,cAGC;IAIGF,EAAA,CAAAG,MAAA,8BACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,cAAiB;IAGbF,EAAA,CAAAa,UAAA,mBAAAC,wDAAA;MAAAd,EAAA,CAAAe,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAASlB,EAAA,CAAAmB,WAAA,CAAAF,OAAA,CAAAG,WAAA,CAAY,eAAe,CAAC;IAAA,EAAC;IAGtCpB,EAAA,CAAAE,cAAA,cAA+B;IAC7BF,EAAA,CAAAC,SAAA,cAA0D;IAC1DD,EAAA,CAAAE,cAAA,UAAK;IAAAF,EAAA,CAAAG,MAAA,sBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAGzBJ,EAAA,CAAAE,cAAA,YAIC;IAFCF,EAAA,CAAAa,UAAA,mBAAAQ,wDAAA;MAAArB,EAAA,CAAAe,aAAA,CAAAC,IAAA;MAAA,MAAAM,OAAA,GAAAtB,EAAA,CAAAkB,aAAA;MAAA,OAASlB,EAAA,CAAAmB,WAAA,CAAAG,OAAA,CAAAF,WAAA,CAAY,gBAAgB,CAAC;IAAA,EAAC;IAGvCpB,EAAA,CAAAE,cAAA,eAA+B;IAC7BF,EAAA,CAAAC,SAAA,eAA0D;IAC1DD,EAAA,CAAAE,cAAA,WAAK;IAAAF,EAAA,CAAAG,MAAA,YAAI;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAGnBJ,EAAA,CAAAE,cAAA,aAIC;IAFCF,EAAA,CAAAa,UAAA,mBAAAU,yDAAA;MAAAvB,EAAA,CAAAe,aAAA,CAAAC,IAAA;MAAA,MAAAQ,OAAA,GAAAxB,EAAA,CAAAkB,aAAA;MAAA,OAASlB,EAAA,CAAAmB,WAAA,CAAAK,OAAA,CAAAJ,WAAA,CAAY,iBAAiB,CAAC;IAAA,EAAC;IAGxCpB,EAAA,CAAAE,cAAA,eAA+B;IAC7BF,EAAA,CAAAC,SAAA,eAA0D;IAC1DD,EAAA,CAAAE,cAAA,WAAK;IAAAF,EAAA,CAAAG,MAAA,YAAI;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAGnBJ,EAAA,CAAAE,cAAA,aAIC;IAFCF,EAAA,CAAAa,UAAA,mBAAAY,yDAAA;MAAAzB,EAAA,CAAAe,aAAA,CAAAC,IAAA;MAAA,MAAAU,OAAA,GAAA1B,EAAA,CAAAkB,aAAA;MAAA,OAASlB,EAAA,CAAAmB,WAAA,CAAAO,OAAA,CAAAN,WAAA,CAAY,eAAe,CAAC;IAAA,EAAC;IAGtCpB,EAAA,CAAAE,cAAA,eAA+B;IAC7BF,EAAA,CAAAC,SAAA,eAA0D;IAC1DD,EAAA,CAAAE,cAAA,WAAK;IAAAF,EAAA,CAAAG,MAAA,YAAI;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IAoB3BJ,EAAA,CAAAE,cAAA,cAAqE;IAG/DF,EAAA,CAAAC,SAAA,cAEO;IAKTD,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,cAEC;IACCF,EAAA,CAAAG,MAAA,sCACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IAKVJ,EAAA,CAAAE,cAAA,cAGC;IAKKF,EAAA,CAAAC,SAAA,cAEO;IASTD,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,cAAwD;IACtDF,EAAA,CAAAG,MAAA,2BACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IAKVJ,EAAA,CAAAE,cAAA,cAGC;IAEGF,EAAA,CAAAC,SAAA,cAEO;IACPD,EAAA,CAAAE,cAAA,cAEC;IACCF,EAAA,CAAAG,MAAA,kCACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,SAAA,cAEO;IACTD,EAAA,CAAAI,YAAA,EAAM;;;;;IAIRJ,EAAA,CAAAE,cAAA,cAGC;IAGKF,EAAA,CAAAC,SAAA,YAA2C;IAK7CD,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,UAAK;IAEDF,EAAA,CAAAG,MAAA,4CACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAE,cAAA,YAAsD;IAAAF,EAAA,CAAAG,MAAA,GAAW;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;IAAfJ,EAAA,CAAAK,SAAA,GAAW;IAAXL,EAAA,CAAA2B,iBAAA,CAAAC,MAAA,CAAAC,KAAA,CAAW;;;;;IAanE7B,EAAA,CAAAE,cAAA,cAAuE;IACrEF,EAAA,CAAAC,SAAA,cAAwC;IACxCD,EAAA,CAAAE,cAAA,eAAkC;IAChCF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,SAAA,cAAwC;IAC1CD,EAAA,CAAAI,YAAA,EAAM;;;;;IAHFJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAwB,OAAA,CAAAC,iBAAA,CAAAC,WAAA,kBAAAA,WAAA,CAAAC,SAAA,OACF;;;;;IAoBAjC,EAAA,CAAAE,cAAA,eASC;IACCF,EAAA,CAAAC,SAAA,eAME;IACJD,EAAA,CAAAI,YAAA,EAAM;;;;IANFJ,EAAA,CAAAK,SAAA,GAEC;IAFDL,EAAA,CAAAkC,UAAA,SAAAF,WAAA,kBAAAA,WAAA,CAAAG,MAAA,kBAAAH,WAAA,CAAAG,MAAA,CAAAC,KAAA,yCAAApC,EAAA,CAAAqC,aAAA,CAEC;;;;;IAwCGrC,EAAA,CAAAC,SAAA,aAA2D;;;;;IAC3DD,EAAA,CAAAC,SAAA,aAAqD;;;;;IATvDD,EAAA,CAAAE,cAAA,gBAOC;IACCF,EAAA,CAAAsC,UAAA,IAAAC,oEAAA,iBAA2D;IAC3DvC,EAAA,CAAAsC,UAAA,IAAAE,oEAAA,iBAAqD;IACvDxC,EAAA,CAAAI,YAAA,EAAO;;;;IAFDJ,EAAA,CAAAK,SAAA,GAAqB;IAArBL,EAAA,CAAAkC,UAAA,SAAAF,WAAA,kBAAAA,WAAA,CAAAS,MAAA,CAAqB;IACrBzC,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAAkC,UAAA,WAAAF,WAAA,kBAAAA,WAAA,CAAAS,MAAA,EAAsB;;;;;;;;;;;;IAhChCzC,EAAA,CAAAE,cAAA,eAaC;IAEGF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAGNJ,EAAA,CAAAE,cAAA,eAAqC;IAEjCF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACPJ,EAAA,CAAAsC,UAAA,IAAAI,gEAAA,oBAUO;IACT1C,EAAA,CAAAI,YAAA,EAAM;;;;;IA3BNJ,EAAA,CAAAkC,UAAA,YAAAlC,EAAA,CAAA2C,eAAA,IAAAC,GAAA,EAAAZ,WAAA,CAAAa,SAAA,EAAAb,WAAA,CAAAa,SAAA,KAAAb,WAAA,CAAAc,OAAA,EAAAd,WAAA,CAAAc,OAAA,EAKE;IAGA9C,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAA0B,WAAA,CAAAe,OAAA,MACF;IAKI/C,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAA0C,OAAA,CAAAC,iBAAA,CAAAjB,WAAA,kBAAAA,WAAA,CAAAC,SAAA,OACF;IAEGjC,EAAA,CAAAK,SAAA,GAKnB;IALmBL,EAAA,CAAAkC,UAAA,UAAAF,WAAA,kBAAAA,WAAA,CAAAG,MAAA,kBAAAH,WAAA,CAAAG,MAAA,CAAAe,EAAA,MAAAF,OAAA,CAAAG,aAAA,KAAAnB,WAAA,kBAAAA,WAAA,CAAAG,MAAA,kBAAAH,WAAA,CAAAG,MAAA,CAAAiB,GAAA,MAAAJ,OAAA,CAAAG,aAAA,KAAAnB,WAAA,kBAAAA,WAAA,CAAAqB,QAAA,MAAAL,OAAA,CAAAG,aAAA,CAKnB;;;;;IAwBkBnD,EAAA,CAAAC,SAAA,eAUO;;;;IAHLD,EAAA,CAAAsD,WAAA,gBAAAC,KAAA,kBAAAA,KAAA,4BAEC;;;;;IAyBHvD,EAAA,CAAAC,SAAA,aAA2D;;;;;IAC3DD,EAAA,CAAAC,SAAA,aAAqD;;;;;IATvDD,EAAA,CAAAE,cAAA,gBAOC;IACCF,EAAA,CAAAsC,UAAA,IAAAkB,qEAAA,iBAA2D;IAC3DxD,EAAA,CAAAsC,UAAA,IAAAmB,qEAAA,iBAAqD;IACvDzD,EAAA,CAAAI,YAAA,EAAO;;;;IAFDJ,EAAA,CAAAK,SAAA,GAAqB;IAArBL,EAAA,CAAAkC,UAAA,SAAAF,WAAA,kBAAAA,WAAA,CAAAS,MAAA,CAAqB;IACrBzC,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAAkC,UAAA,WAAAF,WAAA,kBAAAA,WAAA,CAAAS,MAAA,EAAsB;;;;;;;;IAlDhCzC,EAAA,CAAAE,cAAA,eASC;IAGKF,EAAA,CAAAC,SAAA,aAA2B;IAC7BD,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,eAAuC;IACrCF,EAAA,CAAAsC,UAAA,IAAAoB,+DAAA,mBAUO;IACT1D,EAAA,CAAAI,YAAA,EAAM;IAGRJ,EAAA,CAAAC,SAAA,oCAK4B;IAG5BD,EAAA,CAAAE,cAAA,eAAqC;IAEjCF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACPJ,EAAA,CAAAsC,UAAA,KAAAqB,iEAAA,oBAUO;IACT3D,EAAA,CAAAI,YAAA,EAAM;;;;;IAjDNJ,EAAA,CAAAkC,UAAA,YAAAlC,EAAA,CAAA2C,eAAA,IAAAC,GAAA,EAAAZ,WAAA,CAAAa,SAAA,EAAAb,WAAA,CAAAa,SAAA,KAAAb,WAAA,CAAAc,OAAA,EAAAd,WAAA,CAAAc,OAAA,EAKE;IAUE9C,EAAA,CAAAK,SAAA,GAGlB;IAHkBL,EAAA,CAAAkC,UAAA,YAAAlC,EAAA,CAAA4D,eAAA,KAAAC,GAAA,EAGlB;IASgB7D,EAAA,CAAAK,SAAA,GAAwC;IAAxCL,EAAA,CAAAkC,UAAA,aAAA4B,OAAA,CAAAC,kBAAA,CAAA/B,WAAA,EAAwC,aAAA8B,OAAA,CAAAE,uBAAA,CAAAhC,WAAA;IAStChC,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAwD,OAAA,CAAAb,iBAAA,CAAAjB,WAAA,kBAAAA,WAAA,CAAAC,SAAA,OACF;IAEGjC,EAAA,CAAAK,SAAA,GAKnB;IALmBL,EAAA,CAAAkC,UAAA,UAAAF,WAAA,kBAAAA,WAAA,CAAAG,MAAA,kBAAAH,WAAA,CAAAG,MAAA,CAAAe,EAAA,MAAAY,OAAA,CAAAX,aAAA,KAAAnB,WAAA,kBAAAA,WAAA,CAAAG,MAAA,kBAAAH,WAAA,CAAAG,MAAA,CAAAiB,GAAA,MAAAU,OAAA,CAAAX,aAAA,KAAAnB,WAAA,kBAAAA,WAAA,CAAAqB,QAAA,MAAAS,OAAA,CAAAX,aAAA,CAKnB;;;;;IAiDkBnD,EAAA,CAAAC,SAAA,aAA2D;;;;;IAC3DD,EAAA,CAAAC,SAAA,aAAqD;;;;;IATvDD,EAAA,CAAAE,cAAA,gBAOC;IACCF,EAAA,CAAAsC,UAAA,IAAA2B,oEAAA,iBAA2D;IAC3DjE,EAAA,CAAAsC,UAAA,IAAA4B,oEAAA,iBAAqD;IACvDlE,EAAA,CAAAI,YAAA,EAAO;;;;IAFDJ,EAAA,CAAAK,SAAA,GAAqB;IAArBL,EAAA,CAAAkC,UAAA,SAAAF,WAAA,kBAAAA,WAAA,CAAAS,MAAA,CAAqB;IACrBzC,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAAkC,UAAA,WAAAF,WAAA,kBAAAA,WAAA,CAAAS,MAAA,EAAsB;;;;;IAzChCzC,EAAA,CAAAE,cAAA,eASC;IAOKF,EAAA,CAAAC,SAAA,eAIE;IACJD,EAAA,CAAAI,YAAA,EAAI;IACJJ,EAAA,CAAAE,cAAA,eAAsC;IACpCF,EAAA,CAAAC,SAAA,aAA6B;IAC/BD,EAAA,CAAAI,YAAA,EAAM;IAIRJ,EAAA,CAAAE,cAAA,eAAqC;IAEjCF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACPJ,EAAA,CAAAsC,UAAA,IAAA6B,gEAAA,oBAUO;IACTnE,EAAA,CAAAI,YAAA,EAAM;;;;;IAxCNJ,EAAA,CAAAkC,UAAA,YAAAlC,EAAA,CAAA2C,eAAA,IAAAC,GAAA,EAAAZ,WAAA,CAAAa,SAAA,EAAAb,WAAA,CAAAa,SAAA,KAAAb,WAAA,CAAAc,OAAA,EAAAd,WAAA,CAAAc,OAAA,EAKE;IAIE9C,EAAA,CAAAK,SAAA,GAA6B;IAA7BL,EAAA,CAAAkC,UAAA,SAAAkC,OAAA,CAAAC,WAAA,CAAArC,WAAA,GAAAhC,EAAA,CAAAqC,aAAA,CAA6B;IAK3BrC,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAAkC,UAAA,QAAAkC,OAAA,CAAAC,WAAA,CAAArC,WAAA,GAAAhC,EAAA,CAAAqC,aAAA,CAA4B;IAa9BrC,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAA8D,OAAA,CAAAnB,iBAAA,CAAAjB,WAAA,kBAAAA,WAAA,CAAAC,SAAA,OACF;IAEGjC,EAAA,CAAAK,SAAA,GAKnB;IALmBL,EAAA,CAAAkC,UAAA,UAAAF,WAAA,kBAAAA,WAAA,CAAAG,MAAA,kBAAAH,WAAA,CAAAG,MAAA,CAAAe,EAAA,MAAAkB,OAAA,CAAAjB,aAAA,KAAAnB,WAAA,kBAAAA,WAAA,CAAAG,MAAA,kBAAAH,WAAA,CAAAG,MAAA,CAAAiB,GAAA,MAAAgB,OAAA,CAAAjB,aAAA,KAAAnB,WAAA,kBAAAA,WAAA,CAAAqB,QAAA,MAAAe,OAAA,CAAAjB,aAAA,CAKnB;;;;;;;;;;;IAxLMnD,EAAA,CAAAE,cAAA,cAIC;IAECF,EAAA,CAAAsC,UAAA,IAAAgC,yDAAA,kBAMM;IAGNtE,EAAA,CAAAE,cAAA,cAaC;IAECF,EAAA,CAAAsC,UAAA,IAAAiC,yDAAA,kBAiBM;IAGNvE,EAAA,CAAAE,cAAA,cAAwC;IAEtCF,EAAA,CAAAsC,UAAA,IAAAkC,yDAAA,kBAmCM;IAGNxE,EAAA,CAAAsC,UAAA,IAAAmC,yDAAA,oBAqDM;IAGNzE,EAAA,CAAAsC,UAAA,IAAAoC,yDAAA,mBA4CM;IACR1E,EAAA,CAAAI,YAAA,EAAM;;;;;;IA5LRJ,EAAA,CAAA2E,WAAA,oBAAA3C,WAAA,CAAAkB,EAAA,CAAmC;IAG7BlD,EAAA,CAAAK,SAAA,GAA6B;IAA7BL,EAAA,CAAAkC,UAAA,SAAA0C,OAAA,CAAAC,oBAAA,CAAAC,KAAA,EAA6B;IAWjC9E,EAAA,CAAAK,SAAA,GAUE;IAVFL,EAAA,CAAAkC,UAAA,YAAAlC,EAAA,CAAA+E,eAAA,IAAAC,GAAA,GAAAhD,WAAA,kBAAAA,WAAA,CAAAG,MAAA,kBAAAH,WAAA,CAAAG,MAAA,CAAAe,EAAA,MAAA0B,OAAA,CAAAzB,aAAA,KAAAnB,WAAA,kBAAAA,WAAA,CAAAG,MAAA,kBAAAH,WAAA,CAAAG,MAAA,CAAAiB,GAAA,MAAAwB,OAAA,CAAAzB,aAAA,KAAAnB,WAAA,kBAAAA,WAAA,CAAAqB,QAAA,MAAAuB,OAAA,CAAAzB,aAAA,KAAAnB,WAAA,kBAAAA,WAAA,CAAAG,MAAA,kBAAAH,WAAA,CAAAG,MAAA,CAAAe,EAAA,MAAA0B,OAAA,CAAAzB,aAAA,KAAAnB,WAAA,kBAAAA,WAAA,CAAAG,MAAA,kBAAAH,WAAA,CAAAG,MAAA,CAAAiB,GAAA,MAAAwB,OAAA,CAAAzB,aAAA,KAAAnB,WAAA,kBAAAA,WAAA,CAAAqB,QAAA,MAAAuB,OAAA,CAAAzB,aAAA,GAUE;IAICnD,EAAA,CAAAK,SAAA,GAOb;IAPaL,EAAA,CAAAkC,UAAA,YAAAF,WAAA,kBAAAA,WAAA,CAAAG,MAAA,kBAAAH,WAAA,CAAAG,MAAA,CAAAe,EAAA,MAAA0B,OAAA,CAAAzB,aAAA,KAAAnB,WAAA,kBAAAA,WAAA,CAAAG,MAAA,kBAAAH,WAAA,CAAAG,MAAA,CAAAiB,GAAA,MAAAwB,OAAA,CAAAzB,aAAA,KAAAnB,WAAA,kBAAAA,WAAA,CAAAqB,QAAA,MAAAuB,OAAA,CAAAzB,aAAA,EAOb;IAeenD,EAAA,CAAAK,SAAA,GAKf;IALeL,EAAA,CAAAkC,UAAA,UAAAF,WAAA,kBAAAA,WAAA,CAAAe,OAAA,MAAA6B,OAAA,CAAAK,QAAA,CAAAjD,WAAA,MAAA4C,OAAA,CAAAM,cAAA,CAAAlD,WAAA,EAKf;IAiCehC,EAAA,CAAAK,SAAA,GAA6B;IAA7BL,EAAA,CAAAkC,UAAA,SAAA0C,OAAA,CAAAM,cAAA,CAAAlD,WAAA,EAA6B;IAwD7BhC,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAAkC,UAAA,SAAA0C,OAAA,CAAAK,QAAA,CAAAjD,WAAA,EAAuB;;;;;IApJlChC,EAAA,CAAAmF,uBAAA,GAAuE;IACrEnF,EAAA,CAAAsC,UAAA,IAAA8C,mDAAA,mBAiMM;IACRpF,EAAA,CAAAqF,qBAAA,EAAe;;;;IAjMSrF,EAAA,CAAAK,SAAA,GAAa;IAAbL,EAAA,CAAAkC,UAAA,YAAAoD,MAAA,CAAAC,QAAA,CAAa;;;;;;IAqMnCvF,EAAA,CAAAE,cAAA,eAA+D;IAE3DF,EAAA,CAAAC,SAAA,aAAqC;IACvCD,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,eAAyC;IACvCF,EAAA,CAAAG,MAAA,+CACA;IAAAH,EAAA,CAAAC,SAAA,SAAM;IAAAD,EAAA,CAAAG,MAAA,0DACR;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,kBAGC;IAFCF,EAAA,CAAAa,UAAA,mBAAA2E,2EAAA;MAAAxF,EAAA,CAAAe,aAAA,CAAA0E,IAAA;MAAA,MAAAC,OAAA,GAAA1F,EAAA,CAAAkB,aAAA;MAAA,IAAAyE,OAAA;MAAA,OAAA3F,EAAA,CAAAmB,WAAA,EAAAwE,OAAA,GAASD,OAAA,CAAAE,WAAA,CAAAC,GAAA,CAAgB,SAAS,CAAC,mBAA1BF,OAAA,CAAAG,QAAA,CAAqC,UAAU,CAAC;IAAA,EAAC;IAG1D9F,EAAA,CAAAC,SAAA,aAAkC;IAClCD,EAAA,CAAAG,MAAA,qCACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;;IAdXJ,EAAA,CAAAsC,UAAA,IAAAyD,kDAAA,oBAeM;;;;IAfA/F,EAAA,CAAAkC,UAAA,UAAA8D,OAAA,CAAAC,OAAA,KAAAD,OAAA,CAAAnE,KAAA,CAAwB;;;;;IAmBhC7B,EAAA,CAAAE,cAAA,eAA0D;IAEtDF,EAAA,CAAAC,SAAA,cAGE;IACJD,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,eAAsC;IAElCF,EAAA,CAAAC,SAAA,eAAyC;IAS3CD,EAAA,CAAAI,YAAA,EAAM;;;;IAfJJ,EAAA,CAAAK,SAAA,GAAqE;IAArEL,EAAA,CAAAkC,UAAA,SAAAgE,OAAA,CAAA1F,gBAAA,kBAAA0F,OAAA,CAAA1F,gBAAA,CAAA4B,KAAA,yCAAApC,EAAA,CAAAqC,aAAA,CAAqE;;;;;;IAuB3ErC,EAAA,CAAAE,cAAA,eAAsD;IACpDF,EAAA,CAAAC,SAAA,eAAyD;IACzDD,EAAA,CAAAE,cAAA,kBAAoE;IAA5DF,EAAA,CAAAa,UAAA,mBAAAsF,6DAAA;MAAAnG,EAAA,CAAAe,aAAA,CAAAqF,IAAA;MAAA,MAAAC,OAAA,GAAArG,EAAA,CAAAkB,aAAA;MAAA,OAASlB,EAAA,CAAAmB,WAAA,CAAAkF,OAAA,CAAAC,gBAAA,EAAkB;IAAA,EAAC;IAClCtG,EAAA,CAAAC,SAAA,aAA4B;IAC9BD,EAAA,CAAAI,YAAA,EAAS;;;;IAHJJ,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAkC,UAAA,QAAAqE,OAAA,CAAAC,UAAA,EAAAxG,EAAA,CAAAqC,aAAA,CAAkB;;;;;;IA6BrBrC,EAAA,CAAAE,cAAA,kBAIC;IADCF,EAAA,CAAAa,UAAA,mBAAA4F,uEAAA;MAAA,MAAAC,WAAA,GAAA1G,EAAA,CAAAe,aAAA,CAAA4F,IAAA;MAAA,MAAAC,SAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAC,OAAA,GAAA9G,EAAA,CAAAkB,aAAA;MAAA,OAASlB,EAAA,CAAAmB,WAAA,CAAA2F,OAAA,CAAAC,WAAA,CAAAH,SAAA,CAAkB;IAAA,EAAC;IAE5B5G,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;IADPJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAsG,SAAA,MACF;;;;;IA5BJ5G,EAAA,CAAAE,cAAA,eAA2D;IAGrDF,EAAA,CAAAC,SAAA,YAA4B;IAC9BD,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAE,cAAA,kBAAwC;IACtCF,EAAA,CAAAC,SAAA,aAA0B;IAC5BD,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAE,cAAA,kBAAwC;IACtCF,EAAA,CAAAC,SAAA,aAAgC;IAClCD,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAE,cAAA,kBAAwC;IACtCF,EAAA,CAAAC,SAAA,aAA6B;IAC/BD,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAE,cAAA,mBAAwC;IACtCF,EAAA,CAAAC,SAAA,cAA0B;IAC5BD,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAE,cAAA,mBAAwC;IACtCF,EAAA,CAAAC,SAAA,cAAgC;IAClCD,EAAA,CAAAI,YAAA,EAAS;IAEXJ,EAAA,CAAAE,cAAA,gBAAiC;IAC/BF,EAAA,CAAAsC,UAAA,KAAA0E,8CAAA,sBAMS;IACXhH,EAAA,CAAAI,YAAA,EAAM;;;;IANgBJ,EAAA,CAAAK,SAAA,IAAe;IAAfL,EAAA,CAAAkC,UAAA,YAAA+E,OAAA,CAAAC,YAAA,CAAe;;;;;;IAyCrClH,EAAA,CAAAE,cAAA,8BAKC;IAHCF,EAAA,CAAAa,UAAA,+BAAAsG,oGAAAC,MAAA;MAAApH,EAAA,CAAAe,aAAA,CAAAsG,IAAA;MAAA,MAAAC,OAAA,GAAAtH,EAAA,CAAAkB,aAAA;MAAA,OAAqBlB,EAAA,CAAAmB,WAAA,CAAAmG,OAAA,CAAAC,wBAAA,CAAAH,MAAA,CAAgC;IAAA,EAAC,gCAAAI,qGAAA;MAAAxH,EAAA,CAAAe,aAAA,CAAAsG,IAAA;MAAA,MAAAI,OAAA,GAAAzH,EAAA,CAAAkB,aAAA;MAAA,OAChClB,EAAA,CAAAmB,WAAA,CAAAsG,OAAA,CAAAC,yBAAA,EAA2B;IAAA,EADK;IAGvD1H,EAAA,CAAAI,YAAA,EAAqB;;;IADpBJ,EAAA,CAAAkC,UAAA,mBAAkB;;;;;;IAGpBlC,EAAA,CAAAE,cAAA,iBAOE;IAFAF,EAAA,CAAAa,UAAA,mBAAA8G,8DAAA;MAAA3H,EAAA,CAAAe,aAAA,CAAA6G,IAAA;MAAA,MAAAC,OAAA,GAAA7H,EAAA,CAAAkB,aAAA;MAAA,OAASlB,EAAA,CAAAmB,WAAA,CAAA0G,OAAA,CAAAC,QAAA,EAAU;IAAA,EAAC;IALtB9H,EAAA,CAAAI,YAAA,EAOE;;;;;;IAEFJ,EAAA,CAAAE,cAAA,kBAKC;IAFCF,EAAA,CAAAa,UAAA,mBAAAkH,gEAAA;MAAA/H,EAAA,CAAAe,aAAA,CAAAiH,IAAA;MAAA,MAAAC,OAAA,GAAAjI,EAAA,CAAAkB,aAAA;MAAA,OAASlB,EAAA,CAAAmB,WAAA,CAAA8G,OAAA,CAAAC,oBAAA,EAAsB;IAAA,EAAC;IAGhClI,EAAA,CAAAC,SAAA,aAAiC;IACnCD,EAAA,CAAAI,YAAA,EAAS;;;;;IAQPJ,EAAA,CAAAC,SAAA,aAAuD;;;;;IACvDD,EAAA,CAAAC,SAAA,aAA0D;;;;;IAP5DD,EAAA,CAAAE,cAAA,kBAKC;IACCF,EAAA,CAAAsC,UAAA,IAAA6F,2CAAA,iBAAuD;IACvDnI,EAAA,CAAAsC,UAAA,IAAA8F,2CAAA,iBAA0D;IAC5DpI,EAAA,CAAAI,YAAA,EAAS;;;;IALPJ,EAAA,CAAAkC,UAAA,aAAAmG,OAAA,CAAAC,WAAA,IAAAD,OAAA,CAAAzC,WAAA,CAAA2C,OAAA,KAAAF,OAAA,CAAAG,YAAA,CAAkE;IAG9DxI,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAkC,UAAA,UAAAmG,OAAA,CAAAC,WAAA,CAAkB;IAClBtI,EAAA,CAAAK,SAAA,GAAiB;IAAjBL,EAAA,CAAAkC,UAAA,SAAAmG,OAAA,CAAAC,WAAA,CAAiB;;;;;;IAM3BtI,EAAA,CAAAE,cAAA,eAAuE;IAI/DF,EAAA,CAAAC,SAAA,eAKE;IACJD,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,cAA+B;IAAAF,EAAA,CAAAG,MAAA,GAAmC;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACvEJ,EAAA,CAAAE,cAAA,aAAgC;IAC9BF,EAAA,CAAAG,MAAA,GAKF;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAENJ,EAAA,CAAAE,cAAA,eAAmC;IACzBF,EAAA,CAAAa,UAAA,mBAAA4H,8DAAA;MAAAzI,EAAA,CAAAe,aAAA,CAAA2H,IAAA;MAAA,MAAAC,OAAA,GAAA3I,EAAA,CAAAkB,aAAA;MAAA,OAASlB,EAAA,CAAAmB,WAAA,CAAAwH,OAAA,CAAAC,UAAA,EAAY;IAAA,EAAC;IAC5B5I,EAAA,CAAAC,SAAA,cAAkC;IAClCD,EAAA,CAAAE,cAAA,YAAM;IAAAF,EAAA,CAAAG,MAAA,eAAO;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAEtBJ,EAAA,CAAAE,cAAA,mBAA4D;IAApDF,EAAA,CAAAa,UAAA,mBAAAgI,8DAAA;MAAA7I,EAAA,CAAAe,aAAA,CAAA2H,IAAA;MAAA,MAAAI,OAAA,GAAA9I,EAAA,CAAAkB,aAAA;MAAA,OAASlB,EAAA,CAAAmB,WAAA,CAAA2H,OAAA,CAAAC,UAAA,EAAY;IAAA,EAAC;IAC5B/I,EAAA,CAAAC,SAAA,cAA4B;IAC5BD,EAAA,CAAAE,cAAA,YAAM;IAAAF,EAAA,CAAAG,MAAA,gBAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IAtBnBJ,EAAA,CAAAK,SAAA,GAEC;IAFDL,EAAA,CAAAkC,UAAA,SAAA8G,OAAA,CAAAC,YAAA,CAAAC,MAAA,kBAAAF,OAAA,CAAAC,YAAA,CAAAC,MAAA,CAAA9G,KAAA,yCAAApC,EAAA,CAAAqC,aAAA,CAEC;IAI0BrC,EAAA,CAAAK,SAAA,GAAmC;IAAnCL,EAAA,CAAA2B,iBAAA,CAAAqH,OAAA,CAAAC,YAAA,CAAAC,MAAA,kBAAAF,OAAA,CAAAC,YAAA,CAAAC,MAAA,CAAAzI,QAAA,CAAmC;IAEhET,EAAA,CAAAK,SAAA,GAKF;IALEL,EAAA,CAAAM,kBAAA,MAAA0I,OAAA,CAAAC,YAAA,CAAAE,IAAA,uEAKF;;;;;;;;AD/nBR,OAAM,MAAOC,oBAAoB;EAmI/BC,YACUC,cAA8B,EAC/BC,KAAqB,EACpBC,WAA4B,EAC5BC,EAAe,EAChBC,aAAgC,EAChCC,MAAc,EACbC,YAA0B,EAC1BC,MAAqB,EACrBC,GAAsB;IARtB,KAAAR,cAAc,GAAdA,cAAc;IACf,KAAAC,KAAK,GAALA,KAAK;IACJ,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,EAAE,GAAFA,EAAE;IACH,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,MAAM,GAANA,MAAM;IACL,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,GAAG,GAAHA,GAAG;IArIb,KAAAvE,QAAQ,GAAc,EAAE;IAExB,KAAAwE,YAAY,GAAwB,IAAI;IACxC,KAAA9D,OAAO,GAAG,IAAI;IAEd,KAAA9C,aAAa,GAAkB,IAAI;IACnC,KAAA6G,eAAe,GAAW,KAAK;IAC/B,KAAAxJ,gBAAgB,GAAgB,IAAI;IACpC,KAAAgI,YAAY,GAAgB,IAAI;IAChC,KAAAhC,UAAU,GAAgC,IAAI;IAC9C,KAAA8B,WAAW,GAAG,KAAK;IACnB,KAAA2B,QAAQ,GAAG,KAAK;IAEhB,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,sBAAsB,GAAG,CAAC;IAET,KAAAC,qBAAqB,GAAG,CAAC,CAAC,CAAC;IAC3B,KAAAC,oBAAoB,GAAG,EAAE,CAAC,CAAC;IAC3B,KAAAC,kBAAkB,GAAG,GAAG,CAAC,CAAC;IACnC,KAAAC,WAAW,GAAG,CAAC,CAAC,CAAC;IACzB,KAAAC,aAAa,GAAG,KAAK,CAAC,CAAC;IACvB,KAAAC,eAAe,GAAG,IAAI,CAAC,CAAC;IAChB,KAAAC,aAAa,GAAiB,IAAIhL,YAAY,EAAE;IAExD;IACA,KAAAiL,gBAAgB,GAAkB,IAAI;IACtC,KAAAC,cAAc,GAAW,EAAE;IAC3B,KAAAC,kBAAkB,GAAqC,EAAE;IACzD,KAAAC,iBAAiB,GAAqC,EAAE;IAExD;IACA,KAAAC,aAAa,GAAW,eAAe,CAAC,CAAC;IACzC,KAAAC,iBAAiB,GAAY,KAAK,CAAC,CAAC;IAEpC;IACA,KAAAC,eAAe,GAAY,KAAK;IAEhC;IACA,KAAAhC,YAAY,GAAQ,IAAI;IACxB,KAAAiC,aAAa,GAAY,KAAK;IAE9B,KAAAhE,YAAY,GAAa,CACvB,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,CACL;IAwaO,KAAAiE,iBAAiB,GAAG,KAAK;IAChB,KAAAC,YAAY,GAAG,GAAG,CAAC,CAAC;IACpB,KAAAC,cAAc,GAAG,IAAI,CAAC,CAAC;IA7ZtC,IAAI,CAACzF,WAAW,GAAG,IAAI,CAAC6D,EAAE,CAAC6B,KAAK,CAAC;MAC/BvI,OAAO,EAAE,CAAC,EAAE,EAAE,CAACtD,UAAU,CAAC8L,SAAS,CAAC,IAAI,CAAC,CAAC;KAC3C,CAAC;EACJ;EACAC,QAAQA,CAAA;IACN,IAAI,CAACrI,aAAa,GAAG,IAAI,CAACqG,WAAW,CAACiC,gBAAgB,EAAE;IAExD;IACA,MAAMC,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IACrD,IAAIF,UAAU,EAAE;MACd,IAAI,CAACX,aAAa,GAAGW,UAAU;MAC/B,IAAI,CAAC7B,MAAM,CAACgC,KAAK,CAAC,aAAa,EAAE,uBAAuBH,UAAU,EAAE,CAAC;;IAGvE;IACA,IAAI,CAACI,iBAAiB,EAAE;IAExB;IACA,IAAI,CAACC,wBAAwB,EAAE;IAE/B,MAAMC,QAAQ,GAAG,IAAI,CAACzC,KAAK,CAAC0C,MAAM,CAC/BC,IAAI,CACHnM,MAAM,CAAEkM,MAAM,IAAKA,MAAM,CAAC,IAAI,CAAC,CAAC,EAChCnM,oBAAoB,EAAE,EACtBD,SAAS,CAAEoM,MAAM,IAAI;MACnB,IAAI,CAAChG,OAAO,GAAG,IAAI;MACnB,IAAI,CAACV,QAAQ,GAAG,EAAE;MAClB,IAAI,CAACgF,WAAW,GAAG,CAAC,CAAC,CAAC;MACtB,IAAI,CAACE,eAAe,GAAG,IAAI,CAAC,CAAC;MAE7B,IAAI,CAACZ,MAAM,CAACgC,KAAK,CACf,aAAa,EACb,8CAA8C,IAAI,CAACtB,WAAW,WAAW,IAAI,CAACF,oBAAoB,EAAE,CACrG;MAED;MACA,OAAO,IAAI,CAACf,cAAc,CAAC6C,eAAe,CACxCF,MAAM,CAAC,IAAI,CAAC,EACZ,IAAI,CAAC5B,oBAAoB,EACzB,IAAI,CAACE,WAAW,CAAC;OAClB;IACH,CAAC,CAAC,CACH,CACA6B,SAAS,CAAC;MACTC,IAAI,EAAGtC,YAAY,IAAI;QACrB,IAAI,CAACuC,wBAAwB,CAACvC,YAAY,CAAC;MAC7C,CAAC;MACDlI,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC0K,WAAW,CAAC,6BAA6B,EAAE1K,KAAK,CAAC;MACxD;KACD,CAAC;IACJ,IAAI,CAAC6I,aAAa,CAAC8B,GAAG,CAACR,QAAQ,CAAC;EAClC;EAEA;;;EAGQF,iBAAiBA,CAAA;IACvB,IAAI,CAACjC,MAAM,CAACgC,KAAK,CAAC,aAAa,EAAE,wCAAwC,CAAC;IAE1E,MAAMY,GAAG,GAAG,IAAI,CAACnD,cAAc,CAACoD,gBAAgB,EAAE,CAACN,SAAS,CAAC;MAC3DC,IAAI,EAAGM,aAAa,IAAI;QACtB,IAAI,CAAC9C,MAAM,CAAC+C,IAAI,CACd,aAAa,EACb,aAAaD,aAAa,CAACE,MAAM,iBAAiB,CACnD;QAED;QACA;QACA,IAAIF,aAAa,CAACE,MAAM,GAAG,CAAC,EAAE;UAC5B,IAAI,CAAChD,MAAM,CAACgC,KAAK,CACf,aAAa,EACb,oCAAoC,CACrC;UAED;UACAiB,UAAU,CAAC,MAAK;YACd,IAAI,CAAChD,GAAG,CAACiD,aAAa,EAAE;YACxB,IAAI,CAAClD,MAAM,CAACgC,KAAK,CACf,aAAa,EACb,6CAA6C,CAC9C;UACH,CAAC,EAAE,GAAG,CAAC;;MAEX,CAAC;MACDhK,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACgI,MAAM,CAAChI,KAAK,CACf,aAAa,EACb,+BAA+B,EAC/BA,KAAK,CACN;QACD;MACF;KACD,CAAC;;IAEF,IAAI,CAAC6I,aAAa,CAAC8B,GAAG,CAACC,GAAG,CAAC;EAC7B;EAEA;;;;;EAKQF,WAAWA,CAACS,OAAe,EAAEnL,KAAU;IAC7C,IAAI,CAACgI,MAAM,CAAChI,KAAK,CAAC,aAAa,EAAEmL,OAAO,EAAEnL,KAAK,CAAC;IAChD,IAAI,CAACoE,OAAO,GAAG,KAAK;IACpB,IAAI,CAACpE,KAAK,GAAGA,KAAK;IAClB,IAAI,CAAC+H,YAAY,CAACqD,SAAS,CAACD,OAAO,CAAC;EACtC;EAEA;EACAE,WAAWA,CAACC,QAAiB;IAC3B,IAAI,CAACA,QAAQ,EAAE,OAAO,SAAS;IAC/B,IAAIA,QAAQ,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,UAAU;IACpD,IAAID,QAAQ,CAACE,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,aAAa;IAClD,IAAIF,QAAQ,CAACE,QAAQ,CAAC,MAAM,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,QAAQ,CAAC,EAC1D,OAAO,cAAc;IACvB,IAAIF,QAAQ,CAACE,QAAQ,CAAC,OAAO,CAAC,EAAE,OAAO,eAAe;IACtD,IAAIF,QAAQ,CAACE,QAAQ,CAAC,YAAY,CAAC,EAAE,OAAO,oBAAoB;IAChE,IAAIF,QAAQ,CAACE,QAAQ,CAAC,OAAO,CAAC,EAAE,OAAO,eAAe;IACtD,IAAIF,QAAQ,CAACE,QAAQ,CAAC,OAAO,CAAC,EAAE,OAAO,eAAe;IACtD,IAAIF,QAAQ,CAACE,QAAQ,CAAC,KAAK,CAAC,IAAIF,QAAQ,CAACE,QAAQ,CAAC,YAAY,CAAC,EAC7D,OAAO,iBAAiB;IAC1B,OAAO,SAAS;EAClB;EACAC,WAAWA,CAACH,QAAiB;IAC3B,IAAI,CAACA,QAAQ,EAAE,OAAO,MAAM;IAE5B,MAAMI,OAAO,GAA2B;MACtC,QAAQ,EAAE,OAAO;MACjB,iBAAiB,EAAE,KAAK;MACxB,oBAAoB,EAAE,UAAU;MAChC,yEAAyE,EACvE,UAAU;MACZ,0BAA0B,EAAE,OAAO;MACnC,mEAAmE,EACjE,OAAO;MACT,+BAA+B,EAAE,YAAY;MAC7C,2EAA2E,EACzE,YAAY;MACd,QAAQ,EAAE,OAAO;MACjB,QAAQ,EAAE,OAAO;MACjB,iBAAiB,EAAE,aAAa;MAChC,8BAA8B,EAAE;KACjC;IACD,KAAK,MAAM,CAACC,GAAG,EAAEC,KAAK,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACJ,OAAO,CAAC,EAAE;MAClD,IAAIJ,QAAQ,CAACE,QAAQ,CAACG,GAAG,CAAC,EAAE,OAAOC,KAAK;;IAE1C,OAAO,MAAM;EACf;EAEQnB,wBAAwBA,CAACvC,YAA0B;IACzD,IAAI,CAACF,MAAM,CAAC+C,IAAI,CACd,aAAa,EACb,iCAAiC7C,YAAY,CAAC7G,EAAE,EAAE,CACnD;IACD,IAAI,CAAC2G,MAAM,CAACgC,KAAK,CACf,aAAa,EACb,oBAAoB9B,YAAY,EAAExE,QAAQ,EAAEsH,MAAM,IAAI,CAAC,iBACrD9C,YAAY,EAAE6D,YAAY,EAAEf,MAAM,IAAI,CACxC,eAAe,CAChB;IAED;IACA,IAAI9C,YAAY,EAAExE,QAAQ,IAAIwE,YAAY,CAACxE,QAAQ,CAACsH,MAAM,GAAG,CAAC,EAAE;MAC9D,IAAI,CAAChD,MAAM,CAACgC,KAAK,CACf,aAAa,EACb,6BACE9B,YAAY,CAACxE,QAAQ,CAAC,CAAC,CAAC,CAACrC,EAC3B,aAAa6G,YAAY,CAACxE,QAAQ,CAAC,CAAC,CAAC,CAACxC,OAAO,EAAE8K,SAAS,CACtD,CAAC,EACD,EAAE,CACH,YAAY9D,YAAY,CAACxE,QAAQ,CAAC,CAAC,CAAC,CAACpD,MAAM,EAAE1B,QAAQ,EAAE,CACzD;;IAGH,IAAI,CAACsJ,YAAY,GAAGA,YAAY;IAEhC;IACA,IAAI,CAACA,YAAY,EAAExE,QAAQ,IAAIwE,YAAY,CAACxE,QAAQ,CAACsH,MAAM,KAAK,CAAC,EAAE;MACjE,IAAI,CAAChD,MAAM,CAACgC,KAAK,CAAC,aAAa,EAAE,mCAAmC,CAAC;MAErE;MACA,IAAI,CAACrL,gBAAgB,GACnBuJ,YAAY,EAAE6D,YAAY,EAAEE,IAAI,CAC7BC,CAAC,IAAKA,CAAC,CAAC7K,EAAE,KAAK,IAAI,CAACC,aAAa,IAAI4K,CAAC,CAAC3K,GAAG,KAAK,IAAI,CAACD,aAAa,CACnE,IAAI,IAAI;MAEX;MACA,IAAI,CAACoC,QAAQ,GAAG,EAAE;MAElB,IAAI,CAACsE,MAAM,CAACgC,KAAK,CAAC,aAAa,EAAE,kCAAkC,CAAC;KACrE,MAAM;MACL;MACA,MAAMmC,oBAAoB,GAAG,CAAC,IAAIjE,YAAY,EAAExE,QAAQ,IAAI,EAAE,CAAC,CAAC;MAEhE;MACAyI,oBAAoB,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;QACjC,MAAMC,KAAK,GACTF,CAAC,CAACjM,SAAS,YAAYoM,IAAI,GACvBH,CAAC,CAACjM,SAAS,CAACqM,OAAO,EAAE,GACrB,IAAID,IAAI,CAACH,CAAC,CAACjM,SAAmB,CAAC,CAACqM,OAAO,EAAE;QAC/C,MAAMC,KAAK,GACTJ,CAAC,CAAClM,SAAS,YAAYoM,IAAI,GACvBF,CAAC,CAAClM,SAAS,CAACqM,OAAO,EAAE,GACrB,IAAID,IAAI,CAACF,CAAC,CAAClM,SAAmB,CAAC,CAACqM,OAAO,EAAE;QAC/C,OAAOF,KAAK,GAAGG,KAAK;MACtB,CAAC,CAAC;MAEF;MACA,IAAIP,oBAAoB,CAACnB,MAAM,GAAG,CAAC,EAAE;QACnC,MAAM2B,YAAY,GAAGR,oBAAoB,CAAC,CAAC,CAAC;QAC5C,IAAI,CAACnE,MAAM,CAACgC,KAAK,CACf,aAAa,EACb,gCAAgC2C,YAAY,CAACrM,MAAM,EAAEe,EAAE,gBAAgBsL,YAAY,CAACrM,MAAM,EAAEiB,GAAG,cAAcoL,YAAY,CAACnL,QAAQ,iBAAiBmL,YAAY,CAACC,QAAQ,EAAEvL,EAAE,kBAAkBsL,YAAY,CAACC,QAAQ,EAAErL,GAAG,gBAAgBoL,YAAY,CAACE,UAAU,EAAE,CAClQ;;MAGH;MACA,IAAI,CAACnJ,QAAQ,GAAGyI,oBAAoB;MAEpC,IAAI,CAACnE,MAAM,CAACgC,KAAK,CACf,aAAa,EACb,aAAa,IAAI,CAACtG,QAAQ,CAACsH,MAAM,6BAA6B,CAC/D;MAED,IAAI,CAAChD,MAAM,CAACgC,KAAK,CACf,aAAa,EACb,SAASmC,oBAAoB,CAACnB,MAAM,6CAA6C,IAAI,CAACtH,QAAQ,CAACsH,MAAM,EAAE,CACxG;;IAGH,IAAI,CAACrM,gBAAgB,GACnBuJ,YAAY,EAAE6D,YAAY,EAAEE,IAAI,CAC7BC,CAAC,IAAKA,CAAC,CAAC7K,EAAE,KAAK,IAAI,CAACC,aAAa,IAAI4K,CAAC,CAAC3K,GAAG,KAAK,IAAI,CAACD,aAAa,CACnE,IAAI,IAAI;IAEX,IAAI,CAAC0G,MAAM,CAACgC,KAAK,CACf,aAAa,EACb,iCACE,IAAI,CAACrL,gBAAgB,EAAEC,QAAQ,IAAI,SACrC,EAAE,CACH;IAED,IAAI,CAACwF,OAAO,GAAG,KAAK;IACpB6G,UAAU,CAAC,MAAM,IAAI,CAAC6B,cAAc,EAAE,EAAE,GAAG,CAAC;IAE5C,IAAI,CAAC9E,MAAM,CAACgC,KAAK,CAAC,aAAa,EAAE,iCAAiC,CAAC;IACnE,IAAI,CAAC+C,kBAAkB,EAAE;IAEzB,IAAI,IAAI,CAAC7E,YAAY,EAAE7G,EAAE,EAAE;MACzB,IAAI,CAAC2G,MAAM,CAACgC,KAAK,CACf,aAAa,EACb,8CAA8C,IAAI,CAAC9B,YAAY,CAAC7G,EAAE,EAAE,CACrE;MACD,IAAI,CAAC2L,8BAA8B,CAAC,IAAI,CAAC9E,YAAY,CAAC7G,EAAE,CAAC;MACzD,IAAI,CAAC4L,sBAAsB,CAAC,IAAI,CAAC/E,YAAY,CAAC7G,EAAE,CAAC;MACjD,IAAI,CAAC6L,2BAA2B,CAAC,IAAI,CAAChF,YAAY,CAAC7G,EAAE,CAAC;;IAGxD,IAAI,CAAC2G,MAAM,CAAC+C,IAAI,CAAC,aAAa,EAAE,kCAAkC,CAAC;EACrE;EAEQiC,8BAA8BA,CAACG,cAAsB;IAC3D,MAAMvC,GAAG,GAAG,IAAI,CAACnD,cAAc,CAACuF,8BAA8B,CAC5DG,cAAc,CACf,CAAC5C,SAAS,CAAC;MACVC,IAAI,EAAG4C,mBAAmB,IAAI;QAC5B,IAAI,CAAClF,YAAY,GAAGkF,mBAAmB;QACvC,IAAI,CAAC1J,QAAQ,GAAG0J,mBAAmB,CAAC1J,QAAQ,GACxC,CAAC,GAAG0J,mBAAmB,CAAC1J,QAAQ,CAAC,GACjC,EAAE;QACN,IAAI,CAACoJ,cAAc,EAAE;MACvB,CAAC;MACD9M,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC+H,YAAY,CAACqD,SAAS,CAAC,yCAAyC,CAAC;MACxE;KACD,CAAC;IACF,IAAI,CAACvC,aAAa,CAAC8B,GAAG,CAACC,GAAG,CAAC;EAC7B;EAEQqC,sBAAsBA,CAACE,cAAsB;IACnD,MAAMvC,GAAG,GAAG,IAAI,CAACnD,cAAc,CAACwF,sBAAsB,CACpDE,cAAc,CACf,CAAC5C,SAAS,CAAC;MACVC,IAAI,EAAG6C,UAAU,IAAI;QACnB,IAAIA,UAAU,EAAEF,cAAc,KAAK,IAAI,CAACjF,YAAY,EAAE7G,EAAE,EAAE;UACxD;UACA,IAAI,CAACqC,QAAQ,GAAG,CAAC,GAAG,IAAI,CAACA,QAAQ,EAAE2J,UAAU,CAAC,CAACjB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;YAC3D,MAAMC,KAAK,GACTF,CAAC,CAACjM,SAAS,YAAYoM,IAAI,GACvBH,CAAC,CAACjM,SAAS,CAACqM,OAAO,EAAE,GACrB,IAAID,IAAI,CAACH,CAAC,CAACjM,SAAmB,CAAC,CAACqM,OAAO,EAAE;YAC/C,MAAMC,KAAK,GACTJ,CAAC,CAAClM,SAAS,YAAYoM,IAAI,GACvBF,CAAC,CAAClM,SAAS,CAACqM,OAAO,EAAE,GACrB,IAAID,IAAI,CAACF,CAAC,CAAClM,SAAmB,CAAC,CAACqM,OAAO,EAAE;YAC/C,OAAOF,KAAK,GAAGG,KAAK,CAAC,CAAC;UACxB,CAAC,CAAC;;UAEF,IAAI,CAAC1E,MAAM,CAACgC,KAAK,CACf,aAAa,EACb,kCAAkC,IAAI,CAACtG,QAAQ,CAACsH,MAAM,WAAW,CAClE;UAEDC,UAAU,CAAC,MAAM,IAAI,CAAC6B,cAAc,EAAE,EAAE,GAAG,CAAC;UAE5C;UACA,IACEO,UAAU,CAAC/M,MAAM,EAAEe,EAAE,KAAK,IAAI,CAACC,aAAa,IAC5C+L,UAAU,CAAC/M,MAAM,EAAEiB,GAAG,KAAK,IAAI,CAACD,aAAa,EAC7C;YACA,IAAI+L,UAAU,CAAChM,EAAE,EAAE;cACjB,IAAI,CAACoG,cAAc,CAAC6F,iBAAiB,CAACD,UAAU,CAAChM,EAAE,CAAC,CAACkJ,SAAS,EAAE;;;;MAIxE,CAAC;MACDvK,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC+H,YAAY,CAACqD,SAAS,CAAC,iCAAiC,CAAC;MAChE;KACD,CAAC;IACF,IAAI,CAACvC,aAAa,CAAC8B,GAAG,CAACC,GAAG,CAAC;EAC7B;EAEQsC,2BAA2BA,CAACC,cAAsB;IACxD,MAAMvC,GAAG,GAAG,IAAI,CAACnD,cAAc,CAAC8F,0BAA0B,CACxDJ,cAAc,CACf,CAAC5C,SAAS,CAAC;MACVC,IAAI,EAAGgD,KAAK,IAAI;QACd,IAAIA,KAAK,CAACC,MAAM,KAAK,IAAI,CAACnM,aAAa,EAAE;UACvC,IAAI,CAAC8G,QAAQ,GAAGoF,KAAK,CAACpF,QAAQ;UAC9B,IAAI,IAAI,CAACA,QAAQ,EAAE;YACjBsF,YAAY,CAAC,IAAI,CAACC,aAAa,CAAC;YAChC,IAAI,CAACA,aAAa,GAAG1C,UAAU,CAAC,MAAK;cACnC,IAAI,CAAC7C,QAAQ,GAAG,KAAK;YACvB,CAAC,EAAE,IAAI,CAAC;;;MAGd;KACD,CAAC;IACF,IAAI,CAACS,aAAa,CAAC8B,GAAG,CAACC,GAAG,CAAC;EAC7B;EAEQmC,kBAAkBA,CAAA;IACxB,MAAMa,cAAc,GAAG,IAAI,CAAClK,QAAQ,CAACxF,MAAM,CACxC2P,GAAG,IACF,CAACA,GAAG,CAACjN,MAAM,KACViN,GAAG,CAACjB,QAAQ,EAAEvL,EAAE,KAAK,IAAI,CAACC,aAAa,IACtCuM,GAAG,CAACjB,QAAQ,EAAErL,GAAG,KAAK,IAAI,CAACD,aAAa,CAAC,CAC9C;IAEDsM,cAAc,CAACE,OAAO,CAAED,GAAG,IAAI;MAC7B,IAAIA,GAAG,CAACxM,EAAE,EAAE;QACV,MAAMuJ,GAAG,GAAG,IAAI,CAACnD,cAAc,CAAC6F,iBAAiB,CAACO,GAAG,CAACxM,EAAE,CAAC,CAACkJ,SAAS,CAAC;UAClEvK,KAAK,EAAGA,KAAK,IAAI;YACf,IAAI,CAACgI,MAAM,CAAChI,KAAK,CACf,aAAa,EACb,gCAAgC,EAChCA,KAAK,CACN;UACH;SACD,CAAC;QACF,IAAI,CAAC6I,aAAa,CAAC8B,GAAG,CAACC,GAAG,CAAC;;IAE/B,CAAC,CAAC;EACJ;EAEAmD,cAAcA,CAACP,KAAU;IACvB,MAAMQ,IAAI,GAAGR,KAAK,CAACS,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAI,CAACF,IAAI,EAAE;IAEX;IACA,IAAIA,IAAI,CAACG,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE;MAC/B,IAAI,CAACpG,YAAY,CAACqD,SAAS,CAAC,mCAAmC,CAAC;MAChE;;IAGF;IACA,MAAMgD,UAAU,GAAG,CACjB,YAAY,EACZ,WAAW,EACX,WAAW,EACX,iBAAiB,EACjB,oBAAoB,EACpB,yEAAyE,CAC1E;IACD,IAAI,CAACA,UAAU,CAAC5C,QAAQ,CAACwC,IAAI,CAAC1G,IAAI,CAAC,EAAE;MACnC,IAAI,CAACS,YAAY,CAACqD,SAAS,CACzB,gEAAgE,CACjE;MACD;;IAGF,IAAI,CAACzE,YAAY,GAAGqH,IAAI;IACxB,MAAMK,MAAM,GAAG,IAAIC,UAAU,EAAE;IAC/BD,MAAM,CAACE,MAAM,GAAG,MAAK;MACnB,IAAI,CAAC5J,UAAU,GAAG0J,MAAM,CAACG,MAAM;IACjC,CAAC;IACDH,MAAM,CAACI,aAAa,CAACT,IAAI,CAAC;EAC5B;EAEAvJ,gBAAgBA,CAAA;IACd,IAAI,CAACkC,YAAY,GAAG,IAAI;IACxB,IAAI,CAAChC,UAAU,GAAG,IAAI;IACtB,IAAI,IAAI,CAAC+J,SAAS,EAAEC,aAAa,EAAE;MACjC,IAAI,CAACD,SAAS,CAACC,aAAa,CAAC/C,KAAK,GAAG,EAAE;;EAE3C;EAOA;;;;EAIA3F,QAAQA,CAAA;IACN,IAAI,CAAC,IAAI,CAACiC,YAAY,EAAE7G,EAAE,IAAI,CAAC,IAAI,CAACC,aAAa,EAAE;MACjD;;IAGF;IACA,MAAM6L,cAAc,GAAG,IAAI,CAACjF,YAAY,CAAC7G,EAAE;IAE3C;IACAqM,YAAY,CAAC,IAAI,CAACkB,WAAW,CAAC;IAE9B;IACA,IAAI,CAAC,IAAI,CAACtF,iBAAiB,EAAE;MAC3B,IAAI,CAACA,iBAAiB,GAAG,IAAI;MAC7B,IAAI,CAACtB,MAAM,CAACgC,KAAK,CAAC,aAAa,EAAE,2BAA2B,CAAC;MAE7D,IAAI,CAACvC,cAAc,CAACoH,WAAW,CAAC1B,cAAc,CAAC,CAAC5C,SAAS,CAAC;QACxDC,IAAI,EAAEA,CAAA,KAAK;UACT,IAAI,CAACxC,MAAM,CAACgC,KAAK,CACf,aAAa,EACb,uCAAuC,CACxC;QACH,CAAC;QACDhK,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACgI,MAAM,CAAChI,KAAK,CACf,aAAa,EACb,kCAAkC,EAClCA,KAAK,CACN;QACH;OACD,CAAC;;IAGJ;IACA,IAAI,CAAC4O,WAAW,GAAG3D,UAAU,CAAC,MAAK;MACjC,IAAI,IAAI,CAAC3B,iBAAiB,EAAE;QAC1B,IAAI,CAACA,iBAAiB,GAAG,KAAK;QAC9B,IAAI,CAACtB,MAAM,CAACgC,KAAK,CACf,aAAa,EACb,6CAA6C,CAC9C;QAED,IAAI,CAACvC,cAAc,CAACqH,UAAU,CAAC3B,cAAc,CAAC,CAAC5C,SAAS,CAAC;UACvDC,IAAI,EAAEA,CAAA,KAAK;YACT,IAAI,CAACxC,MAAM,CAACgC,KAAK,CACf,aAAa,EACb,uCAAuC,CACxC;UACH,CAAC;UACDhK,KAAK,EAAGA,KAAK,IAAI;YACf,IAAI,CAACgI,MAAM,CAAChI,KAAK,CACf,aAAa,EACb,kCAAkC,EAClCA,KAAK,CACN;UACH;SACD,CAAC;;IAEN,CAAC,EAAE,IAAI,CAACwJ,cAAc,CAAC;EACzB;EAEA;;;EAGAuF,mBAAmBA,CAAA;IACjB,IAAI,CAAC5F,iBAAiB,GAAG,CAAC,IAAI,CAACA,iBAAiB;IAEhD;IACA,IAAI,IAAI,CAACA,iBAAiB,EAAE;MAC1B8B,UAAU,CAAC,MAAK;QACd,MAAM+D,YAAY,GAAIxB,KAAiB,IAAI;UACzC,MAAMS,MAAM,GAAGT,KAAK,CAACS,MAAqB;UAC1C,IAAI,CAACA,MAAM,CAACgB,OAAO,CAAC,iBAAiB,CAAC,EAAE;YACtC,IAAI,CAAC9F,iBAAiB,GAAG,KAAK;YAC9B+F,QAAQ,CAACC,mBAAmB,CAAC,OAAO,EAAEH,YAAY,CAAC;;QAEvD,CAAC;QACDE,QAAQ,CAACE,gBAAgB,CAAC,OAAO,EAAEJ,YAAY,CAAC;MAClD,CAAC,EAAE,CAAC,CAAC;;EAET;EAEA;;;;EAIAzP,WAAWA,CAAC8P,KAAa;IACvB,IAAI,CAACnG,aAAa,GAAGmG,KAAK;IAC1B,IAAI,CAAClG,iBAAiB,GAAG,KAAK;IAE9B;IACAW,YAAY,CAACwF,OAAO,CAAC,YAAY,EAAED,KAAK,CAAC;IAEzC,IAAI,CAACrH,MAAM,CAACgC,KAAK,CAAC,aAAa,EAAE,qBAAqBqF,KAAK,EAAE,CAAC;EAChE;EAEAE,WAAWA,CAAA;IACT,IAAI,CAACvH,MAAM,CAAC+C,IAAI,CAAC,aAAa,EAAE,4BAA4B,CAAC;IAE7D;IACA,MAAMyE,KAAK,GAAG1F,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAAC/B,MAAM,CAACgC,KAAK,CACf,aAAa,EACb,+BAA+B,CAAC,CAACwF,KAAK,YAAY,IAAI,CAAClO,aAAa,EAAE,CACvE;IAED,IACG,IAAI,CAACyC,WAAW,CAAC2C,OAAO,IAAI,CAAC,IAAI,CAACC,YAAY,IAC/C,CAAC,IAAI,CAACrF,aAAa,IACnB,CAAC,IAAI,CAAC3C,gBAAgB,EAAE0C,EAAE,EAC1B;MACA,IAAI,CAAC2G,MAAM,CAACyH,IAAI,CACd,aAAa,EACb,uDAAuD,CACxD;MACD;;IAGF;IACA,IAAI,CAACC,mBAAmB,EAAE;IAE1B,MAAMxO,OAAO,GAAG,IAAI,CAAC6C,WAAW,CAACC,GAAG,CAAC,SAAS,CAAC,EAAE4H,KAAK;IAEtD;IACA,MAAM+D,WAAW,GAAY;MAC3BtO,EAAE,EAAE,OAAO,GAAG,IAAImL,IAAI,EAAE,CAACC,OAAO,EAAE;MAClCvL,OAAO,EAAEA,OAAO,IAAI,EAAE;MACtBZ,MAAM,EAAE;QACNe,EAAE,EAAE,IAAI,CAACC,aAAa,IAAI,EAAE;QAC5B1C,QAAQ,EAAE,IAAI,CAACuJ;OAChB;MACDyE,QAAQ,EAAE;QACRvL,EAAE,EAAE,IAAI,CAAC1C,gBAAgB,CAAC0C,EAAE;QAC5BzC,QAAQ,EAAE,IAAI,CAACD,gBAAgB,CAACC,QAAQ,IAAI;OAC7C;MACDwB,SAAS,EAAE,IAAIoM,IAAI,EAAE;MACrB5L,MAAM,EAAE,KAAK;MACbI,SAAS,EAAE,IAAI,CAAE;KAClB;IAED;IACA,IAAI,IAAI,CAAC2F,YAAY,EAAE;MACrB;MACA,IAAIiJ,QAAQ,GAAG,MAAM;MACrB,IAAI,IAAI,CAACjJ,YAAY,CAACW,IAAI,CAACiE,UAAU,CAAC,QAAQ,CAAC,EAAE;QAC/CqE,QAAQ,GAAG,OAAO;QAElB;QACA,IAAI,IAAI,CAACjL,UAAU,EAAE;UACnBgL,WAAW,CAACE,WAAW,GAAG,CACxB;YACExO,EAAE,EAAE,iBAAiB;YACrByO,GAAG,EAAE,IAAI,CAACnL,UAAU,GAAG,IAAI,CAACA,UAAU,CAACoL,QAAQ,EAAE,GAAG,EAAE;YACtDzI,IAAI,EAAExJ,WAAW,CAACkS,KAAK;YACvBC,IAAI,EAAE,IAAI,CAACtJ,YAAY,CAACsJ,IAAI;YAC5B9B,IAAI,EAAE,IAAI,CAACxH,YAAY,CAACwH;WACzB,CACF;;;MAIL;MACA,IAAIyB,QAAQ,KAAK,OAAO,EAAE;QACxBD,WAAW,CAACrI,IAAI,GAAGxJ,WAAW,CAACkS,KAAK;OACrC,MAAM,IAAIJ,QAAQ,KAAK,MAAM,EAAE;QAC9BD,WAAW,CAACrI,IAAI,GAAGxJ,WAAW,CAACoS,IAAI;;;IAIvC;IACA,IAAI,CAACxM,QAAQ,GAAG,CAAC,GAAG,IAAI,CAACA,QAAQ,EAAEiM,WAAW,CAAC;IAE/C;IACA,MAAMQ,UAAU,GAAG,IAAI,CAACxJ,YAAY,CAAC,CAAC;IACtC,IAAI,CAAC5C,WAAW,CAACqM,KAAK,EAAE;IACxB,IAAI,CAAC3L,gBAAgB,EAAE;IAEvB;IACAwG,UAAU,CAAC,MAAM,IAAI,CAAC6B,cAAc,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;IAE/C;IACA,IAAI,CAACrG,WAAW,GAAG,IAAI;IAEvB,MAAM4J,OAAO,GAAG,IAAI,CAAC5I,cAAc,CAAC8H,WAAW,CAC7C,IAAI,CAAC5Q,gBAAgB,CAAC0C,EAAE,EACxBH,OAAO,EACPiP,UAAU,IAAIG,SAAS,EACvBxS,WAAW,CAACyS,IAAI,CACjB,CAAChG,SAAS,CAAC;MACVC,IAAI,EAAGW,OAAO,IAAI;QAChB,IAAI,CAACnD,MAAM,CAAC+C,IAAI,CACd,aAAa,EACb,8BAA8BI,OAAO,EAAE9J,EAAE,IAAI,SAAS,EAAE,CACzD;QAED;QACA,IAAI,CAACqC,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAAC8M,GAAG,CAAE3C,GAAG,IACpCA,GAAG,CAACxM,EAAE,KAAKsO,WAAW,CAACtO,EAAE,GAAG8J,OAAO,GAAG0C,GAAG,CAC1C;QAED,IAAI,CAACpH,WAAW,GAAG,KAAK;MAC1B,CAAC;MACDzG,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACgI,MAAM,CAAChI,KAAK,CAAC,aAAa,EAAE,wBAAwB,EAAEA,KAAK,CAAC;QAEjE;QACA,IAAI,CAAC0D,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAAC8M,GAAG,CAAE3C,GAAG,IAAI;UACxC,IAAIA,GAAG,CAACxM,EAAE,KAAKsO,WAAW,CAACtO,EAAE,EAAE;YAC7B,OAAO;cACL,GAAGwM,GAAG;cACN7M,SAAS,EAAE,KAAK;cAChBC,OAAO,EAAE;aACV;;UAEH,OAAO4M,GAAG;QACZ,CAAC,CAAC;QAEF,IAAI,CAACpH,WAAW,GAAG,KAAK;QACxB,IAAI,CAACsB,YAAY,CAACqD,SAAS,CAAC,wBAAwB,CAAC;MACvD;KACD,CAAC;IAEF,IAAI,CAACvC,aAAa,CAAC8B,GAAG,CAAC0F,OAAO,CAAC;EACjC;EAEAjP,iBAAiBA,CAAChB,SAAoC;IACpD,IAAI,CAACA,SAAS,EAAE;MACd,OAAO,cAAc;;IAEvB,IAAI;MACF,MAAMqQ,IAAI,GAAGrQ,SAAS,YAAYoM,IAAI,GAAGpM,SAAS,GAAG,IAAIoM,IAAI,CAACpM,SAAS,CAAC;MACxE;MACA,OAAOqQ,IAAI,CAACC,kBAAkB,CAAC,EAAE,EAAE;QACjCC,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE,SAAS;QACjBC,MAAM,EAAE;OACT,CAAC;KACH,CAAC,OAAO7Q,KAAK,EAAE;MACd,IAAI,CAACgI,MAAM,CAAChI,KAAK,CAAC,aAAa,EAAE,gCAAgC,EAAEA,KAAK,CAAC;MACzE,OAAO,cAAc;;EAEzB;EAEAlB,gBAAgBA,CAACC,UAAqC;IACpD,IAAI,CAACA,UAAU,EAAE,OAAO,SAAS;IACjC,MAAM+R,cAAc,GAClB/R,UAAU,YAAYyN,IAAI,GAAGzN,UAAU,GAAG,IAAIyN,IAAI,CAACzN,UAAU,CAAC;IAChE,MAAMgS,GAAG,GAAG,IAAIvE,IAAI,EAAE;IACtB,MAAMwE,SAAS,GACbC,IAAI,CAACC,GAAG,CAACH,GAAG,CAACtE,OAAO,EAAE,GAAGqE,cAAc,CAACrE,OAAO,EAAE,CAAC,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;IAEvE,IAAIuE,SAAS,GAAG,EAAE,EAAE;MAClB,OAAO,UAAUF,cAAc,CAACJ,kBAAkB,CAAC,EAAE,EAAE;QACrDC,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE;OACT,CAAC,EAAE;;IAEN,OAAO,UAAUE,cAAc,CAACK,kBAAkB,EAAE,EAAE;EACxD;EAEAjR,iBAAiBA,CAACE,SAAoC;IACpD,IAAI,CAACA,SAAS,EAAE;MACd,OAAO,cAAc;;IAGvB,IAAI;MACF,MAAMqQ,IAAI,GAAGrQ,SAAS,YAAYoM,IAAI,GAAGpM,SAAS,GAAG,IAAIoM,IAAI,CAACpM,SAAS,CAAC;MACxE,MAAMgR,KAAK,GAAG,IAAI5E,IAAI,EAAE;MAExB;MACA,MAAM6E,OAAO,GAA+B;QAC1CC,OAAO,EAAE,OAAO;QAChBX,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE;OACT;MAED,IAAIH,IAAI,CAACc,YAAY,EAAE,KAAKH,KAAK,CAACG,YAAY,EAAE,EAAE;QAChD,OAAOd,IAAI,CAACC,kBAAkB,CAAC,EAAE,EAAE;UACjCC,IAAI,EAAE,SAAS;UACfC,MAAM,EAAE;SACT,CAAC;;MAGJ,MAAMY,SAAS,GAAG,IAAIhF,IAAI,CAAC4E,KAAK,CAAC;MACjCI,SAAS,CAACC,OAAO,CAACD,SAAS,CAACE,OAAO,EAAE,GAAG,CAAC,CAAC;MAE1C,IAAIjB,IAAI,CAACc,YAAY,EAAE,KAAKC,SAAS,CAACD,YAAY,EAAE,EAAE;QACpD,OAAO,SAASd,IAAI,CAACC,kBAAkB,CAAC,EAAE,EAAE;UAC1CC,IAAI,EAAE,SAAS;UACfC,MAAM,EAAE;SACT,CAAC,EAAE;;MAGN;MACA,MAAMe,GAAG,GAAGlB,IAAI,CACbU,kBAAkB,CAAC,OAAO,EAAE;QAAEG,OAAO,EAAE;MAAO,CAAE,CAAC,CACjDM,WAAW,EAAE;MAChB,OAAO,GAAGD,GAAG,MAAMlB,IAAI,CAACC,kBAAkB,CAAC,EAAE,EAAE;QAC7CC,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE;OACT,CAAC,EAAE;KACL,CAAC,OAAO5Q,KAAK,EAAE;MACd,IAAI,CAACgI,MAAM,CAAChI,KAAK,CAAC,aAAa,EAAE,gCAAgC,EAAEA,KAAK,CAAC;MACzE,OAAO,cAAc;;EAEzB;EAEAgD,oBAAoBA,CAAC6O,KAAa;IAChC,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,IAAI;IAE5B,IAAI;MACF,MAAMC,UAAU,GAAG,IAAI,CAACpO,QAAQ,CAACmO,KAAK,CAAC;MACvC,MAAME,OAAO,GAAG,IAAI,CAACrO,QAAQ,CAACmO,KAAK,GAAG,CAAC,CAAC;MAExC,IAAI,CAACC,UAAU,EAAE1R,SAAS,IAAI,CAAC2R,OAAO,EAAE3R,SAAS,EAAE;QACjD,OAAO,IAAI;;MAGb,MAAM4R,WAAW,GAAG,IAAI,CAACC,oBAAoB,CAACH,UAAU,CAAC1R,SAAS,CAAC;MACnE,MAAM8R,QAAQ,GAAG,IAAI,CAACD,oBAAoB,CAACF,OAAO,CAAC3R,SAAS,CAAC;MAE7D,OAAO4R,WAAW,KAAKE,QAAQ;KAChC,CAAC,OAAOlS,KAAK,EAAE;MACd,IAAI,CAACgI,MAAM,CAAChI,KAAK,CAAC,aAAa,EAAE,6BAA6B,EAAEA,KAAK,CAAC;MACtE,OAAO,KAAK;;EAEhB;EAEQiS,oBAAoBA,CAAC7R,SAAoC;IAC/D,IAAI,CAACA,SAAS,EAAE;MACd,OAAO,cAAc;;IAGvB,IAAI;MACF,OAAO,CACLA,SAAS,YAAYoM,IAAI,GAAGpM,SAAS,GAAG,IAAIoM,IAAI,CAACpM,SAAS,CAAC,EAC3DmR,YAAY,EAAE;KACjB,CAAC,OAAOvR,KAAK,EAAE;MACd,IAAI,CAACgI,MAAM,CAAChI,KAAK,CACf,aAAa,EACb,oCAAoC,EACpCA,KAAK,CACN;MACD,OAAO,cAAc;;EAEzB;EACAmS,cAAcA,CAAChH,OAAmC;IAChD,IAAI,CAACA,OAAO,EAAE;MACZ,OAAOrN,WAAW,CAACyS,IAAI;;IAGzB,IAAI;MACF;MACA,IAAIpF,OAAO,CAAC7D,IAAI,EAAE;QAChB;QACA,MAAM8K,OAAO,GAAGjH,OAAO,CAAC7D,IAAI,CAACyI,QAAQ,EAAE;QACvC,IAAIqC,OAAO,KAAK,MAAM,IAAIA,OAAO,KAAK,MAAM,EAAE;UAC5C,OAAOtU,WAAW,CAACyS,IAAI;SACxB,MAAM,IAAI6B,OAAO,KAAK,OAAO,IAAIA,OAAO,KAAK,OAAO,EAAE;UACrD,OAAOtU,WAAW,CAACkS,KAAK;SACzB,MAAM,IAAIoC,OAAO,KAAK,MAAM,IAAIA,OAAO,KAAK,MAAM,EAAE;UACnD,OAAOtU,WAAW,CAACoS,IAAI;SACxB,MAAM,IAAIkC,OAAO,KAAK,OAAO,IAAIA,OAAO,KAAK,OAAO,EAAE;UACrD,OAAOtU,WAAW,CAACuU,KAAK;SACzB,MAAM,IAAID,OAAO,KAAK,OAAO,IAAIA,OAAO,KAAK,OAAO,EAAE;UACrD,OAAOtU,WAAW,CAACwU,KAAK;SACzB,MAAM,IAAIF,OAAO,KAAK,QAAQ,IAAIA,OAAO,KAAK,QAAQ,EAAE;UACvD,OAAOtU,WAAW,CAACyU,MAAM;;;MAI7B;MACA,IAAIpH,OAAO,CAAC0E,WAAW,EAAE7E,MAAM,EAAE;QAC/B,MAAMwH,UAAU,GAAGrH,OAAO,CAAC0E,WAAW,CAAC,CAAC,CAAC;QACzC,IAAI2C,UAAU,IAAIA,UAAU,CAAClL,IAAI,EAAE;UACjC,MAAMmL,iBAAiB,GAAGD,UAAU,CAAClL,IAAI,CAACyI,QAAQ,EAAE;UAEpD;UACA,IAAI0C,iBAAiB,KAAK,OAAO,IAAIA,iBAAiB,KAAK,OAAO,EAAE;YAClE,OAAO3U,WAAW,CAACkS,KAAK;WACzB,MAAM,IACLyC,iBAAiB,KAAK,MAAM,IAC5BA,iBAAiB,KAAK,MAAM,EAC5B;YACA,OAAO3U,WAAW,CAACoS,IAAI;WACxB,MAAM,IACLuC,iBAAiB,KAAK,OAAO,IAC7BA,iBAAiB,KAAK,OAAO,EAC7B;YACA,OAAO3U,WAAW,CAACuU,KAAK;WACzB,MAAM,IACLI,iBAAiB,KAAK,OAAO,IAC7BA,iBAAiB,KAAK,OAAO,EAC7B;YACA,OAAO3U,WAAW,CAACwU,KAAK;;;QAI5B;QACA,OAAOxU,WAAW,CAACoS,IAAI;;MAGzB;MACA,OAAOpS,WAAW,CAACyS,IAAI;KACxB,CAAC,OAAOvQ,KAAK,EAAE;MACd,IAAI,CAACgI,MAAM,CAAChI,KAAK,CAAC,aAAa,EAAE,6BAA6B,EAAEA,KAAK,CAAC;MACtE,OAAOlC,WAAW,CAACyS,IAAI;;EAE3B;EAEA;EACAnN,QAAQA,CAAC+H,OAAmC;IAC1C,IAAI,CAACA,OAAO,IAAI,CAACA,OAAO,CAAC0E,WAAW,IAAI1E,OAAO,CAAC0E,WAAW,CAAC7E,MAAM,KAAK,CAAC,EAAE;MACxE,OAAO,KAAK;;IAGd,MAAMwH,UAAU,GAAGrH,OAAO,CAAC0E,WAAW,CAAC,CAAC,CAAC;IACzC,IAAI,CAAC2C,UAAU,IAAI,CAACA,UAAU,CAAClL,IAAI,EAAE;MACnC,OAAO,KAAK;;IAGd,MAAMA,IAAI,GAAGkL,UAAU,CAAClL,IAAI,CAACyI,QAAQ,EAAE;IACvC,OAAOzI,IAAI,KAAK,OAAO,IAAIA,IAAI,KAAK,OAAO;EAC7C;EAEA;;;EAGAjE,cAAcA,CAAC8H,OAAmC;IAChD,IAAI,CAACA,OAAO,EAAE;MACZ,OAAO,KAAK;;IAGd;IACA,IACEA,OAAO,CAAC7D,IAAI,KAAKxJ,WAAW,CAAC4U,aAAa,IAC1CvH,OAAO,CAAC7D,IAAI,KAAKxJ,WAAW,CAAC6U,mBAAmB,EAChD;MACA,OAAO,IAAI;;IAGb;IACA,IAAIxH,OAAO,CAAC0E,WAAW,IAAI1E,OAAO,CAAC0E,WAAW,CAAC7E,MAAM,GAAG,CAAC,EAAE;MACzD,OAAOG,OAAO,CAAC0E,WAAW,CAAC+C,IAAI,CAAEC,GAAG,IAAI;QACtC,MAAMvL,IAAI,GAAGuL,GAAG,CAACvL,IAAI,EAAEyI,QAAQ,EAAE;QACjC,OACEzI,IAAI,KAAK,eAAe,IACxBA,IAAI,KAAK,eAAe,IACvB6D,OAAO,CAAC2H,QAAQ,EAAEzP,cAAc,KAC9BiE,IAAI,KAAK,OAAO,IAAIA,IAAI,KAAK,OAAO,CAAE;MAE7C,CAAC,CAAC;;IAGJ;IACA,OAAO,CAAC,CAAC6D,OAAO,CAAC2H,QAAQ,EAAEzP,cAAc;EAC3C;EAEA;;;EAGAnB,kBAAkBA,CAACiJ,OAAmC;IACpD,IAAI,CAACA,OAAO,IAAI,CAACA,OAAO,CAAC0E,WAAW,IAAI1E,OAAO,CAAC0E,WAAW,CAAC7E,MAAM,KAAK,CAAC,EAAE;MACxE,OAAO,EAAE;;IAGX;IACA,MAAM+H,eAAe,GAAG5H,OAAO,CAAC0E,WAAW,CAAC5D,IAAI,CAAE4G,GAAG,IAAI;MACvD,MAAMvL,IAAI,GAAGuL,GAAG,CAACvL,IAAI,EAAEyI,QAAQ,EAAE;MACjC,OACEzI,IAAI,KAAK,eAAe,IACxBA,IAAI,KAAK,eAAe,IACxBA,IAAI,KAAK,OAAO,IAChBA,IAAI,KAAK,OAAO;IAEpB,CAAC,CAAC;IAEF,OAAOyL,eAAe,EAAEjD,GAAG,IAAI,EAAE;EACnC;EAEA;;;EAGA3N,uBAAuBA,CAACgJ,OAAmC;IACzD,IAAI,CAACA,OAAO,EAAE;MACZ,OAAO,CAAC;;IAGV;IACA,IAAIA,OAAO,CAAC2H,QAAQ,EAAEE,QAAQ,EAAE;MAC9B,OAAO7H,OAAO,CAAC2H,QAAQ,CAACE,QAAQ;;IAGlC;IACA,IAAI7H,OAAO,CAAC0E,WAAW,IAAI1E,OAAO,CAAC0E,WAAW,CAAC7E,MAAM,GAAG,CAAC,EAAE;MACzD,MAAM+H,eAAe,GAAG5H,OAAO,CAAC0E,WAAW,CAAC5D,IAAI,CAAE4G,GAAG,IAAI;QACvD,MAAMvL,IAAI,GAAGuL,GAAG,CAACvL,IAAI,EAAEyI,QAAQ,EAAE;QACjC,OACEzI,IAAI,KAAK,eAAe,IACxBA,IAAI,KAAK,eAAe,IACxBA,IAAI,KAAK,OAAO,IAChBA,IAAI,KAAK,OAAO;MAEpB,CAAC,CAAC;MAEF,IAAIyL,eAAe,IAAIA,eAAe,CAACC,QAAQ,EAAE;QAC/C,OAAOD,eAAe,CAACC,QAAQ;;;IAInC,OAAO,CAAC;EACV;EAEA;EACAxQ,WAAWA,CAAC2I,OAAmC;IAC7C,IAAI,CAACA,OAAO,IAAI,CAACA,OAAO,CAAC0E,WAAW,IAAI1E,OAAO,CAAC0E,WAAW,CAAC7E,MAAM,KAAK,CAAC,EAAE;MACxE,OAAO,EAAE;;IAGX,MAAMwH,UAAU,GAAGrH,OAAO,CAAC0E,WAAW,CAAC,CAAC,CAAC;IACzC,OAAO2C,UAAU,EAAE1C,GAAG,IAAI,EAAE;EAC9B;EAEAmD,mBAAmBA,CAAC9H,OAAmC;IACrD,IAAI,CAACA,OAAO,EAAE;MACZ,OAAO,kCAAkC;;IAG3C,IAAI;MACF,MAAM+H,aAAa,GACjB/H,OAAO,CAAC7K,MAAM,EAAEe,EAAE,KAAK,IAAI,CAACC,aAAa,IACzC6J,OAAO,CAAC7K,MAAM,EAAEiB,GAAG,KAAK,IAAI,CAACD,aAAa,IAC1C6J,OAAO,CAAC3J,QAAQ,KAAK,IAAI,CAACF,aAAa;MAEzC;MACA;MACA;MACA,MAAM6R,SAAS,GAAGD,aAAa,GAC3B,kDAAkD,GAClD,qDAAqD;MAEzD,MAAME,WAAW,GAAG,IAAI,CAACjB,cAAc,CAAChH,OAAO,CAAC;MAEhD;MACA,IAAIA,OAAO,CAAC0E,WAAW,IAAI1E,OAAO,CAAC0E,WAAW,CAAC7E,MAAM,GAAG,CAAC,EAAE;QACzD,MAAMwH,UAAU,GAAGrH,OAAO,CAAC0E,WAAW,CAAC,CAAC,CAAC;QACzC,IAAI2C,UAAU,IAAIA,UAAU,CAAClL,IAAI,EAAE;UACjC,MAAMmL,iBAAiB,GAAGD,UAAU,CAAClL,IAAI,CAACyI,QAAQ,EAAE;UACpD,IAAI0C,iBAAiB,KAAK,OAAO,IAAIA,iBAAiB,KAAK,OAAO,EAAE;YAClE;YACA,OAAO,cAAc;WACtB,MAAM,IACLA,iBAAiB,KAAK,MAAM,IAC5BA,iBAAiB,KAAK,MAAM,EAC5B;YACA,OAAO,GAAGU,SAAS,MAAM;;;;MAK/B;MACA,IACEC,WAAW,KAAKtV,WAAW,CAACkS,KAAK,IACjCoD,WAAW,KAAKtV,WAAW,CAACuV,WAAW,EACvC;QACA;QACA,OAAO,cAAc;OACtB,MAAM,IACLD,WAAW,KAAKtV,WAAW,CAACoS,IAAI,IAChCkD,WAAW,KAAKtV,WAAW,CAACwV,UAAU,EACtC;QACA,OAAO,GAAGH,SAAS,MAAM;;MAG3B;MACA,OAAO,GAAGA,SAAS,wDAAwD;KAC5E,CAAC,OAAOnT,KAAK,EAAE;MACd,IAAI,CAACgI,MAAM,CAAChI,KAAK,CACf,aAAa,EACb,mCAAmC,EACnCA,KAAK,CACN;MACD,OAAO,gEAAgE;;EAE3E;EAEA;EAEA;EACAuT,QAAQA,CAAC/F,KAAU;IACjB,MAAMgG,SAAS,GAAGhG,KAAK,CAACS,MAAM;IAC9B,MAAMwF,SAAS,GAAGD,SAAS,CAACC,SAAS;IAErC;IACA,IACEA,SAAS,GAAG,EAAE,IACd,CAAC,IAAI,CAAC9K,aAAa,IACnB,IAAI,CAACT,YAAY,EAAE7G,EAAE,IACrB,IAAI,CAACuH,eAAe,EACpB;MACA;MACA,IAAI,CAAC8K,oBAAoB,EAAE;MAE3B;MACA,MAAMC,eAAe,GAAGH,SAAS,CAACI,YAAY;MAC9C,MAAMC,mBAAmB,GAAG,IAAI,CAACC,sBAAsB,EAAE;MAEzD;MACA,IAAI,CAACnL,aAAa,GAAG,IAAI;MAEzB;MACA,IAAI,CAACoL,gBAAgB,EAAE;MAEvB;MACA;MACAC,qBAAqB,CAAC,MAAK;QACzB,MAAMC,sBAAsB,GAAGA,CAAA,KAAK;UAClC,IAAIJ,mBAAmB,EAAE;YACvB,MAAMK,cAAc,GAAG,IAAI,CAACC,kBAAkB,CAC5CN,mBAAmB,CAACxS,EAAE,CACvB;YACD,IAAI6S,cAAc,EAAE;cAClB;cACAA,cAAc,CAACE,cAAc,CAAC;gBAAEC,KAAK,EAAE;cAAQ,CAAE,CAAC;aACnD,MAAM;cACL;cACA,MAAMC,eAAe,GAAGd,SAAS,CAACI,YAAY;cAC9C,MAAMW,UAAU,GAAGD,eAAe,GAAGX,eAAe;cACpDH,SAAS,CAACC,SAAS,GAAGA,SAAS,GAAGc,UAAU;;;UAIhD;UACA,IAAI,CAACC,oBAAoB,EAAE;QAC7B,CAAC;QAED;QACAvJ,UAAU,CAACgJ,sBAAsB,EAAE,GAAG,CAAC;MACzC,CAAC,CAAC;;EAEN;EAEA;EACQH,sBAAsBA,CAAA;IAC5B,IAAI,CAAC,IAAI,CAACW,iBAAiB,EAAE9F,aAAa,IAAI,CAAC,IAAI,CAACjL,QAAQ,CAACsH,MAAM,EACjE,OAAO,IAAI;IAEb,MAAMwI,SAAS,GAAG,IAAI,CAACiB,iBAAiB,CAAC9F,aAAa;IACtD,MAAM+F,eAAe,GAAGlB,SAAS,CAACmB,gBAAgB,CAAC,eAAe,CAAC;IAEnE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,eAAe,CAAC1J,MAAM,EAAE4J,CAAC,EAAE,EAAE;MAC/C,MAAMC,OAAO,GAAGH,eAAe,CAACE,CAAC,CAAC;MAClC,MAAME,IAAI,GAAGD,OAAO,CAACE,qBAAqB,EAAE;MAE5C;MACA,IAAID,IAAI,CAACE,GAAG,IAAI,CAAC,IAAIF,IAAI,CAACG,MAAM,IAAIzB,SAAS,CAAC0B,YAAY,EAAE;QAC1D,MAAMC,SAAS,GAAGN,OAAO,CAACO,YAAY,CAAC,iBAAiB,CAAC;QACzD,OAAO,IAAI,CAAC1R,QAAQ,CAACuI,IAAI,CAAEoJ,CAAC,IAAKA,CAAC,CAAChU,EAAE,KAAK8T,SAAS,CAAC,IAAI,IAAI;;;IAIhE,OAAO,IAAI;EACb;EAEA;EACQhB,kBAAkBA,CACxBgB,SAA6B;IAE7B,IAAI,CAAC,IAAI,CAACV,iBAAiB,EAAE9F,aAAa,IAAI,CAACwG,SAAS,EAAE,OAAO,IAAI;IACrE,OAAO,IAAI,CAACV,iBAAiB,CAAC9F,aAAa,CAAC2G,aAAa,CACvD,qBAAqBH,SAAS,IAAI,CACnC;EACH;EAEA;EACQzB,oBAAoBA,CAAA;IAC1B;IACA,IAAI,CAACxE,QAAQ,CAACqG,cAAc,CAAC,2BAA2B,CAAC,EAAE;MACzD,MAAMC,SAAS,GAAGtG,QAAQ,CAACuG,aAAa,CAAC,KAAK,CAAC;MAC/CD,SAAS,CAACnU,EAAE,GAAG,2BAA2B;MAC1CmU,SAAS,CAACE,SAAS,GAAG,wCAAwC;MAC9DF,SAAS,CAACG,SAAS,GACjB,uEAAuE;MAEzE,IAAI,IAAI,CAAClB,iBAAiB,EAAE9F,aAAa,EAAE;QACzC,IAAI,CAAC8F,iBAAiB,CAAC9F,aAAa,CAACiH,OAAO,CAACJ,SAAS,CAAC;;;EAG7D;EAEA;EACQhB,oBAAoBA,CAAA;IAC1B,MAAMgB,SAAS,GAAGtG,QAAQ,CAACqG,cAAc,CAAC,2BAA2B,CAAC;IACtE,IAAIC,SAAS,IAAIA,SAAS,CAACK,UAAU,EAAE;MACrCL,SAAS,CAACK,UAAU,CAACC,WAAW,CAACN,SAAS,CAAC;;EAE/C;EAEA;EACAzB,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACpL,aAAa,IAAI,CAAC,IAAI,CAACT,YAAY,EAAE7G,EAAE,IAAI,CAAC,IAAI,CAACuH,eAAe,EACvE;IAEF;IACA,IAAI,CAACD,aAAa,GAAG,IAAI;IAEzB;IACA,IAAI,CAACD,WAAW,EAAE;IAElB;IACA,IAAI,CAACjB,cAAc,CAAC6C,eAAe,CACjC,IAAI,CAACpC,YAAY,CAAC7G,EAAE,EACpB,IAAI,CAACmH,oBAAoB,EACzB,IAAI,CAACE,WAAW,CACjB,CAAC6B,SAAS,CAAC;MACVC,IAAI,EAAGtC,YAAY,IAAI;QACrB,IACEA,YAAY,IACZA,YAAY,CAACxE,QAAQ,IACrBwE,YAAY,CAACxE,QAAQ,CAACsH,MAAM,GAAG,CAAC,EAChC;UACA;UACA,MAAM+K,WAAW,GAAG,CAAC,GAAG,IAAI,CAACrS,QAAQ,CAAC;UAEtC;UACA,MAAMsS,WAAW,GAAG,IAAIC,GAAG,CAACF,WAAW,CAACvF,GAAG,CAAE3C,GAAG,IAAKA,GAAG,CAACxM,EAAE,CAAC,CAAC;UAE7D;UACA,MAAM6U,WAAW,GAAGhO,YAAY,CAACxE,QAAQ,CACtCxF,MAAM,CAAE2P,GAAG,IAAK,CAACmI,WAAW,CAACG,GAAG,CAACtI,GAAG,CAACxM,EAAE,CAAC,CAAC,CACzC+K,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;YACb,MAAMC,KAAK,GAAG,IAAIC,IAAI,CAACH,CAAC,CAACjM,SAAmB,CAAC,CAACqM,OAAO,EAAE;YACvD,MAAMC,KAAK,GAAG,IAAIF,IAAI,CAACF,CAAC,CAAClM,SAAmB,CAAC,CAACqM,OAAO,EAAE;YACvD,OAAOF,KAAK,GAAGG,KAAK;UACtB,CAAC,CAAC;UAEJ,IAAIwJ,WAAW,CAAClL,MAAM,GAAG,CAAC,EAAE;YAC1B;YACA,IAAI,CAACtH,QAAQ,GAAG,CAAC,GAAGwS,WAAW,EAAE,GAAGH,WAAW,CAAC;YAEhD;YACA,IAAI,IAAI,CAACrS,QAAQ,CAACsH,MAAM,GAAG,IAAI,CAACvC,kBAAkB,EAAE;cAClD,IAAI,CAAC/E,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAAC0S,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC3N,kBAAkB,CAAC;;YAGjE;YACA,IAAI,CAACG,eAAe,GAClBsN,WAAW,CAAClL,MAAM,IAAI,IAAI,CAACxC,oBAAoB;WAClD,MAAM;YACL;YACA,IAAI,CAACI,eAAe,GAAG,KAAK;;SAE/B,MAAM;UACL,IAAI,CAACA,eAAe,GAAG,KAAK;;QAG9B;QACA;QACAqC,UAAU,CAAC,MAAK;UACd,IAAI,CAACtC,aAAa,GAAG,KAAK;QAC5B,CAAC,EAAE,GAAG,CAAC;MACT,CAAC;MACD3I,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACgI,MAAM,CAAChI,KAAK,CAAC,aAAa,EAAE,8BAA8B,EAAEA,KAAK,CAAC;QACvE,IAAI,CAAC2I,aAAa,GAAG,KAAK;QAC1B,IAAI,CAAC6L,oBAAoB,EAAE;QAC3B,IAAI,CAACzM,YAAY,CAACqD,SAAS,CAAC,8BAA8B,CAAC;MAC7D;KACD,CAAC;EACJ;EAEA;EACQiL,eAAeA,CACrBC,UAAqC,EACrCC,UAAqC;IAErC,IAAI,CAACD,UAAU,IAAI,CAACC,UAAU,EAAE,OAAO,KAAK;IAE5C,IAAI;MACF,MAAMC,KAAK,GACTF,UAAU,YAAY9J,IAAI,GACtB8J,UAAU,CAAC7J,OAAO,EAAE,GACpB,IAAID,IAAI,CAAC8J,UAAoB,CAAC,CAAC7J,OAAO,EAAE;MAC9C,MAAMgK,KAAK,GACTF,UAAU,YAAY/J,IAAI,GACtB+J,UAAU,CAAC9J,OAAO,EAAE,GACpB,IAAID,IAAI,CAAC+J,UAAoB,CAAC,CAAC9J,OAAO,EAAE;MAC9C,OAAOwE,IAAI,CAACC,GAAG,CAACsF,KAAK,GAAGC,KAAK,CAAC,GAAG,IAAI,CAAC,CAAC;KACxC,CAAC,OAAOzW,KAAK,EAAE;MACd,OAAO,KAAK;;EAEhB;EAEA8M,cAAcA,CAAC4J,KAAA,GAAiB,KAAK;IACnC,IAAI;MACF,IAAI,CAAC,IAAI,CAACjC,iBAAiB,EAAE9F,aAAa,EAAE;MAE5C;MACAqF,qBAAqB,CAAC,MAAK;QACzB,MAAMR,SAAS,GAAG,IAAI,CAACiB,iBAAiB,CAAC9F,aAAa;QACtD,MAAMgI,kBAAkB,GACtBnD,SAAS,CAACI,YAAY,GAAGJ,SAAS,CAAC0B,YAAY,IAC/C1B,SAAS,CAACC,SAAS,GAAG,GAAG;QAE3B;QACA;QACA;QACA,IAAIiD,KAAK,IAAIC,kBAAkB,EAAE;UAC/B;UACAnD,SAAS,CAACoD,QAAQ,CAAC;YACjB5B,GAAG,EAAExB,SAAS,CAACI,YAAY;YAC3BiD,QAAQ,EAAE;WACX,CAAC;;MAEN,CAAC,CAAC;KACH,CAAC,OAAOC,GAAG,EAAE;MACZ,IAAI,CAAC9O,MAAM,CAAChI,KAAK,CAAC,aAAa,EAAE,4BAA4B,EAAE8W,GAAG,CAAC;;EAEvE;EAEA;EACA;;;EAGAzQ,oBAAoBA,CAAA;IAClB,IAAI,CAACgC,gBAAgB,GAAG,CAAC,IAAI,CAACA,gBAAgB;IAE9C,IAAI,CAAC,IAAI,CAACA,gBAAgB,EAAE;MAC1B;MACA,IAAI,CAACC,sBAAsB,GAAG,CAAC;;EAEnC;EAEA;;;;EAIA5C,wBAAwBA,CAACqR,SAAe;IACtC,IAAI,CAAC/O,MAAM,CAACgC,KAAK,CACf,aAAa,EACb,iCAAiC,EACjC+M,SAAS,CAAC5I,IAAI,CACf;IAED,IAAI,CAAC,IAAI,CAACjG,YAAY,EAAE7G,EAAE,IAAI,CAAC,IAAI,CAAC1C,gBAAgB,EAAE0C,EAAE,EAAE;MACxD,IAAI,CAAC0G,YAAY,CAACqD,SAAS,CAAC,uCAAuC,CAAC;MACpE,IAAI,CAAC/C,gBAAgB,GAAG,KAAK;MAC7B;;IAGF;IACA,MAAMwE,UAAU,GAAG,IAAI,CAAClO,gBAAgB,EAAE0C,EAAE,IAAI,EAAE;IAElD;IACA,IAAI,CAACoG,cAAc,CAACuP,gBAAgB,CAClCnK,UAAU,EACVkK,SAAS,EACT,IAAI,CAAC7O,YAAY,EAAE7G,EAAE,EACrB,IAAI,CAACiH,sBAAsB,CAC5B,CAACiC,SAAS,CAAC;MACVC,IAAI,EAAGW,OAAO,IAAI;QAChB,IAAI,CAACnD,MAAM,CAACgC,KAAK,CAAC,aAAa,EAAE,qBAAqB,EAAEmB,OAAO,CAAC;QAChE,IAAI,CAAC9C,gBAAgB,GAAG,KAAK;QAC7B,IAAI,CAACC,sBAAsB,GAAG,CAAC;QAC/B,IAAI,CAACwE,cAAc,CAAC,IAAI,CAAC;MAC3B,CAAC;MACD9M,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACgI,MAAM,CAAChI,KAAK,CAAC,aAAa,EAAE,8BAA8B,EAAEA,KAAK,CAAC;QACvE,IAAI,CAAC+H,YAAY,CAACqD,SAAS,CAAC,8BAA8B,CAAC;QAC3D,IAAI,CAAC/C,gBAAgB,GAAG,KAAK;MAC/B;KACD,CAAC;EACJ;EAEA;;;EAGAxC,yBAAyBA,CAAA;IACvB,IAAI,CAACmC,MAAM,CAACgC,KAAK,CAAC,aAAa,EAAE,2BAA2B,CAAC;IAC7D,IAAI,CAAC3B,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,sBAAsB,GAAG,CAAC;EACjC;EAEA;;;;EAIA2O,mBAAmBA,CAACC,QAAgB;IAClC;IACAC,MAAM,CAACC,IAAI,CAACF,QAAQ,EAAE,QAAQ,CAAC;IAC/B,IAAI,CAAClP,MAAM,CAACgC,KAAK,CAAC,aAAa,EAAE,4BAA4BkN,QAAQ,EAAE,CAAC;EAC1E;EAEA;;;;;EAKAG,kBAAkBA,CAAA;IAChB;IACA,IAAI,CAACvK,cAAc,EAAE;IAErB;IACA;IACA,IAAI,IAAI,CAACpJ,QAAQ,CAACkP,IAAI,CAAE/E,GAAG,IAAKA,GAAG,CAACvG,IAAI,KAAKxJ,WAAW,CAAC4U,aAAa,CAAC,EAAE;MACvE;MACAzH,UAAU,CAAC,MAAK;QACd,IAAI,CAAChD,GAAG,CAACiD,aAAa,EAAE;MAC1B,CAAC,EAAE,CAAC,CAAC;;EAET;EAEA;;;EAGQwE,mBAAmBA,CAAA;IACzB,IAAI,IAAI,CAACpG,iBAAiB,IAAI,IAAI,CAACpB,YAAY,EAAE7G,EAAE,EAAE;MACnD,IAAI,CAACiI,iBAAiB,GAAG,KAAK;MAC9BoE,YAAY,CAAC,IAAI,CAACkB,WAAW,CAAC;MAE9B,IAAI,CAAC5G,MAAM,CAACgC,KAAK,CAAC,aAAa,EAAE,2BAA2B,CAAC;MAE7D;MACA,MAAMmD,cAAc,GAAG,IAAI,CAACjF,YAAY,EAAE7G,EAAE;MAC5C,IAAI8L,cAAc,EAAE;QAClB,IAAI,CAAC1F,cAAc,CAACqH,UAAU,CAAC3B,cAAc,CAAC,CAAC5C,SAAS,CAAC;UACvDC,IAAI,EAAEA,CAAA,KAAK;YACT,IAAI,CAACxC,MAAM,CAACgC,KAAK,CACf,aAAa,EACb,uCAAuC,CACxC;UACH,CAAC;UACDhK,KAAK,EAAGA,KAAK,IAAI;YACf,IAAI,CAACgI,MAAM,CAAChI,KAAK,CACf,aAAa,EACb,kCAAkC,EAClCA,KAAK,CACN;UACH;SACD,CAAC;;;EAGR;EAEAsX,WAAWA,CAAA;IACT;IACA,IAAI,CAAC5H,mBAAmB,EAAE;IAE1B,IAAI,CAAC7G,aAAa,CAAC0O,WAAW,EAAE;IAChC7J,YAAY,CAAC,IAAI,CAACC,aAAa,CAAC;EAClC;EAEA;;;EAGA6J,qBAAqBA,CAAA;IACnB,IAAI,CAAC1P,MAAM,CAAC2P,QAAQ,CAAC,CAAC,yBAAyB,CAAC,CAAC;EACnD;EAEA;;;EAGAC,iBAAiBA,CAAA;IACf,IAAI,CAACtO,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;IAC5C,IAAI,IAAI,CAACA,eAAe,EAAE;MACxB,IAAI,CAACD,iBAAiB,GAAG,KAAK;;EAElC;EAEA;;;;EAIAjE,WAAWA,CAACyS,KAAa;IACvB,MAAMC,OAAO,GAAG,IAAI,CAAC7T,WAAW,CAACC,GAAG,CAAC,SAAS,CAAC;IAC/C,IAAI4T,OAAO,EAAE;MACX,MAAMC,YAAY,GAAGD,OAAO,CAAChM,KAAK,IAAI,EAAE;MACxCgM,OAAO,CAAC3T,QAAQ,CAAC4T,YAAY,GAAGF,KAAK,CAAC;MACtCC,OAAO,CAACE,WAAW,EAAE;MACrB;MACA7M,UAAU,CAAC,MAAK;QACd,MAAM8M,YAAY,GAAG7I,QAAQ,CAACoG,aAAa,CACzC,uBAAuB,CACJ;QACrB,IAAIyC,YAAY,EAAE;UAChBA,YAAY,CAACC,KAAK,EAAE;;MAExB,CAAC,EAAE,CAAC,CAAC;;EAET;EAEA;;;EAGQ9N,wBAAwBA,CAAA;IAC9B;IACA,MAAM+N,eAAe,GACnB,IAAI,CAACxQ,cAAc,CAACyQ,2BAA2B,EAAE,CAAC3N,SAAS,CAAC;MAC1DC,IAAI,EAAG2N,YAAY,IAAI;QACrB,IAAI,CAACnQ,MAAM,CAACgC,KAAK,CACf,aAAa,EACb,gCAAgCmO,YAAY,CAAC7Q,IAAI,EAAE,CACpD;QAED;QACA,IACE6Q,YAAY,CAAC7Q,IAAI,KAAK,aAAa,IACnC6Q,YAAY,CAAChL,cAAc,KAAK,IAAI,CAACjF,YAAY,EAAE7G,EAAE,EACrD;UACA;UACA,IAAI8W,YAAY,CAAC9W,EAAE,EAAE;YACnB,IAAI,CAACoG,cAAc,CAAC2Q,UAAU,CAAC,CAACD,YAAY,CAAC9W,EAAE,CAAC,CAAC,CAACkJ,SAAS,EAAE;;;MAGnE,CAAC;MACDvK,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACgI,MAAM,CAAChI,KAAK,CACf,aAAa,EACb,gDAAgD,EAChDA,KAAK,CACN;MACH;KACD,CAAC;IACJ,IAAI,CAAC6I,aAAa,CAAC8B,GAAG,CAACsN,eAAe,CAAC;IAEvC;IACA,MAAMI,OAAO,GAAG,IAAI,CAAC5Q,cAAc,CAAC6Q,aAAa,CAAC/N,SAAS,CAAC;MAC1DC,IAAI,EAAG+N,IAAI,IAAI;QACb,IAAIA,IAAI,EAAE;UACR,IAAI,CAACvQ,MAAM,CAACgC,KAAK,CACf,aAAa,EACb,qBAAqBuO,IAAI,CAAClR,MAAM,CAACzI,QAAQ,EAAE,CAC5C;UACD,IAAI,CAACwI,YAAY,GAAGmR,IAAI;UACxB,IAAI,CAAClP,aAAa,GAAG,IAAI;UAEzB;UACA,IAAI,CAAC5B,cAAc,CAAC+Q,IAAI,CAAC,UAAU,CAAC;SACrC,MAAM;UACL,IAAI,CAACnP,aAAa,GAAG,KAAK;UAC1B,IAAI,CAACjC,YAAY,GAAG,IAAI;;MAE5B;KACD,CAAC;IACF,IAAI,CAACyB,aAAa,CAAC8B,GAAG,CAAC0N,OAAO,CAAC;EACjC;EAEA;;;;EAIAI,YAAYA,CAACnR,IAAuB;IAClC,IAAI,CAAC,IAAI,CAAC3I,gBAAgB,IAAI,CAAC,IAAI,CAACA,gBAAgB,CAAC0C,EAAE,EAAE;MACvDqX,OAAO,CAAC1Y,KAAK,CAAC,qDAAqD,CAAC;MACpE;;IAGF,IAAI,CAACgI,MAAM,CAAC+C,IAAI,CACd,aAAa,EACb,yBAAyBzD,IAAI,SAAS,IAAI,CAAC3I,gBAAgB,CAACC,QAAQ,EAAE,CACvE;IAED;IACA,IAAI,CAAC6I,cAAc,CAACgR,YAAY,CAC9B,IAAI,CAAC9Z,gBAAgB,CAAC0C,EAAE,EACxBiG,IAAI,KAAK,OAAO,GAAGvJ,QAAQ,CAACsU,KAAK,GAAGtU,QAAQ,CAACuU,KAAK,EAClD,IAAI,CAACpK,YAAY,EAAE7G,EAAE,CACtB,CAACkJ,SAAS,CAAC;MACVC,IAAI,EAAG+N,IAAI,IAAI;QACb,IAAI,CAACvQ,MAAM,CAAC+C,IAAI,CAAC,aAAa,EAAE,2BAA2B,EAAEwN,IAAI,CAAC;QAClE;MACF,CAAC;;MACDvY,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACgI,MAAM,CAAChI,KAAK,CACf,aAAa,EACb,yCAAyC,EACzCA,KAAK,CACN;QACD,IAAI,CAAC+H,YAAY,CAACqD,SAAS,CACzB,mDAAmD,CACpD;MACH;KACD,CAAC;EACJ;EAEA;;;EAGAlE,UAAUA,CAAA;IACR,IAAI,CAAC,IAAI,CAACE,YAAY,EAAE;MACtB,IAAI,CAACY,MAAM,CAAChI,KAAK,CAAC,aAAa,EAAE,gCAAgC,CAAC;MAClE;;IAGF,IAAI,CAACgI,MAAM,CAAC+C,IAAI,CACd,aAAa,EACb,6BAA6B,IAAI,CAAC3D,YAAY,CAACC,MAAM,CAACzI,QAAQ,EAAE,CACjE;IAED,IAAI,CAAC6I,cAAc,CAACP,UAAU,CAAC,IAAI,CAACE,YAAY,CAAC/F,EAAE,CAAC,CAACkJ,SAAS,CAAC;MAC7DC,IAAI,EAAG+N,IAAI,IAAI;QACb,IAAI,CAACvQ,MAAM,CAAC+C,IAAI,CAAC,aAAa,EAAE,4BAA4B,EAAEwN,IAAI,CAAC;QACnE,IAAI,CAAClP,aAAa,GAAG,KAAK;QAC1B;MACF,CAAC;;MACDrJ,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACgI,MAAM,CAAChI,KAAK,CACf,aAAa,EACb,0CAA0C,EAC1CA,KAAK,CACN;QACD,IAAI,CAAC+H,YAAY,CAACqD,SAAS,CACzB,oDAAoD,CACrD;QACD,IAAI,CAAC/B,aAAa,GAAG,KAAK;QAC1B,IAAI,CAACjC,YAAY,GAAG,IAAI;MAC1B;KACD,CAAC;EACJ;EAEA;;;EAGAL,UAAUA,CAAA;IACR,IAAI,CAAC,IAAI,CAACK,YAAY,EAAE;MACtB,IAAI,CAACY,MAAM,CAAChI,KAAK,CAAC,aAAa,EAAE,+BAA+B,CAAC;MACjE;;IAGF,IAAI,CAACgI,MAAM,CAAC+C,IAAI,CACd,aAAa,EACb,uBAAuB,IAAI,CAAC3D,YAAY,CAACC,MAAM,CAACzI,QAAQ,EAAE,CAC3D;IAED,IAAI,CAAC6I,cAAc,CAACV,UAAU,CAAC,IAAI,CAACK,YAAY,CAAC/F,EAAE,CAAC,CAACkJ,SAAS,CAAC;MAC7DC,IAAI,EAAG+N,IAAI,IAAI;QACb,IAAI,CAACvQ,MAAM,CAAC+C,IAAI,CAAC,aAAa,EAAE,2BAA2B,EAAEwN,IAAI,CAAC;QAClE,IAAI,CAAClP,aAAa,GAAG,KAAK;QAC1B,IAAI,CAACjC,YAAY,GAAG,IAAI;MAC1B,CAAC;MACDpH,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACgI,MAAM,CAAChI,KAAK,CACf,aAAa,EACb,kCAAkC,EAClCA,KAAK,CACN;QACD,IAAI,CAACqJ,aAAa,GAAG,KAAK;QAC1B,IAAI,CAACjC,YAAY,GAAG,IAAI;MAC1B;KACD,CAAC;EACJ;EAEA;;;EAGAuR,OAAOA,CAAA;IACL;IACA,IAAIC,UAAU,GAAQ,IAAI;IAE1B;IACA,MAAMhO,GAAG,GAAG,IAAI,CAACnD,cAAc,CAACoR,WAAW,CAACtO,SAAS,CAAEgO,IAAI,IAAI;MAC7DK,UAAU,GAAGL,IAAI;MAEjB,IAAI,CAACK,UAAU,EAAE;QACf,IAAI,CAAC5Q,MAAM,CAAChI,KAAK,CAAC,aAAa,EAAE,8BAA8B,CAAC;QAChE;;MAGF,IAAI,CAACgI,MAAM,CAAC+C,IAAI,CAAC,aAAa,EAAE,gBAAgB,CAAC;MAEjD,IAAI,CAACtD,cAAc,CAACkR,OAAO,CAACC,UAAU,CAACvX,EAAE,CAAC,CAACkJ,SAAS,CAAC;QACnDC,IAAI,EAAG+N,IAAI,IAAI;UACb,IAAI,CAACvQ,MAAM,CAAC+C,IAAI,CAAC,aAAa,EAAE,4BAA4B,EAAEwN,IAAI,CAAC;QACrE,CAAC;QACDvY,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACgI,MAAM,CAAChI,KAAK,CACf,aAAa,EACb,mCAAmC,EACnCA,KAAK,CACN;QACH;OACD,CAAC;IACJ,CAAC,CAAC;IAEF;IACA4K,GAAG,CAAC2M,WAAW,EAAE;EACnB;;;uBAntDWhQ,oBAAoB,EAAApJ,EAAA,CAAA2a,iBAAA,CAAAC,EAAA,CAAAtR,cAAA,GAAAtJ,EAAA,CAAA2a,iBAAA,CAAAE,EAAA,CAAAC,cAAA,GAAA9a,EAAA,CAAA2a,iBAAA,CAAAI,EAAA,CAAAC,eAAA,GAAAhb,EAAA,CAAA2a,iBAAA,CAAAM,EAAA,CAAAC,WAAA,GAAAlb,EAAA,CAAA2a,iBAAA,CAAAQ,EAAA,CAAAC,iBAAA,GAAApb,EAAA,CAAA2a,iBAAA,CAAAE,EAAA,CAAAQ,MAAA,GAAArb,EAAA,CAAA2a,iBAAA,CAAAW,EAAA,CAAAC,YAAA,GAAAvb,EAAA,CAAA2a,iBAAA,CAAAa,EAAA,CAAAC,aAAA,GAAAzb,EAAA,CAAA2a,iBAAA,CAAA3a,EAAA,CAAA0b,iBAAA;IAAA;EAAA;;;YAApBtS,oBAAoB;MAAAuS,SAAA;MAAAC,SAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;UC/BjC9b,EAAA,CAAAE,cAAA,aAGC;UAuDOF,EAAA,CAAAC,SAAA,aAAmE;UAWrED,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAE,cAAA,cAAsC;UACpCF,EAAA,CAAAC,SAAA,cAAmE;UAWrED,EAAA,CAAAI,YAAA,EAAM;UAIRJ,EAAA,CAAAE,cAAA,cAAyE;UACvEF,EAAA,CAAAC,SAAA,cAAqE;UACvED,EAAA,CAAAI,YAAA,EAAM;UAIRJ,EAAA,CAAAE,cAAA,cAAkC;UACxBF,EAAA,CAAAa,UAAA,mBAAAmb,uDAAA;YAAA,OAASD,GAAA,CAAA1C,qBAAA,EAAuB;UAAA,EAAC;UACvCrZ,EAAA,CAAAC,SAAA,aAAiC;UACnCD,EAAA,CAAAI,YAAA,EAAS;UAETJ,EAAA,CAAAE,cAAA,eAAgC;UAE5BF,EAAA,CAAAC,SAAA,eAGE;UACFD,EAAA,CAAAsC,UAAA,KAAA2Z,qCAAA,mBAGQ;UACVjc,EAAA,CAAAI,YAAA,EAAM;UAGNJ,EAAA,CAAAsC,UAAA,KAAA4Z,oCAAA,kBAWM;UACRlc,EAAA,CAAAI,YAAA,EAAM;UAENJ,EAAA,CAAAE,cAAA,eAA8B;UAEWF,EAAA,CAAAa,UAAA,mBAAAsb,uDAAA;YAAA,OAASJ,GAAA,CAAAzB,YAAA,CAAa,OAAO,CAAC;UAAA,EAAC;UACpEta,EAAA,CAAAC,SAAA,aAAgC;UAClCD,EAAA,CAAAI,YAAA,EAAS;UAGTJ,EAAA,CAAAE,cAAA,kBAAuE;UAAhCF,EAAA,CAAAa,UAAA,mBAAAub,uDAAA;YAAA,OAASL,GAAA,CAAAzB,YAAA,CAAa,OAAO,CAAC;UAAA,EAAC;UACpEta,EAAA,CAAAC,SAAA,aAA4B;UAC9BD,EAAA,CAAAI,YAAA,EAAS;UAGTJ,EAAA,CAAAE,cAAA,eAAsB;UACZF,EAAA,CAAAa,UAAA,mBAAAwb,uDAAA;YAAA,OAASN,GAAA,CAAAnL,mBAAA,EAAqB;UAAA,EAAC;UACrC5Q,EAAA,CAAAC,SAAA,aAA8B;UAChCD,EAAA,CAAAI,YAAA,EAAS;UAGTJ,EAAA,CAAAsC,UAAA,KAAAga,oCAAA,mBAmDM;UACRtc,EAAA,CAAAI,YAAA,EAAM;UAENJ,EAAA,CAAAE,cAAA,kBAAuC;UACrCF,EAAA,CAAAC,SAAA,aAAiC;UACnCD,EAAA,CAAAI,YAAA,EAAS;UAKbJ,EAAA,CAAAE,cAAA,mBAIC;UADCF,EAAA,CAAAa,UAAA,oBAAA0b,qDAAAnV,MAAA;YAAA,OAAU2U,GAAA,CAAA3G,QAAA,CAAAhO,MAAA,CAAgB;UAAA,EAAC;UAG3BpH,EAAA,CAAAsC,UAAA,KAAAka,oCAAA,kBAiBM;UAGNxc,EAAA,CAAAsC,UAAA,KAAAma,oCAAA,kBAwBM;UAGNzc,EAAA,CAAAsC,UAAA,KAAAoa,oCAAA,kBAiBM;UAGN1c,EAAA,CAAAsC,UAAA,KAAAqa,oCAAA,mBAmBM;UAGN3c,EAAA,CAAAsC,UAAA,KAAAsa,6CAAA,2BAmMe;UAGf5c,EAAA,CAAAsC,UAAA,KAAAua,4CAAA,iCAAA7c,EAAA,CAAA8c,sBAAA,CAiBc;UAGd9c,EAAA,CAAAsC,UAAA,KAAAya,oCAAA,kBAoBM;UACR/c,EAAA,CAAAI,YAAA,EAAM;UAGNJ,EAAA,CAAAE,cAAA,eAAsC;UAEpCF,EAAA,CAAAsC,UAAA,KAAA0a,oCAAA,kBAKM;UAGNhd,EAAA,CAAAsC,UAAA,KAAA2a,oCAAA,mBA8BM;UAENjd,EAAA,CAAAE,cAAA,gBAIC;UAFCF,EAAA,CAAAa,UAAA,sBAAAqc,wDAAA;YAAA,OAAYnB,GAAA,CAAA3K,WAAA,EAAa;UAAA,EAAC;UAI1BpR,EAAA,CAAAE,cAAA,eAAkC;UAG9BF,EAAA,CAAAa,UAAA,mBAAAsc,uDAAA;YAAA,OAASpB,GAAA,CAAAxC,iBAAA,EAAmB;UAAA,EAAC;UAI7BvZ,EAAA,CAAAC,SAAA,aAA4B;UAC9BD,EAAA,CAAAI,YAAA,EAAS;UACTJ,EAAA,CAAAE,cAAA,kBAIC;UAFCF,EAAA,CAAAa,UAAA,mBAAAuc,uDAAA;YAAApd,EAAA,CAAAe,aAAA,CAAAsc,IAAA;YAAA,MAAAC,IAAA,GAAAtd,EAAA,CAAAud,WAAA;YAAA,OAASvd,EAAA,CAAAmB,WAAA,CAAAmc,IAAA,CAAAE,KAAA,EAAiB;UAAA,EAAC;UAG3Bxd,EAAA,CAAAC,SAAA,aAAgC;UAChCD,EAAA,CAAAE,cAAA,qBAME;UAHAF,EAAA,CAAAa,UAAA,oBAAA4c,uDAAArW,MAAA;YAAA,OAAU2U,GAAA,CAAAnM,cAAA,CAAAxI,MAAA,CAAsB;UAAA,EAAC;UAHnCpH,EAAA,CAAAI,YAAA,EAME;UAKNJ,EAAA,CAAAsC,UAAA,KAAAob,mDAAA,iCAKsB;UAEtB1d,EAAA,CAAAsC,UAAA,KAAAqb,sCAAA,oBAOE;UAEF3d,EAAA,CAAAsC,UAAA,KAAAsb,uCAAA,qBAOS;UAET5d,EAAA,CAAAsC,UAAA,KAAAub,uCAAA,qBAQS;UACX7d,EAAA,CAAAI,YAAA,EAAO;UAITJ,EAAA,CAAAsC,UAAA,KAAAwb,oCAAA,mBA+BM;UACR9d,EAAA,CAAAI,YAAA,EAAM;;;;;;UA1qBJJ,EAAA,CAAAkC,UAAA,YAAA6Z,GAAA,CAAAhR,aAAA,CAAyB;UAkGjB/K,EAAA,CAAAK,SAAA,IAAqE;UAArEL,EAAA,CAAAkC,UAAA,SAAA6Z,GAAA,CAAAvb,gBAAA,kBAAAub,GAAA,CAAAvb,gBAAA,CAAA4B,KAAA,yCAAApC,EAAA,CAAAqC,aAAA,CAAqE;UAIpErC,EAAA,CAAAK,SAAA,GAAgC;UAAhCL,EAAA,CAAAkC,UAAA,SAAA6Z,GAAA,CAAAvb,gBAAA,kBAAAub,GAAA,CAAAvb,gBAAA,CAAAE,QAAA,CAAgC;UAM/BV,EAAA,CAAAK,SAAA,GAAsB;UAAtBL,EAAA,CAAAkC,UAAA,SAAA6Z,GAAA,CAAAvb,gBAAA,CAAsB;UAiCvBR,EAAA,CAAAK,SAAA,GAAuB;UAAvBL,EAAA,CAAAkC,UAAA,SAAA6Z,GAAA,CAAA/Q,iBAAA,CAAuB;UAkExBhL,EAAA,CAAAK,SAAA,GAAa;UAAbL,EAAA,CAAAkC,UAAA,SAAA6Z,GAAA,CAAA9V,OAAA,CAAa;UAqBhBjG,EAAA,CAAAK,SAAA,GAAmB;UAAnBL,EAAA,CAAAkC,UAAA,SAAA6Z,GAAA,CAAAvR,aAAA,CAAmB;UA2BnBxK,EAAA,CAAAK,SAAA,GAA6C;UAA7CL,EAAA,CAAAkC,UAAA,UAAA6Z,GAAA,CAAAtR,eAAA,IAAAsR,GAAA,CAAAxW,QAAA,CAAAsH,MAAA,KAA6C;UAoB7C7M,EAAA,CAAAK,SAAA,GAAW;UAAXL,EAAA,CAAAkC,UAAA,SAAA6Z,GAAA,CAAAla,KAAA,CAAW;UAqBC7B,EAAA,CAAAK,SAAA,GAAuC;UAAvCL,EAAA,CAAAkC,UAAA,SAAA6Z,GAAA,CAAAxW,QAAA,IAAAwW,GAAA,CAAAxW,QAAA,CAAAsH,MAAA,KAAuC,aAAAkR,GAAA;UA0NhD/d,EAAA,CAAAK,SAAA,GAAc;UAAdL,EAAA,CAAAkC,UAAA,SAAA6Z,GAAA,CAAA9R,QAAA,CAAc;UA0BdjK,EAAA,CAAAK,SAAA,GAAgB;UAAhBL,EAAA,CAAAkC,UAAA,SAAA6Z,GAAA,CAAAvV,UAAA,CAAgB;UAQhBxG,EAAA,CAAAK,SAAA,GAAqB;UAArBL,EAAA,CAAAkC,UAAA,SAAA6Z,GAAA,CAAA9Q,eAAA,CAAqB;UAiCzBjL,EAAA,CAAAK,SAAA,GAAyB;UAAzBL,EAAA,CAAAkC,UAAA,cAAA6Z,GAAA,CAAAnW,WAAA,CAAyB;UAUrB5F,EAAA,CAAAK,SAAA,GAAuC;UAAvCL,EAAA,CAAAkC,UAAA,YAAAlC,EAAA,CAAAge,eAAA,KAAAC,GAAA,EAAAlC,GAAA,CAAA9Q,eAAA,EAAuC;UAsBxCjL,EAAA,CAAAK,SAAA,GAAsB;UAAtBL,EAAA,CAAAkC,UAAA,SAAA6Z,GAAA,CAAA7R,gBAAA,CAAsB;UAOtBlK,EAAA,CAAAK,SAAA,GAAuB;UAAvBL,EAAA,CAAAkC,UAAA,UAAA6Z,GAAA,CAAA7R,gBAAA,CAAuB;UAUvBlK,EAAA,CAAAK,SAAA,GAAmE;UAAnEL,EAAA,CAAAkC,UAAA,UAAA6Z,GAAA,CAAA7R,gBAAA,MAAAgU,QAAA,GAAAnC,GAAA,CAAAnW,WAAA,CAAAC,GAAA,8BAAAqY,QAAA,CAAAzQ,KAAA,SAAmE;UASnEzN,EAAA,CAAAK,SAAA,GAAmE;UAAnEL,EAAA,CAAAkC,UAAA,UAAA6Z,GAAA,CAAA7R,gBAAA,MAAAiU,QAAA,GAAApC,GAAA,CAAAnW,WAAA,CAAAC,GAAA,8BAAAsY,QAAA,CAAA1Q,KAAA,SAAmE;UAWpEzN,EAAA,CAAAK,SAAA,GAAmC;UAAnCL,EAAA,CAAAkC,UAAA,SAAA6Z,GAAA,CAAA7Q,aAAA,IAAA6Q,GAAA,CAAA9S,YAAA,CAAmC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}