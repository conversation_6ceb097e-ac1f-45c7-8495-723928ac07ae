<div class="container mx-auto px-4 py-6">
    <!-- En-tête -->
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800">Mes Plannings</h1>
        <a routerLink="/plannings/nouveau"
           class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors">
            Nouveau Planning
        </a>
    </div>

    <!-- Chargement -->
    <div *ngIf="loading" class="text-center py-8">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto"></div>
    </div>

    <!-- Erreur -->
    <div *ngIf="error" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
        {{ error }}
    </div>

    <!-- Liste vide -->
    <div *ngIf="!loading && plannings.length === 0" class="text-center py-8">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
        </svg>
        <h3 class="mt-2 text-lg font-medium text-gray-900">Aucun planning disponible</h3>
    </div>

    <!-- Liste des plannings -->
    <div *ngIf="!loading && plannings.length > 0" class="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <div *ngFor="let planning of plannings"
             class="bg-white rounded-lg shadow-md p-4 hover:shadow-lg transition-shadow">
            <div class="flex justify-between items-start">
                <div>
                    <h3 class="text-lg font-semibold text-gray-800">
                        <a  class="hover:text-purple-600">
                            {{ planning.titre }}
                        </a>
                    </h3>
                    <p class="text-sm text-gray-600 mt-1" [innerHTML]="(planning.description || 'Aucune description') | highlightPresence"></p>
                </div>
                <button (click)="deletePlanning(planning._id)" class="text-red-500 hover:text-red-700">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                </button>
            </div>

            <div class="mt-3 flex items-center text-sm text-gray-500">
                <svg class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                {{ planning.dateDebut | date:'mediumDate' }} - {{ planning.dateFin | date:'mediumDate' }}
            </div>

            <div class="mt-4 pt-3 border-t border-gray-100 flex justify-between items-center">
                <span class="text-sm text-gray-500">
                    {{ planning.reunions?.length || 0 }} réunion(s)
                </span>
              <a (click)="GotoDetail(planning.id)"
                 class="text-sm text-purple-600 hover:text-purple-800">
                Voir détails →
              </a>

            </div>
        </div>
    </div>
</div>
