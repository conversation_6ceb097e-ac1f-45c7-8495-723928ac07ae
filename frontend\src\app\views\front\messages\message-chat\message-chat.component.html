<div
  class="flex flex-col h-full bg-[#edf1f4] dark:bg-[#121212] relative overflow-hidden futuristic-chat-container dark"
  [ngClass]="selectedTheme"
>
  <!-- Animations CSS -->
  <style>
    @keyframes borderFlow {
      0% {
        background-position: 0% 0%;
      }
      100% {
        background-position: 200% 0%;
      }
    }

    @keyframes ambientGlow {
      0% {
        opacity: 0;
        transform: scale(0.95);
      }
      100% {
        opacity: 0.5;
        transform: scale(1.05);
      }
    }

    @keyframes rotateHalo {
      0% {
        transform: rotate(0deg);
      }
      100% {
        transform: rotate(360deg);
      }
    }

    @keyframes pulseButton {
      0% {
        box-shadow: 0 0 10px rgba(0, 247, 255, 0.5),
          0 0 20px rgba(0, 247, 255, 0.2);
        transform: scale(1);
      }
      50% {
        box-shadow: 0 0 15px rgba(0, 247, 255, 0.7),
          0 0 30px rgba(0, 247, 255, 0.4);
        transform: scale(1.05);
      }
      100% {
        box-shadow: 0 0 10px rgba(0, 247, 255, 0.5),
          0 0 20px rgba(0, 247, 255, 0.2);
        transform: scale(1);
      }
    }

    /* Styles pour la recherche */
    .search-highlight {
      background-color: rgba(79, 95, 173, 0.3);
      color: #4f5fad;
      padding: 1px 2px;
      border-radius: 2px;
      font-weight: 500;
    }

    .dark .search-highlight {
      background-color: rgba(109, 120, 201, 0.3);
      color: #6d78c9;
    }

    .highlight-message {
      animation: highlightPulse 2s ease-in-out;
      border: 2px solid #4f5fad !important;
      box-shadow: 0 0 15px rgba(79, 95, 173, 0.3) !important;
    }

    .dark .highlight-message {
      border-color: #6d78c9 !important;
      box-shadow: 0 0 15px rgba(109, 120, 201, 0.3) !important;
    }

    @keyframes highlightPulse {
      0%,
      100% {
        transform: scale(1);
        opacity: 1;
      }
      50% {
        transform: scale(1.02);
        opacity: 0.9;
      }
    }

    /* Ajustement de la hauteur avec la barre de recherche */
    .futuristic-messages-container.with-search-bar {
      height: calc(100vh - 200px) !important;
    }

    /* Styles pour les messages épinglés */
    .futuristic-message-pinned {
      border-left: 3px solid #4f5fad !important;
      background: linear-gradient(
        135deg,
        rgba(79, 95, 173, 0.05),
        rgba(79, 95, 173, 0.02)
      ) !important;
      box-shadow: 0 2px 8px rgba(79, 95, 173, 0.1) !important;
    }

    .dark .futuristic-message-pinned {
      border-left-color: #6d78c9 !important;
      background: linear-gradient(
        135deg,
        rgba(109, 120, 201, 0.08),
        rgba(109, 120, 201, 0.03)
      ) !important;
      box-shadow: 0 2px 8px rgba(109, 120, 201, 0.15) !important;
    }

    /* Animation pour le panneau des messages épinglés */
    .pinned-messages-panel {
      animation: slideDown 0.3s ease-out;
    }

    @keyframes slideDown {
      from {
        opacity: 0;
        transform: translateY(-10px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    /* Styles pour la classe line-clamp-2 */
    .line-clamp-2 {
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
  </style>
  <!-- Background decorative elements - Grid pattern only -->
  <div class="absolute inset-0 overflow-hidden pointer-events-none">
    <!-- Animated grid pattern -->
    <div class="absolute inset-0 opacity-5 dark:opacity-[0.03]">
      <div class="h-full grid grid-cols-12">
        <div class="border-r border-[#4f5fad] dark:border-[#6d78c9]"></div>
        <div class="border-r border-[#4f5fad] dark:border-[#6d78c9]"></div>
        <div class="border-r border-[#4f5fad] dark:border-[#6d78c9]"></div>
        <div class="border-r border-[#4f5fad] dark:border-[#6d78c9]"></div>
        <div class="border-r border-[#4f5fad] dark:border-[#6d78c9]"></div>
        <div class="border-r border-[#4f5fad] dark:border-[#6d78c9]"></div>
        <div class="border-r border-[#4f5fad] dark:border-[#6d78c9]"></div>
        <div class="border-r border-[#4f5fad] dark:border-[#6d78c9]"></div>
        <div class="border-r border-[#4f5fad] dark:border-[#6d78c9]"></div>
        <div class="border-r border-[#4f5fad] dark:border-[#6d78c9]"></div>
        <div class="border-r border-[#4f5fad] dark:border-[#6d78c9]"></div>
      </div>
      <div class="w-full grid grid-rows-12">
        <div class="border-b border-[#4f5fad] dark:border-[#6d78c9]"></div>
        <div class="border-b border-[#4f5fad] dark:border-[#6d78c9]"></div>
        <div class="border-b border-[#4f5fad] dark:border-[#6d78c9]"></div>
        <div class="border-b border-[#4f5fad] dark:border-[#6d78c9]"></div>
        <div class="border-b border-[#4f5fad] dark:border-[#6d78c9]"></div>
        <div class="border-b border-[#4f5fad] dark:border-[#6d78c9]"></div>
        <div class="border-b border-[#4f5fad] dark:border-[#6d78c9]"></div>
        <div class="border-b border-[#4f5fad] dark:border-[#6d78c9]"></div>
        <div class="border-b border-[#4f5fad] dark:border-[#6d78c9]"></div>
        <div class="border-b border-[#4f5fad] dark:border-[#6d78c9]"></div>
        <div class="border-b border-[#4f5fad] dark:border-[#6d78c9]"></div>
      </div>
    </div>

    <!-- Horizontal scan line effect for dark mode -->
    <div class="absolute inset-0 opacity-0 dark:opacity-100 overflow-hidden">
      <div class="h-px w-full bg-[#00f7ff]/20 absolute animate-scan"></div>
    </div>
  </div>

  <!-- En-tête style WhatsApp -->
  <div class="whatsapp-chat-header">
    <button (click)="goBackToConversations()" class="whatsapp-action-button">
      <i class="fas fa-arrow-left"></i>
    </button>

    <div class="whatsapp-user-info">
      <div class="whatsapp-avatar">
        <img
          [src]="otherParticipant?.image || 'assets/images/default-avatar.png'"
          alt="User avatar"
        />
        <span
          *ngIf="otherParticipant?.isOnline"
          class="whatsapp-online-indicator"
        ></span>
      </div>

      <!-- Nom et statut style WhatsApp -->
      <div *ngIf="otherParticipant" class="whatsapp-user-details">
        <span class="whatsapp-username">
          {{ otherParticipant.username }}
        </span>
        <span class="whatsapp-status">
          {{
            otherParticipant.isOnline
              ? "En ligne"
              : formatLastActive(otherParticipant.lastActive)
          }}
        </span>
      </div>
    </div>

    <div class="whatsapp-actions">
      <!-- Bouton d'appel audio -->
      <button class="whatsapp-action-button" (click)="initiateCall('AUDIO')">
        <i class="fas fa-phone-alt"></i>
      </button>

      <!-- Bouton d'appel vidéo -->
      <button class="whatsapp-action-button" (click)="initiateCall('VIDEO')">
        <i class="fas fa-video"></i>
      </button>

      <!-- Bouton de recherche -->
      <button
        (click)="toggleSearchBar()"
        class="whatsapp-action-button"
        [ngClass]="{ 'text-[#4f5fad] dark:text-[#6d78c9]': showSearchBar }"
      >
        <i class="fas fa-search"></i>
      </button>

      <!-- Bouton des messages épinglés -->
      <button
        (click)="togglePinnedMessages()"
        class="whatsapp-action-button relative"
        [ngClass]="{ 'text-[#4f5fad] dark:text-[#6d78c9]': showPinnedMessages }"
        [title]="'Messages épinglés (' + getPinnedMessagesCount() + ')'"
      >
        <i class="fas fa-thumbtack"></i>
        <!-- Badge du nombre de messages épinglés -->
        <span
          *ngIf="getPinnedMessagesCount() > 0"
          class="absolute -top-1 -right-1 w-4 h-4 bg-[#4f5fad] dark:bg-[#6d78c9] text-white text-xs rounded-full flex items-center justify-center font-medium"
        >
          {{ getPinnedMessagesCount() > 9 ? "9+" : getPinnedMessagesCount() }}
        </span>
      </button>

      <!-- Bouton des notifications -->
      <button
        (click)="toggleNotificationPanel()"
        class="whatsapp-action-button relative"
        [ngClass]="{
          'text-[#4f5fad] dark:text-[#6d78c9]': showNotificationPanel
        }"
        title="Notifications"
      >
        <i class="fas fa-bell"></i>
        <!-- Badge du nombre de notifications non lues -->
        <span
          *ngIf="unreadNotificationCount > 0"
          class="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-r from-[#ff6b69] to-[#ee5a52] text-white text-xs rounded-full flex items-center justify-center font-medium animate-pulse"
        >
          {{ unreadNotificationCount > 9 ? "9+" : unreadNotificationCount }}
        </span>
      </button>

      <!-- Bouton du statut utilisateur -->
      <div class="relative">
        <button
          (click)="toggleStatusSelector()"
          class="whatsapp-action-button relative"
          [ngClass]="{
            'text-[#4f5fad] dark:text-[#6d78c9]': showStatusSelector
          }"
          title="Statut utilisateur"
        >
          <i
            [class]="getStatusIcon(currentUserStatus)"
            [ngClass]="getStatusColor(currentUserStatus)"
          ></i>
          <!-- Indicateur de mise à jour -->
          <span
            *ngIf="isUpdatingStatus"
            class="absolute -top-1 -right-1 w-3 h-3 bg-blue-500 rounded-full animate-pulse"
          ></span>
        </button>

        <!-- Menu déroulant du statut -->
        <div
          *ngIf="showStatusSelector"
          class="absolute right-0 mt-2 w-56 bg-white dark:bg-[#1e1e1e] rounded-lg shadow-lg z-50 border border-[#edf1f4]/50 dark:border-[#2a2a2a] overflow-hidden"
        >
          <div
            class="p-3 text-xs text-[#6d6870] dark:text-[#a0a0a0] border-b border-[#edf1f4]/50 dark:border-[#2a2a2a] bg-gradient-to-r from-[#4f5fad]/10 to-[#6d78c9]/10"
          >
            <div class="flex items-center justify-between">
              <span>Statut actuel</span>
              <span
                class="font-medium"
                [ngClass]="getStatusColor(currentUserStatus)"
              >
                {{ getStatusText(currentUserStatus) }}
              </span>
            </div>
          </div>

          <div class="p-1">
            <button
              (click)="updateUserStatus('online'); toggleStatusSelector()"
              [disabled]="isUpdatingStatus"
              class="block w-full text-left px-3 py-2 text-sm rounded-md hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 transition-colors"
              [ngClass]="{
                'bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20':
                  currentUserStatus === 'online'
              }"
            >
              <div class="flex items-center">
                <i class="fas fa-circle text-green-500 mr-3 text-xs"></i>
                <div>
                  <div class="font-medium">En ligne</div>
                  <div class="text-xs text-[#6d6870] dark:text-[#a0a0a0]">
                    Disponible pour discuter
                  </div>
                </div>
              </div>
            </button>

            <button
              (click)="updateUserStatus('away'); toggleStatusSelector()"
              [disabled]="isUpdatingStatus"
              class="block w-full text-left px-3 py-2 text-sm rounded-md hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 transition-colors"
              [ngClass]="{
                'bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20':
                  currentUserStatus === 'away'
              }"
            >
              <div class="flex items-center">
                <i class="fas fa-clock text-yellow-500 mr-3 text-xs"></i>
                <div>
                  <div class="font-medium">Absent</div>
                  <div class="text-xs text-[#6d6870] dark:text-[#a0a0a0]">
                    Absent temporairement
                  </div>
                </div>
              </div>
            </button>

            <button
              (click)="updateUserStatus('busy'); toggleStatusSelector()"
              [disabled]="isUpdatingStatus"
              class="block w-full text-left px-3 py-2 text-sm rounded-md hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 transition-colors"
              [ngClass]="{
                'bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20':
                  currentUserStatus === 'busy'
              }"
            >
              <div class="flex items-center">
                <i class="fas fa-minus-circle text-red-500 mr-3 text-xs"></i>
                <div>
                  <div class="font-medium">Occupé</div>
                  <div class="text-xs text-[#6d6870] dark:text-[#a0a0a0]">
                    Ne pas déranger
                  </div>
                </div>
              </div>
            </button>

            <button
              (click)="updateUserStatus('offline'); toggleStatusSelector()"
              [disabled]="isUpdatingStatus"
              class="block w-full text-left px-3 py-2 text-sm rounded-md hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 transition-colors"
              [ngClass]="{
                'bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20':
                  currentUserStatus === 'offline'
              }"
            >
              <div class="flex items-center">
                <i class="far fa-circle text-gray-500 mr-3 text-xs"></i>
                <div>
                  <div class="font-medium">Hors ligne</div>
                  <div class="text-xs text-[#6d6870] dark:text-[#a0a0a0]">
                    Invisible pour tous
                  </div>
                </div>
              </div>
            </button>
          </div>

          <div class="border-t border-[#edf1f4]/50 dark:border-[#2a2a2a] p-1">
            <button
              (click)="toggleUserStatusPanel(); toggleStatusSelector()"
              class="block w-full text-left px-3 py-2 text-sm rounded-md hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 transition-colors"
            >
              <div class="flex items-center">
                <i
                  class="fas fa-users text-[#4f5fad] dark:text-[#6d78c9] mr-3 text-xs"
                ></i>
                <div>
                  <div class="font-medium">Voir tous les utilisateurs</div>
                  <div class="text-xs text-[#6d6870] dark:text-[#a0a0a0]">
                    {{ getOnlineUsersCount() }} en ligne
                  </div>
                </div>
              </div>
            </button>
          </div>
        </div>
      </div>

      <!-- Sélecteur de thème -->
      <div class="relative">
        <button (click)="toggleThemeSelector()" class="whatsapp-action-button">
          <i class="fas fa-palette"></i>
        </button>

        <!-- Menu déroulant des thèmes -->
        <div
          *ngIf="showThemeSelector"
          class="absolute right-0 mt-2 w-48 bg-white dark:bg-[#1e1e1e] rounded-lg shadow-lg z-50 border border-[#edf1f4]/50 dark:border-[#2a2a2a] overflow-hidden"
        >
          <div
            class="p-2 text-xs text-[#6d6870] dark:text-[#a0a0a0] border-b border-[#edf1f4]/50 dark:border-[#2a2a2a]"
          >
            Choisir un thème
          </div>
          <div class="p-1">
            <a
              href="javascript:void(0)"
              (click)="changeTheme('theme-default')"
              class="block w-full text-left px-3 py-2 text-sm rounded-md hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 transition-colors"
            >
              <div class="flex items-center">
                <div class="w-4 h-4 rounded-full bg-[#4f5fad] mr-2"></div>
                <div>Par défaut</div>
              </div>
            </a>
            <a
              href="javascript:void(0)"
              (click)="changeTheme('theme-feminine')"
              class="block w-full text-left px-3 py-2 text-sm rounded-md hover:bg-[#ff6b9d]/10 dark:hover:bg-[#ff6b9d]/10 transition-colors"
            >
              <div class="flex items-center">
                <div class="w-4 h-4 rounded-full bg-[#ff6b9d] mr-2"></div>
                <div>Rose</div>
              </div>
            </a>
            <a
              href="javascript:void(0)"
              (click)="changeTheme('theme-masculine')"
              class="block w-full text-left px-3 py-2 text-sm rounded-md hover:bg-[#3d85c6]/10 dark:hover:bg-[#3d85c6]/10 transition-colors"
            >
              <div class="flex items-center">
                <div class="w-4 h-4 rounded-full bg-[#3d85c6] mr-2"></div>
                <div>Bleu</div>
              </div>
            </a>
            <a
              href="javascript:void(0)"
              (click)="changeTheme('theme-neutral')"
              class="block w-full text-left px-3 py-2 text-sm rounded-md hover:bg-[#6aa84f]/10 dark:hover:bg-[#6aa84f]/10 transition-colors"
            >
              <div class="flex items-center">
                <div class="w-4 h-4 rounded-full bg-[#6aa84f] mr-2"></div>
                <div>Vert</div>
              </div>
            </a>
          </div>
        </div>
      </div>

      <!-- Bouton historique des appels -->
      <button
        (click)="toggleCallHistoryPanel()"
        class="whatsapp-action-button relative"
        [ngClass]="{
          'text-[#4f5fad] dark:text-[#6d78c9]': showCallHistoryPanel
        }"
        title="Historique des appels"
      >
        <i class="fas fa-history"></i>
      </button>

      <!-- Bouton statistiques d'appels -->
      <button
        (click)="toggleCallStatsPanel()"
        class="whatsapp-action-button relative"
        [ngClass]="{ 'text-[#4f5fad] dark:text-[#6d78c9]': showCallStatsPanel }"
        title="Statistiques d'appels"
      >
        <i class="fas fa-chart-bar"></i>
      </button>

      <!-- Bouton messages vocaux -->
      <button
        (click)="toggleVoiceMessagesPanel()"
        class="whatsapp-action-button relative"
        [ngClass]="{
          'text-[#4f5fad] dark:text-[#6d78c9]': showVoiceMessagesPanel
        }"
        title="Messages vocaux"
      >
        <i class="fas fa-microphone"></i>
        <!-- Badge du nombre de messages vocaux -->
        <span
          *ngIf="voiceMessages.length > 0"
          class="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-r from-[#4f5fad] to-[#6d78c9] text-white text-xs rounded-full flex items-center justify-center font-medium"
        >
          {{ voiceMessages.length > 9 ? "9+" : voiceMessages.length }}
        </span>
      </button>

      <button class="whatsapp-action-button">
        <i class="fas fa-ellipsis-v"></i>
      </button>
    </div>
  </div>

  <!-- Barre de recherche -->
  <div
    *ngIf="showSearchBar"
    class="search-bar bg-white dark:bg-[#1e1e1e] border-b border-[#edf1f4]/50 dark:border-[#2a2a2a] px-4 py-3 relative z-10"
  >
    <div class="flex items-center space-x-3">
      <!-- Icône de recherche -->
      <div class="text-[#6d6870] dark:text-[#a0a0a0]">
        <i class="fas fa-search text-sm"></i>
      </div>

      <!-- Champ de recherche -->
      <div class="flex-1 relative">
        <input
          type="text"
          [(ngModel)]="searchQuery"
          (input)="onSearchInput($event)"
          (keydown)="onSearchKeyPress($event)"
          placeholder="Rechercher dans cette conversation..."
          class="w-full px-3 py-2 text-sm bg-[#edf1f4]/50 dark:bg-[#2a2a2a]/50 border border-[#edf1f4] dark:border-[#3a3a3a] rounded-lg focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] text-[#6d6870] dark:text-[#a0a0a0] placeholder-[#6d6870]/50 dark:placeholder-[#a0a0a0]/50 transition-colors"
          autofocus
        />

        <!-- Indicateur de chargement -->
        <div
          *ngIf="isSearching"
          class="absolute right-3 top-1/2 transform -translate-y-1/2"
        >
          <div
            class="w-4 h-4 border-2 border-[#4f5fad]/20 dark:border-[#6d78c9]/20 border-t-[#4f5fad] dark:border-t-[#6d78c9] rounded-full animate-spin"
          ></div>
        </div>

        <!-- Bouton de suppression -->
        <button
          *ngIf="searchQuery && !isSearching"
          (click)="clearSearch()"
          class="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-colors"
        >
          <i class="fas fa-times text-xs"></i>
        </button>
      </div>

      <!-- Bouton de fermeture -->
      <button
        (click)="toggleSearchBar()"
        class="text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-colors"
      >
        <i class="fas fa-times"></i>
      </button>
    </div>

    <!-- Résultats de recherche -->
    <div
      *ngIf="searchMode && searchResults.length > 0"
      class="mt-3 max-h-40 overflow-y-auto border border-[#edf1f4]/50 dark:border-[#3a3a3a] rounded-lg bg-[#edf1f4]/30 dark:bg-[#2a2a2a]/30"
    >
      <div
        class="p-2 text-xs text-[#6d6870] dark:text-[#a0a0a0] border-b border-[#edf1f4]/50 dark:border-[#3a3a3a]"
      >
        {{ searchResults.length }} résultat(s) trouvé(s)
      </div>
      <div class="max-h-32 overflow-y-auto">
        <button
          *ngFor="let result of searchResults"
          (click)="result.id && navigateToMessage(result.id)"
          class="w-full text-left px-3 py-2 hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 transition-colors border-b border-[#edf1f4]/30 dark:border-[#3a3a3a]/30 last:border-b-0"
        >
          <div class="flex items-start space-x-2">
            <div
              class="w-6 h-6 rounded-full bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 flex items-center justify-center flex-shrink-0 mt-0.5"
            >
              <i
                class="fas fa-comment text-xs text-[#4f5fad] dark:text-[#6d78c9]"
              ></i>
            </div>
            <div class="flex-1 min-w-0">
              <div class="text-xs text-[#6d6870] dark:text-[#a0a0a0] mb-1">
                {{ formatMessageTime(result.timestamp) }}
              </div>
              <div
                class="text-sm text-[#6d6870] dark:text-[#a0a0a0] truncate"
                [innerHTML]="
                  highlightSearchTerms(result.content || '', searchQuery)
                "
              ></div>
            </div>
          </div>
        </button>
      </div>
    </div>

    <!-- Message aucun résultat -->
    <div
      *ngIf="
        searchMode &&
        searchResults.length === 0 &&
        !isSearching &&
        searchQuery.length >= 2
      "
      class="mt-3 p-3 text-center text-sm text-[#6d6870] dark:text-[#a0a0a0] bg-[#edf1f4]/30 dark:bg-[#2a2a2a]/30 rounded-lg"
    >
      <i
        class="fas fa-search text-lg mb-2 block text-[#6d6870]/50 dark:text-[#a0a0a0]/50"
      ></i>
      Aucun message trouvé pour "{{ searchQuery }}"
    </div>
  </div>

  <!-- Panneau des messages épinglés -->
  <div
    *ngIf="showPinnedMessages"
    class="pinned-messages-panel bg-white dark:bg-[#1e1e1e] border-b border-[#edf1f4]/50 dark:border-[#2a2a2a] relative z-10"
  >
    <!-- En-tête du panneau -->
    <div
      class="flex items-center justify-between p-4 border-b border-[#edf1f4]/50 dark:border-[#2a2a2a]"
    >
      <div class="flex items-center space-x-2">
        <i class="fas fa-thumbtack text-[#4f5fad] dark:text-[#6d78c9]"></i>
        <h3 class="text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0]">
          Messages épinglés ({{ getPinnedMessagesCount() }})
        </h3>
      </div>
      <button
        (click)="togglePinnedMessages()"
        class="w-6 h-6 flex items-center justify-center text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 rounded-full transition-colors"
      >
        <i class="fas fa-times text-xs"></i>
      </button>
    </div>

    <!-- Liste des messages épinglés -->
    <div class="max-h-48 overflow-y-auto">
      <!-- État de chargement -->
      <div *ngIf="pinnedMessages.length === 0" class="p-4 text-center">
        <div class="text-[#6d6870]/70 dark:text-[#a0a0a0]/70">
          <i class="fas fa-thumbtack text-2xl mb-2 block opacity-50"></i>
          <div class="text-sm">Aucun message épinglé</div>
        </div>
      </div>

      <!-- Messages épinglés -->
      <div
        *ngIf="pinnedMessages.length > 0"
        class="divide-y divide-[#edf1f4]/30 dark:divide-[#3a3a3a]/30"
      >
        <button
          *ngFor="let pinnedMessage of pinnedMessages"
          (click)="scrollToPinnedMessage(pinnedMessage.id!)"
          class="w-full text-left p-3 hover:bg-[#4f5fad]/5 dark:hover:bg-[#6d78c9]/5 transition-colors"
        >
          <div class="flex items-start space-x-3">
            <!-- Avatar de l'expéditeur -->
            <div class="flex-shrink-0">
              <img
                [src]="
                  pinnedMessage.sender?.image ||
                  'assets/images/default-avatar.png'
                "
                [alt]="pinnedMessage.sender?.username || 'User'"
                class="w-8 h-8 rounded-full object-cover"
                onerror="this.src='assets/images/default-avatar.png'"
              />
            </div>

            <!-- Contenu du message -->
            <div class="flex-1 min-w-0">
              <!-- En-tête avec nom et date -->
              <div class="flex items-center justify-between mb-1">
                <span
                  class="text-xs font-medium text-[#4f5fad] dark:text-[#6d78c9]"
                >
                  {{ pinnedMessage.sender?.username || "Utilisateur inconnu" }}
                </span>
                <span class="text-xs text-[#6d6870]/70 dark:text-[#a0a0a0]/70">
                  {{ formatMessageTime(pinnedMessage.timestamp) }}
                </span>
              </div>

              <!-- Contenu du message -->
              <div
                class="text-sm text-[#6d6870] dark:text-[#a0a0a0] line-clamp-2"
              >
                <span *ngIf="pinnedMessage.content">{{
                  pinnedMessage.content
                }}</span>
                <span
                  *ngIf="hasImage(pinnedMessage)"
                  class="italic flex items-center"
                >
                  <i class="fas fa-image mr-1"></i>
                  Image
                </span>
                <span
                  *ngIf="isVoiceMessage(pinnedMessage)"
                  class="italic flex items-center"
                >
                  <i class="fas fa-microphone mr-1"></i>
                  Message vocal
                </span>
              </div>

              <!-- Indicateur d'épinglage -->
              <div class="flex items-center mt-1">
                <i
                  class="fas fa-thumbtack text-xs text-[#4f5fad] dark:text-[#6d78c9] mr-1"
                ></i>
                <span class="text-xs text-[#4f5fad] dark:text-[#6d78c9]">
                  Épinglé
                  {{
                    pinnedMessage.pinnedAt
                      ? "le " + formatMessageDate(pinnedMessage.pinnedAt)
                      : ""
                  }}
                </span>
              </div>
            </div>

            <!-- Icône de navigation -->
            <div class="flex-shrink-0 text-[#6d6870]/50 dark:text-[#a0a0a0]/50">
              <i class="fas fa-chevron-right text-xs"></i>
            </div>
          </div>
        </button>
      </div>
    </div>
  </div>

  <!-- Zone de messages futuriste -->
  <div
    #messagesContainer
    class="futuristic-messages-container"
    [ngClass]="{ 'with-search-bar': showSearchBar }"
    (scroll)="onScroll($event)"
  >
    <!-- État de chargement (initial) -->
    <div *ngIf="loading" class="flex justify-center items-center h-full">
      <div class="flex flex-col items-center">
        <div class="relative">
          <div
            class="w-12 h-12 border-4 border-[#4f5fad]/20 dark:border-[#6d78c9]/20 border-t-[#4f5fad] dark:border-t-[#6d78c9] rounded-full animate-spin mb-3"
          ></div>
          <!-- Glow effect -->
          <div
            class="absolute inset-0 bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 blur-xl rounded-full transform scale-150 -z-10"
          ></div>
        </div>
        <div
          class="text-[#6d6870] dark:text-[#a0a0a0] mt-3 bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent font-medium"
        >
          Initializing communication...
        </div>
      </div>
    </div>

    <!-- Indicateur de chargement de messages supplémentaires -->
    <div
      *ngIf="isLoadingMore"
      class="flex justify-center py-2 sticky top-0 z-10"
    >
      <div
        class="flex flex-col items-center backdrop-blur-sm bg-white/50 dark:bg-[#1e1e1e]/50 px-4 py-2 rounded-full shadow-sm"
      >
        <div class="flex space-x-2 mb-1">
          <div
            class="w-2 h-2 bg-[#4f5fad] dark:bg-[#6d78c9] rounded-full animate-pulse shadow-[0_0_5px_rgba(79,95,173,0.5)] dark:shadow-[0_0_5px_rgba(109,120,201,0.5)]"
          ></div>
          <div
            class="w-2 h-2 bg-[#4f5fad] dark:bg-[#6d78c9] rounded-full animate-pulse shadow-[0_0_5px_rgba(79,95,173,0.5)] dark:shadow-[0_0_5px_rgba(109,120,201,0.5)]"
            style="animation-delay: 0.2s"
          ></div>
          <div
            class="w-2 h-2 bg-[#4f5fad] dark:bg-[#6d78c9] rounded-full animate-pulse shadow-[0_0_5px_rgba(79,95,173,0.5)] dark:shadow-[0_0_5px_rgba(109,120,201,0.5)]"
            style="animation-delay: 0.4s"
          ></div>
        </div>
        <div class="text-xs text-[#6d6870] dark:text-[#a0a0a0]">
          Retrieving data...
        </div>
      </div>
    </div>

    <!-- Indicateur de début de conversation -->
    <div
      *ngIf="!hasMoreMessages && messages.length > 0"
      class="flex justify-center py-2 mb-2"
    >
      <div class="flex items-center w-full max-w-xs">
        <div
          class="flex-1 h-px bg-gradient-to-r from-transparent via-[#4f5fad]/20 dark:via-[#6d78c9]/20 to-transparent"
        ></div>
        <div
          class="px-3 text-xs bg-gradient-to-r from-[#3d4a85] to-[#4f5fad] dark:from-[#6d78c9] dark:to-[#4f5fad] bg-clip-text text-transparent font-medium"
        >
          Communication Initialized
        </div>
        <div
          class="flex-1 h-px bg-gradient-to-r from-transparent via-[#4f5fad]/20 dark:via-[#6d78c9]/20 to-transparent"
        ></div>
      </div>
    </div>

    <!-- État d'erreur -->
    <div
      *ngIf="error"
      class="bg-[#ff6b69]/10 dark:bg-[#ff6b69]/5 border border-[#ff6b69] dark:border-[#ff6b69]/30 rounded-lg p-4 mx-auto max-w-md my-4 backdrop-blur-sm"
    >
      <div class="flex items-start">
        <div class="text-[#ff6b69] dark:text-[#ff8785] mr-3 text-xl relative">
          <i class="fas fa-exclamation-triangle"></i>
          <!-- Glow effect -->
          <div
            class="absolute inset-0 bg-[#ff6b69]/20 dark:bg-[#ff8785]/20 blur-xl rounded-full transform scale-150 -z-10"
          ></div>
        </div>
        <div>
          <h3 class="font-medium text-[#ff6b69] dark:text-[#ff8785] mb-1">
            System Error: Communication Failure
          </h3>
          <p class="text-sm text-[#6d6870] dark:text-[#a0a0a0]">{{ error }}</p>
        </div>
      </div>
    </div>

    <!-- Messages -->
    <ng-container *ngIf="messages && messages.length > 0; else noMessages">
      <div
        *ngFor="let message of messages; let i = index"
        class="futuristic-message-wrapper"
        [attr.data-message-id]="message.id"
      >
        <!-- Séparateur de date futuriste -->
        <div *ngIf="shouldShowDateHeader(i)" class="futuristic-date-separator">
          <div class="futuristic-date-line"></div>
          <div class="futuristic-date-text">
            {{ formatMessageDate(message?.timestamp) }}
          </div>
          <div class="futuristic-date-line"></div>
        </div>

        <!-- Conteneur de message avec alignement -->
        <div
          class="futuristic-message group relative"
          [ngClass]="{
            'futuristic-message-current-user':
              message?.sender?.id === currentUserId ||
              message?.sender?._id === currentUserId ||
              message?.senderId === currentUserId,
            'futuristic-message-other-user': !(
              message?.sender?.id === currentUserId ||
              message?.sender?._id === currentUserId ||
              message?.senderId === currentUserId
            )
          }"
        >
          <!-- Bouton de réaction rapide (pour tous les messages) -->
          <button
            *ngIf="message.id && !message.isDeleted"
            (click)="toggleReactionPicker(message.id)"
            data-reaction-trigger
            class="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-8 h-8 bg-white dark:bg-[#2a2a2a] border border-[#edf1f4] dark:border-[#3a3a3a] hover:border-[#4f5fad] dark:hover:border-[#6d78c9] text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] rounded-full flex items-center justify-center text-sm opacity-0 group-hover:opacity-100 transition-all duration-200 z-10 shadow-lg"
          >
            <i class="fas fa-smile"></i>
          </button>

          <!-- Sélecteur de réactions global (positionné sous le message) -->
          <div
            *ngIf="message.id && showReactionPicker[message.id]"
            class="reaction-picker absolute -bottom-12 left-1/2 transform -translate-x-1/2 bg-white dark:bg-[#2a2a2a] rounded-lg shadow-lg border border-[#edf1f4]/50 dark:border-[#3a3a3a] z-30 p-2"
          >
            <div class="flex space-x-1">
              <button
                *ngFor="let emoji of availableReactions"
                (click)="reactToMessage(message.id, emoji)"
                class="w-8 h-8 flex items-center justify-center text-lg hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 rounded transition-colors"
                [title]="'Réagir avec ' + emoji"
              >
                {{ emoji }}
              </button>
            </div>
          </div>
          <!-- Avatar pour les messages reçus -->
          <div
            *ngIf="
              !(
                message?.sender?.id === currentUserId ||
                message?.sender?._id === currentUserId ||
                message?.senderId === currentUserId
              )
            "
            class="futuristic-avatar"
          >
            <img
              [src]="
                message?.sender?.image || 'assets/images/default-avatar.png'
              "
              alt="User avatar"
              onerror="this.src='assets/images/default-avatar.png'"
            />
          </div>

          <!-- Contenu du message -->
          <div class="futuristic-message-content">
            <!-- Contenu textuel -->
            <div
              *ngIf="
                message?.content &&
                !hasImage(message) &&
                !isVoiceMessage(message)
              "
              class="futuristic-message-bubble relative"
              [ngClass]="{
                'futuristic-message-pending': message.isPending,
                'futuristic-message-sending':
                  message.isPending && !message.isError,
                'futuristic-message-error': message.isError,
                'futuristic-message-pinned': isMessagePinned(message)
              }"
              [id]="'message-' + message.id"
              (mouseenter)="message.id && canEditMessage(message) ? null : null"
              (mouseleave)="message.id && canEditMessage(message) ? null : null"
            >
              <!-- Bouton d'options (trois points) pour les messages de l'utilisateur -->
              <button
                *ngIf="
                  message.id && canEditMessage(message) && !message.isDeleted
                "
                (click)="toggleMessageOptions(message.id)"
                class="absolute -top-2 -right-2 w-6 h-6 bg-[#4f5fad]/80 dark:bg-[#6d78c9]/80 hover:bg-[#4f5fad] dark:hover:bg-[#6d78c9] text-white rounded-full flex items-center justify-center text-xs opacity-0 group-hover:opacity-100 transition-all duration-200 z-10"
              >
                <i class="fas fa-ellipsis-v text-xs"></i>
              </button>

              <!-- Menu d'options -->
              <div
                *ngIf="message.id && showMessageOptions[message.id]"
                class="absolute top-6 right-0 bg-white dark:bg-[#2a2a2a] rounded-lg shadow-lg border border-[#edf1f4]/50 dark:border-[#3a3a3a] z-20 min-w-[140px] overflow-hidden"
              >
                <button
                  (click)="startReplyToMessage(message)"
                  class="w-full px-3 py-2 text-left text-sm hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 flex items-center space-x-2 text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-colors"
                >
                  <i class="fas fa-reply text-xs"></i>
                  <span>Répondre</span>
                </button>
                <button
                  (click)="openForwardModal(message)"
                  class="w-full px-3 py-2 text-left text-sm hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 flex items-center space-x-2 text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-colors"
                >
                  <i class="fas fa-share text-xs"></i>
                  <span>Transférer</span>
                </button>
                <button
                  (click)="showPinConfirmation(message.id)"
                  class="w-full px-3 py-2 text-left text-sm hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 flex items-center space-x-2 text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-colors"
                >
                  <i [class]="getPinIcon(message)" class="text-xs"></i>
                  <span>{{ getPinDisplayText(message) }}</span>
                </button>
                <button
                  *ngIf="canEditMessage(message)"
                  (click)="startEditMessage(message)"
                  class="w-full px-3 py-2 text-left text-sm hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 flex items-center space-x-2 text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-colors"
                >
                  <i class="fas fa-edit text-xs"></i>
                  <span>Modifier</span>
                </button>
                <button
                  *ngIf="canEditMessage(message)"
                  (click)="showDeleteConfirmation(message.id)"
                  class="w-full px-3 py-2 text-left text-sm hover:bg-[#ff6b69]/10 flex items-center space-x-2 text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#ff6b69] transition-colors"
                >
                  <i class="fas fa-trash text-xs"></i>
                  <span>Supprimer</span>
                </button>
              </div>

              <!-- Confirmation de suppression -->
              <div
                *ngIf="message.id && showDeleteConfirm[message.id]"
                class="absolute top-6 right-0 bg-white dark:bg-[#2a2a2a] rounded-lg shadow-lg border border-[#ff6b69]/50 z-20 min-w-[200px] p-3"
              >
                <div class="text-sm text-[#6d6870] dark:text-[#a0a0a0] mb-3">
                  Supprimer ce message ?
                </div>
                <div class="flex space-x-2">
                  <button
                    (click)="cancelDeleteMessage(message.id)"
                    class="px-3 py-1 text-xs bg-[#edf1f4] dark:bg-[#3a3a3a] hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 text-[#6d6870] dark:text-[#a0a0a0] rounded transition-colors"
                  >
                    Annuler
                  </button>
                  <button
                    (click)="confirmDeleteMessage(message.id)"
                    class="px-3 py-1 text-xs bg-[#ff6b69] hover:bg-[#ff6b69]/80 text-white rounded transition-colors"
                  >
                    Supprimer
                  </button>
                </div>
              </div>

              <!-- Confirmation d'épinglage -->
              <div
                *ngIf="message.id && showPinConfirm[message.id]"
                class="absolute top-6 right-0 bg-white dark:bg-[#2a2a2a] rounded-lg shadow-lg border border-[#4f5fad]/50 dark:border-[#6d78c9]/50 z-20 min-w-[200px] p-3"
              >
                <div class="text-sm text-[#6d6870] dark:text-[#a0a0a0] mb-3">
                  {{
                    isMessagePinned(message)
                      ? "Désépingler ce message ?"
                      : "Épingler ce message ?"
                  }}
                </div>
                <div class="flex space-x-2">
                  <button
                    (click)="cancelPinConfirmation(message.id)"
                    class="px-3 py-1 text-xs bg-[#edf1f4] dark:bg-[#3a3a3a] hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 text-[#6d6870] dark:text-[#a0a0a0] rounded transition-colors"
                  >
                    Annuler
                  </button>
                  <button
                    (click)="togglePinMessage(message)"
                    [disabled]="isPinning[message.id]"
                    class="px-3 py-1 text-xs bg-[#4f5fad] dark:bg-[#6d78c9] hover:bg-[#4f5fad]/80 dark:hover:bg-[#6d78c9]/80 text-white rounded transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-1"
                  >
                    <i
                      *ngIf="isPinning[message.id]"
                      class="fas fa-spinner fa-spin text-xs"
                    ></i>
                    <span>{{
                      isMessagePinned(message) ? "Désépingler" : "Épingler"
                    }}</span>
                  </button>
                </div>
              </div>

              <!-- Contenu du message (normal ou en édition) -->
              <div
                *ngIf="editingMessageId !== message.id"
                class="futuristic-message-text"
              >
                <!-- Indicateur de message épinglé -->
                <div
                  *ngIf="isMessagePinned(message)"
                  class="flex items-center mb-1"
                >
                  <i
                    class="fas fa-thumbtack text-xs text-[#4f5fad] dark:text-[#6d78c9] mr-1"
                  ></i>
                  <span
                    class="text-xs text-[#4f5fad] dark:text-[#6d78c9] font-medium"
                    >Épinglé</span
                  >
                </div>

                <!-- Message auquel on répond -->
                <div
                  *ngIf="message.replyTo"
                  class="reply-to-message bg-[#edf1f4]/30 dark:bg-[#3a3a3a]/30 border-l-2 border-[#4f5fad]/50 dark:border-[#6d78c9]/50 p-2 mb-2 rounded-r text-xs"
                >
                  <div
                    class="text-[#4f5fad] dark:text-[#6d78c9] font-medium mb-1"
                  >
                    {{ message.replyTo.sender?.username || "Utilisateur" }}
                  </div>
                  <div class="text-[#6d6870] dark:text-[#a0a0a0] line-clamp-2">
                    {{ message.replyTo.content || "Message" }}
                  </div>
                </div>

                <span *ngIf="!message.isDeleted">{{ message.content }}</span>
                <span
                  *ngIf="message.isDeleted"
                  class="italic text-[#6d6870] dark:text-[#a0a0a0]"
                >
                  {{ message.content }}
                </span>
                <span
                  *ngIf="message.isEdited && !message.isDeleted"
                  class="text-xs text-[#6d6870] dark:text-[#a0a0a0] ml-2"
                >
                  (modifié)
                </span>
              </div>

              <!-- Mode édition -->
              <div
                *ngIf="editingMessageId === message.id"
                class="futuristic-message-edit"
              >
                <textarea
                  [(ngModel)]="editingContent"
                  (keydown)="onEditKeyPress($event, message.id)"
                  class="w-full p-2 text-sm bg-transparent border border-[#4f5fad]/30 dark:border-[#6d78c9]/30 rounded resize-none focus:outline-none focus:border-[#4f5fad] dark:focus:border-[#6d78c9] text-[#6d6870] dark:text-[#a0a0a0]"
                  rows="2"
                  placeholder="Modifier le message..."
                  autofocus
                ></textarea>
                <div class="flex justify-end space-x-2 mt-2">
                  <button
                    (click)="cancelEditMessage()"
                    class="px-3 py-1 text-xs bg-[#edf1f4] dark:bg-[#3a3a3a] hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 text-[#6d6870] dark:text-[#a0a0a0] rounded transition-colors"
                  >
                    Annuler
                  </button>
                  <button
                    (click)="saveEditMessage(message.id)"
                    class="px-3 py-1 text-xs bg-[#4f5fad] hover:bg-[#4f5fad]/80 dark:bg-[#6d78c9] dark:hover:bg-[#6d78c9]/80 text-white rounded transition-colors"
                  >
                    Sauvegarder
                  </button>
                </div>
              </div>

              <!-- Réactions existantes -->
              <div
                *ngIf="message.reactions && message.reactions.length > 0"
                class="flex flex-wrap gap-1 mt-2"
              >
                <button
                  *ngFor="let reaction of getUniqueReactions(message)"
                  (click)="
                    message.id && onReactionClick(message.id, reaction.emoji)
                  "
                  class="flex items-center space-x-1 px-2 py-1 rounded-full text-xs transition-all duration-200"
                  [ngClass]="{
                    'bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 text-[#4f5fad] dark:text-[#6d78c9] border border-[#4f5fad]/30 dark:border-[#6d78c9]/30':
                      hasUserReacted(message, reaction.emoji),
                    'bg-[#edf1f4]/50 dark:bg-[#3a3a3a]/50 text-[#6d6870] dark:text-[#a0a0a0] border border-[#edf1f4] dark:border-[#3a3a3a] hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10':
                      !hasUserReacted(message, reaction.emoji)
                  }"
                  [title]="reaction.count + ' réaction(s)'"
                >
                  <span>{{ reaction.emoji }}</span>
                  <span class="font-medium">{{ reaction.count }}</span>
                </button>
              </div>

              <!-- Heure du message avec statut de lecture -->
              <div class="futuristic-message-info">
                <span class="futuristic-message-time">
                  {{ formatMessageTime(message?.timestamp) }}
                </span>
                <span
                  *ngIf="
                    message?.sender?.id === currentUserId ||
                    message?.sender?._id === currentUserId ||
                    message?.senderId === currentUserId
                  "
                  class="futuristic-message-status"
                >
                  <i *ngIf="message?.isRead" class="fas fa-check-double"></i>
                  <i *ngIf="!message?.isRead" class="fas fa-check"></i>
                </span>
              </div>
            </div>

            <!-- Message vocal moderne (style WhatsApp) -->
            <div
              *ngIf="isVoiceMessage(message)"
              class="voice-message-modern"
              [ngClass]="{
                'voice-message-sent':
                  message?.sender?.id === currentUserId ||
                  message?.sender?._id === currentUserId ||
                  message?.senderId === currentUserId,
                'voice-message-received': !(
                  message?.sender?.id === currentUserId ||
                  message?.sender?._id === currentUserId ||
                  message?.senderId === currentUserId
                ),
                'voice-message-pending': message.isPending,
                'voice-message-error': message.isError
              }"
            >
              <!-- Bouton play/pause compact -->
              <button class="voice-play-btn-modern">
                <i class="fas fa-play"></i>
              </button>

              <!-- Forme d'onde compacte -->
              <div class="voice-waveform-modern">
                <div
                  *ngFor="
                    let i of [
                      0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16,
                      17, 18, 19
                    ]
                  "
                  class="voice-bar-modern"
                  [style.height.px]="getVoiceBarHeight(i)"
                ></div>
              </div>

              <!-- Durée du message vocal -->
              <div class="voice-duration-modern">
                {{ formatVoiceDuration(getVoiceMessageDuration(message)) }}
              </div>

              <!-- Composant caché pour la lecture -->
              <app-voice-message-player
                [audioUrl]="getVoiceMessageUrl(message)"
                [duration]="getVoiceMessageDuration(message)"
                class="hidden"
              ></app-voice-message-player>

              <!-- Réactions existantes pour messages vocaux -->
              <div
                *ngIf="message.reactions && message.reactions.length > 0"
                class="flex flex-wrap gap-1 mt-2"
              >
                <button
                  *ngFor="let reaction of getUniqueReactions(message)"
                  (click)="
                    message.id && onReactionClick(message.id, reaction.emoji)
                  "
                  class="flex items-center space-x-1 px-2 py-1 rounded-full text-xs transition-all duration-200"
                  [ngClass]="{
                    'bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 text-[#4f5fad] dark:text-[#6d78c9] border border-[#4f5fad]/30 dark:border-[#6d78c9]/30':
                      hasUserReacted(message, reaction.emoji),
                    'bg-[#edf1f4]/50 dark:bg-[#3a3a3a]/50 text-[#6d6870] dark:text-[#a0a0a0] border border-[#edf1f4] dark:border-[#3a3a3a] hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10':
                      !hasUserReacted(message, reaction.emoji)
                  }"
                  [title]="reaction.count + ' réaction(s)'"
                >
                  <span>{{ reaction.emoji }}</span>
                  <span class="font-medium">{{ reaction.count }}</span>
                </button>
              </div>

              <!-- Heure du message avec statut de lecture -->
              <div class="futuristic-message-info">
                <span class="futuristic-message-time">
                  {{ formatMessageTime(message?.timestamp) }}
                </span>
                <span
                  *ngIf="
                    message?.sender?.id === currentUserId ||
                    message?.sender?._id === currentUserId ||
                    message?.senderId === currentUserId
                  "
                  class="futuristic-message-status"
                >
                  <i *ngIf="message?.isRead" class="fas fa-check-double"></i>
                  <i *ngIf="!message?.isRead" class="fas fa-check"></i>
                </span>
              </div>
            </div>

            <!-- Contenu image -->
            <div
              *ngIf="hasImage(message)"
              class="futuristic-message-image-container"
              [ngClass]="{
                'futuristic-message-pending': message.isPending,
                'futuristic-message-sending':
                  message.isPending && !message.isError,
                'futuristic-message-error': message.isError
              }"
            >
              <div class="futuristic-image-wrapper">
                <a
                  [href]="getImageUrl(message)"
                  target="_blank"
                  class="futuristic-message-image-link"
                >
                  <img
                    [src]="getImageUrl(message)"
                    class="futuristic-message-image"
                    alt="Image"
                  />
                </a>
                <div class="futuristic-image-overlay">
                  <i class="fas fa-expand"></i>
                </div>
              </div>

              <!-- Réactions existantes pour messages avec images -->
              <div
                *ngIf="message.reactions && message.reactions.length > 0"
                class="flex flex-wrap gap-1 mt-2"
              >
                <button
                  *ngFor="let reaction of getUniqueReactions(message)"
                  (click)="
                    message.id && onReactionClick(message.id, reaction.emoji)
                  "
                  class="flex items-center space-x-1 px-2 py-1 rounded-full text-xs transition-all duration-200"
                  [ngClass]="{
                    'bg-[#4f5fad]/20 dark:bg-[#6d78c9]/20 text-[#4f5fad] dark:text-[#6d78c9] border border-[#4f5fad]/30 dark:border-[#6d78c9]/30':
                      hasUserReacted(message, reaction.emoji),
                    'bg-[#edf1f4]/50 dark:bg-[#3a3a3a]/50 text-[#6d6870] dark:text-[#a0a0a0] border border-[#edf1f4] dark:border-[#3a3a3a] hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10':
                      !hasUserReacted(message, reaction.emoji)
                  }"
                  [title]="reaction.count + ' réaction(s)'"
                >
                  <span>{{ reaction.emoji }}</span>
                  <span class="font-medium">{{ reaction.count }}</span>
                </button>
              </div>

              <!-- Heure du message avec statut de lecture -->
              <div class="futuristic-message-info">
                <span class="futuristic-message-time">
                  {{ formatMessageTime(message?.timestamp) }}
                </span>
                <span
                  *ngIf="
                    message?.sender?.id === currentUserId ||
                    message?.sender?._id === currentUserId ||
                    message?.senderId === currentUserId
                  "
                  class="futuristic-message-status"
                >
                  <i *ngIf="message?.isRead" class="fas fa-check-double"></i>
                  <i *ngIf="!message?.isRead" class="fas fa-check"></i>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </ng-container>

    <!-- Template pour aucun message -->
    <ng-template #noMessages>
      <div *ngIf="!loading && !error" class="futuristic-no-messages">
        <div class="futuristic-no-messages-icon">
          <i class="fas fa-satellite-dish"></i>
        </div>
        <div class="futuristic-no-messages-text">
          Aucun message dans cette conversation.
          <br />Établissez le premier contact pour commencer.
        </div>
        <button
          (click)="messageForm.get('content')?.setValue('Bonjour!')"
          class="futuristic-start-button"
        >
          <i class="fas fa-paper-plane"></i>
          Initialiser la communication
        </button>
      </div>
    </ng-template>

    <!-- Indicateur de frappe -->
    <div *ngIf="isTyping" class="futuristic-typing-indicator">
      <div class="futuristic-avatar">
        <img
          [src]="otherParticipant?.image || 'assets/images/default-avatar.png'"
          alt="User avatar"
        />
      </div>
      <div class="futuristic-typing-bubble">
        <div class="futuristic-typing-dots">
          <div class="futuristic-typing-dot"></div>
          <div
            class="futuristic-typing-dot"
            style="animation-delay: 0.2s"
          ></div>
          <div
            class="futuristic-typing-dot"
            style="animation-delay: 0.4s"
          ></div>
        </div>
      </div>
    </div>
  </div>

  <!-- Zone de saisie style WhatsApp -->
  <div class="whatsapp-input-container">
    <!-- Aperçu du fichier -->
    <div *ngIf="previewUrl" class="whatsapp-file-preview">
      <img [src]="previewUrl" class="whatsapp-preview-image" />
      <button (click)="removeAttachment()" class="whatsapp-remove-button">
        <i class="fas fa-times"></i>
      </button>
    </div>

    <!-- Sélecteur d'émojis -->
    <div *ngIf="showEmojiPicker" class="whatsapp-emoji-picker">
      <div class="whatsapp-emoji-categories">
        <button class="whatsapp-emoji-category active">
          <i class="far fa-smile"></i>
        </button>
        <button class="whatsapp-emoji-category">
          <i class="fas fa-cat"></i>
        </button>
        <button class="whatsapp-emoji-category">
          <i class="fas fa-hamburger"></i>
        </button>
        <button class="whatsapp-emoji-category">
          <i class="fas fa-futbol"></i>
        </button>
        <button class="whatsapp-emoji-category">
          <i class="fas fa-car"></i>
        </button>
        <button class="whatsapp-emoji-category">
          <i class="fas fa-lightbulb"></i>
        </button>
      </div>
      <div class="whatsapp-emoji-list">
        <button
          *ngFor="let emoji of commonEmojis"
          class="whatsapp-emoji-item"
          (click)="insertEmoji(emoji)"
        >
          {{ emoji }}
        </button>
      </div>
    </div>

    <!-- Aperçu du message auquel on répond -->
    <div
      *ngIf="replyingToMessage"
      class="reply-preview bg-[#edf1f4]/50 dark:bg-[#2a2a2a]/50 border-l-4 border-[#4f5fad] dark:border-[#6d78c9] p-3 mx-4 mb-2 rounded-r-lg"
    >
      <div class="flex items-start justify-between">
        <div class="flex-1">
          <div
            class="text-xs text-[#4f5fad] dark:text-[#6d78c9] font-medium mb-1"
          >
            Réponse à {{ replyingToMessage.sender?.username || "Utilisateur" }}
          </div>
          <div class="text-sm text-[#6d6870] dark:text-[#a0a0a0] line-clamp-2">
            {{ replyingToMessage.content || "Message" }}
          </div>
        </div>
        <button
          (click)="cancelReply()"
          class="ml-2 w-6 h-6 flex items-center justify-center text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] transition-colors"
        >
          <i class="fas fa-times text-xs"></i>
        </button>
      </div>
    </div>

    <form
      [formGroup]="messageForm"
      (ngSubmit)="sendMessage()"
      class="whatsapp-input-form"
    >
      <!-- Boutons d'outils style WhatsApp -->
      <div class="whatsapp-input-tools">
        <button
          type="button"
          (click)="toggleEmojiPicker()"
          class="whatsapp-tool-button"
          [ngClass]="{ active: showEmojiPicker }"
        >
          <i class="far fa-smile"></i>
        </button>
        <button
          type="button"
          (click)="fileInput.click()"
          class="whatsapp-tool-button"
        >
          <i class="fas fa-paperclip"></i>
          <input
            #fileInput
            type="file"
            (change)="onFileSelected($event)"
            class="hidden"
            accept="image/*"
          />
        </button>
      </div>

      <!-- Composant d'enregistrement vocal -->
      <app-voice-recorder
        *ngIf="isRecordingVoice"
        (recordingComplete)="onVoiceRecordingComplete($event)"
        (recordingCancelled)="onVoiceRecordingCancelled()"
        [maxDuration]="60"
      ></app-voice-recorder>

      <input
        *ngIf="!isRecordingVoice"
        formControlName="content"
        type="text"
        placeholder="Message"
        (input)="onTyping()"
        class="whatsapp-input-field"
      />

      <button
        type="button"
        *ngIf="!isRecordingVoice && messageForm.get('content')?.value === ''"
        (click)="toggleVoiceRecording()"
        class="whatsapp-voice-button"
      >
        <i class="fas fa-microphone"></i>
      </button>

      <button
        type="submit"
        *ngIf="!isRecordingVoice && messageForm.get('content')?.value !== ''"
        [disabled]="isUploading || (messageForm.invalid && !selectedFile)"
        class="whatsapp-send-button"
      >
        <i *ngIf="!isUploading" class="fas fa-paper-plane"></i>
        <i *ngIf="isUploading" class="fas fa-spinner fa-spin"></i>
      </button>
    </form>
  </div>

  <!-- Modal d'appel entrant -->
  <div *ngIf="showCallModal && incomingCall" class="whatsapp-call-modal">
    <div class="whatsapp-call-modal-content">
      <div class="whatsapp-call-header">
        <div class="whatsapp-call-avatar">
          <img
            [src]="
              incomingCall.caller?.image || 'assets/images/default-avatar.png'
            "
            alt="Caller avatar"
          />
        </div>
        <h3 class="whatsapp-call-name">{{ incomingCall.caller?.username }}</h3>
        <p class="whatsapp-call-status">
          {{
            incomingCall.type === "AUDIO"
              ? "Appel audio entrant"
              : "Appel vidéo entrant"
          }}
        </p>
      </div>
      <div class="whatsapp-call-actions">
        <button (click)="rejectCall()" class="whatsapp-call-reject">
          <i class="fas fa-phone-slash"></i>
          <span>Rejeter</span>
        </button>
        <button (click)="acceptCall()" class="whatsapp-call-accept">
          <i class="fas fa-phone"></i>
          <span>Accepter</span>
        </button>
      </div>
    </div>
  </div>

  <!-- Modal d'appel actif -->
  <div
    *ngIf="showActiveCallModal && activeCall"
    class="active-call-modal"
    [ngClass]="{ minimized: isCallMinimized }"
    (mousemove)="onCallMouseMove()"
  >
    <!-- Interface d'appel vidéo -->
    <div *ngIf="activeCall.type === 'VIDEO'" class="video-call-interface">
      <!-- Vidéo distante (plein écran) -->
      <video #remoteVideo class="remote-video" autoplay playsinline></video>

      <!-- Vidéo locale (petit écran) -->
      <video
        #localVideo
        class="local-video"
        autoplay
        muted
        playsinline
        [style.display]="isVideoEnabled ? 'block' : 'none'"
      ></video>

      <!-- Avatar local si vidéo désactivée -->
      <div *ngIf="!isVideoEnabled" class="local-avatar">
        <img [src]="'assets/images/default-avatar.png'" alt="Vous" />
      </div>
    </div>

    <!-- Interface d'appel audio -->
    <div *ngIf="activeCall.type === 'AUDIO'" class="audio-call-interface">
      <div class="audio-call-content">
        <div class="call-participant-avatar">
          <img
            [src]="
              activeCall.caller?.image ||
              activeCall.recipient?.image ||
              'assets/images/default-avatar.png'
            "
            alt="Participant"
          />
        </div>
        <h3 class="call-participant-name">
          {{ activeCall.caller?.username || activeCall.recipient?.username }}
        </h3>
        <p class="call-status">{{ formatCallDuration(callDuration) }}</p>
        <div class="call-quality-indicator">
          <i
            class="fas fa-signal"
            [ngClass]="{
              'text-green-500': callQuality === 'excellent',
              'text-yellow-500': callQuality === 'good',
              'text-red-500': callQuality === 'poor',
              'text-gray-500': callQuality === 'connecting'
            }"
          ></i>
          <span class="quality-text">{{ callQuality }}</span>
        </div>
      </div>
    </div>

    <!-- Contrôles d'appel -->
    <div
      class="call-controls"
      [ngClass]="{ hidden: !showCallControls }"
      *ngIf="!isCallMinimized"
    >
      <!-- Informations d'appel (pour vidéo) -->
      <div *ngIf="activeCall.type === 'VIDEO'" class="call-info">
        <span class="call-duration">{{
          formatCallDuration(callDuration)
        }}</span>
        <span class="call-participant">
          {{ activeCall.caller?.username || activeCall.recipient?.username }}
        </span>
      </div>

      <!-- Boutons de contrôle -->
      <div class="control-buttons">
        <!-- Bouton muet -->
        <button
          (click)="toggleCallMute()"
          class="control-btn"
          [ngClass]="{ active: isCallMuted }"
        >
          <i
            class="fas"
            [ngClass]="isCallMuted ? 'fa-microphone-slash' : 'fa-microphone'"
          ></i>
        </button>

        <!-- Bouton vidéo (seulement pour appels vidéo) -->
        <button
          *ngIf="activeCall.type === 'VIDEO'"
          (click)="toggleCallVideo()"
          class="control-btn"
          [ngClass]="{ active: !isVideoEnabled }"
        >
          <i
            class="fas"
            [ngClass]="isVideoEnabled ? 'fa-video' : 'fa-video-slash'"
          ></i>
        </button>

        <!-- Bouton raccrocher -->
        <button (click)="endCall()" class="control-btn end-call">
          <i class="fas fa-phone-slash"></i>
        </button>

        <!-- Bouton minimiser -->
        <button (click)="toggleCallMinimize()" class="control-btn">
          <i
            class="fas"
            [ngClass]="isCallMinimized ? 'fa-expand' : 'fa-compress'"
          ></i>
        </button>
      </div>
    </div>

    <!-- Interface minimisée -->
    <div *ngIf="isCallMinimized" class="minimized-call-interface">
      <div class="minimized-info">
        <span class="minimized-duration">{{
          formatCallDuration(callDuration)
        }}</span>
        <span class="minimized-participant">
          {{ activeCall.caller?.username || activeCall.recipient?.username }}
        </span>
      </div>
      <div class="minimized-controls">
        <button (click)="toggleCallMinimize()" class="minimized-btn">
          <i class="fas fa-expand"></i>
        </button>
        <button (click)="endCall()" class="minimized-btn end-call">
          <i class="fas fa-phone-slash"></i>
        </button>
      </div>
    </div>
  </div>

  <!-- Modal de transfert de message -->
  <div
    *ngIf="showForwardModal"
    class="fixed inset-0 bg-black/50 dark:bg-black/70 flex items-center justify-center z-50 p-4"
    (click)="closeForwardModal()"
  >
    <div
      class="bg-white dark:bg-[#1e1e1e] rounded-lg shadow-xl max-w-md w-full max-h-[80vh] overflow-hidden"
      (click)="$event.stopPropagation()"
    >
      <!-- En-tête du modal -->
      <div
        class="flex items-center justify-between p-4 border-b border-[#edf1f4]/50 dark:border-[#3a3a3a]"
      >
        <h3 class="text-lg font-semibold text-[#6d6870] dark:text-[#a0a0a0]">
          Transférer le message
        </h3>
        <button
          (click)="closeForwardModal()"
          class="w-8 h-8 flex items-center justify-center text-[#6d6870] dark:text-[#a0a0a0] hover:text-[#4f5fad] dark:hover:text-[#6d78c9] hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 rounded-full transition-colors"
        >
          <i class="fas fa-times"></i>
        </button>
      </div>

      <!-- Aperçu du message à transférer -->
      <div
        class="p-4 border-b border-[#edf1f4]/50 dark:border-[#3a3a3a] bg-[#edf1f4]/30 dark:bg-[#2a2a2a]/30"
      >
        <div class="text-xs text-[#6d6870] dark:text-[#a0a0a0] mb-2">
          Message à transférer :
        </div>
        <div
          class="bg-white dark:bg-[#2a2a2a] rounded-lg p-3 border border-[#edf1f4] dark:border-[#3a3a3a]"
        >
          <div class="text-sm text-[#6d6870] dark:text-[#a0a0a0] line-clamp-3">
            {{ forwardingMessage?.content || "Message sans contenu" }}
          </div>
          <div *ngIf="hasImage(forwardingMessage)" class="mt-2">
            <div class="text-xs text-[#6d6870]/70 dark:text-[#a0a0a0]/70">
              <i class="fas fa-image mr-1"></i>
              Image jointe
            </div>
          </div>
          <div *ngIf="isVoiceMessage(forwardingMessage)" class="mt-2">
            <div class="text-xs text-[#6d6870]/70 dark:text-[#a0a0a0]/70">
              <i class="fas fa-microphone mr-1"></i>
              Message vocal
            </div>
          </div>
        </div>
      </div>

      <!-- Actions de sélection -->
      <div class="p-4 border-b border-[#edf1f4]/50 dark:border-[#3a3a3a]">
        <div class="flex items-center justify-between mb-3">
          <span class="text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0]">
            Sélectionner les conversations
          </span>
          <div class="flex space-x-2">
            <button
              *ngIf="!areAllConversationsSelected()"
              (click)="selectAllConversations()"
              class="text-xs px-2 py-1 bg-[#4f5fad]/10 dark:bg-[#6d78c9]/10 text-[#4f5fad] dark:text-[#6d78c9] hover:bg-[#4f5fad]/20 dark:hover:bg-[#6d78c9]/20 rounded transition-colors"
            >
              Tout sélectionner
            </button>
            <button
              *ngIf="areAllConversationsSelected()"
              (click)="deselectAllConversations()"
              class="text-xs px-2 py-1 bg-[#ff6b69]/10 text-[#ff6b69] hover:bg-[#ff6b69]/20 rounded transition-colors"
            >
              Tout désélectionner
            </button>
          </div>
        </div>
        <div class="text-xs text-[#6d6870]/70 dark:text-[#a0a0a0]/70">
          {{ selectedConversations.length }} conversation(s) sélectionnée(s)
        </div>
      </div>

      <!-- Liste des conversations -->
      <div class="max-h-60 overflow-y-auto">
        <!-- État de chargement -->
        <div *ngIf="isLoadingConversations" class="p-4 text-center">
          <div class="flex items-center justify-center space-x-2">
            <div
              class="w-4 h-4 border-2 border-[#4f5fad]/20 dark:border-[#6d78c9]/20 border-t-[#4f5fad] dark:border-t-[#6d78c9] rounded-full animate-spin"
            ></div>
            <span class="text-sm text-[#6d6870] dark:text-[#a0a0a0]"
              >Chargement des conversations...</span
            >
          </div>
        </div>

        <!-- Liste des conversations -->
        <div
          *ngIf="!isLoadingConversations && availableConversations.length > 0"
        >
          <button
            *ngFor="let conversation of availableConversations"
            (click)="
              conversation.id && toggleConversationSelection(conversation.id)
            "
            class="w-full p-3 flex items-center space-x-3 hover:bg-[#4f5fad]/5 dark:hover:bg-[#6d78c9]/5 transition-colors border-b border-[#edf1f4]/30 dark:border-[#3a3a3a]/30 last:border-b-0"
          >
            <!-- Checkbox -->
            <div class="flex-shrink-0">
              <div
                class="w-5 h-5 rounded border-2 flex items-center justify-center transition-colors"
                [ngClass]="{
                  'border-[#4f5fad] dark:border-[#6d78c9] bg-[#4f5fad] dark:bg-[#6d78c9]':
                    conversation.id && isConversationSelected(conversation.id),
                  'border-[#edf1f4] dark:border-[#3a3a3a] bg-transparent':
                    !conversation.id || !isConversationSelected(conversation.id)
                }"
              >
                <i
                  *ngIf="
                    conversation.id && isConversationSelected(conversation.id)
                  "
                  class="fas fa-check text-xs text-white"
                ></i>
              </div>
            </div>

            <!-- Avatar -->
            <div class="flex-shrink-0">
              <img
                [src]="getConversationDisplayImage(conversation)"
                [alt]="getConversationDisplayName(conversation)"
                class="w-10 h-10 rounded-full object-cover"
                onerror="this.src='assets/images/default-avatar.png'"
              />
            </div>

            <!-- Informations de la conversation -->
            <div class="flex-1 min-w-0 text-left">
              <div
                class="font-medium text-[#6d6870] dark:text-[#a0a0a0] truncate"
              >
                {{ getConversationDisplayName(conversation) }}
              </div>
              <div
                class="text-xs text-[#6d6870]/70 dark:text-[#a0a0a0]/70 truncate"
              >
                {{ conversation.isGroup ? "Groupe" : "Conversation privée" }}
                <span *ngIf="conversation.participants">
                  • {{ conversation.participants.length }} participant(s)</span
                >
              </div>
            </div>
          </button>
        </div>

        <!-- Aucune conversation disponible -->
        <div
          *ngIf="!isLoadingConversations && availableConversations.length === 0"
          class="p-4 text-center"
        >
          <div class="text-[#6d6870]/70 dark:text-[#a0a0a0]/70">
            <i class="fas fa-comments text-2xl mb-2 block"></i>
            <div class="text-sm">Aucune autre conversation disponible</div>
          </div>
        </div>
      </div>

      <!-- Actions du modal -->
      <div
        class="p-4 border-t border-[#edf1f4]/50 dark:border-[#3a3a3a] flex space-x-3"
      >
        <button
          (click)="closeForwardModal()"
          class="flex-1 px-4 py-2 text-sm bg-[#edf1f4] dark:bg-[#3a3a3a] hover:bg-[#4f5fad]/10 dark:hover:bg-[#6d78c9]/10 text-[#6d6870] dark:text-[#a0a0a0] rounded-lg transition-colors"
        >
          Annuler
        </button>
        <button
          (click)="forwardMessage()"
          [disabled]="selectedConversations.length === 0 || isForwarding"
          class="flex-1 px-4 py-2 text-sm bg-[#4f5fad] dark:bg-[#6d78c9] hover:bg-[#4f5fad]/80 dark:hover:bg-[#6d78c9]/80 text-white rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
        >
          <i *ngIf="isForwarding" class="fas fa-spinner fa-spin"></i>
          <i *ngIf="!isForwarding" class="fas fa-share"></i>
          <span>{{ isForwarding ? "Transfert..." : "Transférer" }}</span>
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Panneau de notifications avancé -->
<div *ngIf="showNotificationPanel" class="notification-panel-overlay">
  <div class="notification-panel">
    <!-- En-tête du panneau -->
    <div class="notification-header">
      <div class="notification-title">
        <i class="fas fa-bell"></i>
        <span>Notifications</span>
        <span *ngIf="unreadNotificationCount > 0" class="notification-badge">
          {{ unreadNotificationCount }}
        </span>
      </div>
      <div class="notification-actions">
        <button
          (click)="toggleNotificationSettings()"
          class="notification-btn"
          title="Paramètres"
        >
          <i class="fas fa-cog"></i>
        </button>
        <button
          (click)="loadNotifications(true)"
          class="notification-btn"
          [disabled]="isLoadingNotifications"
          title="Actualiser"
        >
          <i
            class="fas fa-sync-alt"
            [ngClass]="{ 'fa-spin': isLoadingNotifications }"
          ></i>
        </button>
        <button
          (click)="toggleNotificationPanel()"
          class="notification-btn close-btn"
          title="Fermer"
        >
          <i class="fas fa-times"></i>
        </button>
      </div>
    </div>

    <!-- Filtres et options -->
    <div class="notification-filters">
      <div class="filter-tabs">
        <button
          (click)="setNotificationFilter('all')"
          [ngClass]="{ active: notificationFilter === 'all' }"
          class="filter-tab"
        >
          Toutes
        </button>
        <button
          (click)="setNotificationFilter('unread')"
          [ngClass]="{ active: notificationFilter === 'unread' }"
          class="filter-tab"
        >
          Non lues
        </button>
        <button
          (click)="setNotificationFilter('read')"
          [ngClass]="{ active: notificationFilter === 'read' }"
          class="filter-tab"
        >
          Lues
        </button>
      </div>

      <!-- Actions de sélection -->
      <div
        class="selection-actions"
        *ngIf="getFilteredNotifications().length > 0"
      >
        <div class="selection-controls">
          <label class="select-all-checkbox">
            <input
              type="checkbox"
              [checked]="areAllNotificationsSelected()"
              (change)="toggleSelectAllNotifications()"
            />
            <span class="checkmark"></span>
            <span class="select-text">Tout sélectionner</span>
          </label>
        </div>

        <div class="bulk-actions" *ngIf="selectedNotifications.size > 0">
          <button
            (click)="markSelectedAsRead()"
            [disabled]="isMarkingAsRead"
            class="action-btn mark-read-btn"
          >
            <i class="fas fa-check"></i>
            Marquer comme lu
          </button>
          <button
            (click)="showDeleteSelectedConfirmation()"
            [disabled]="isDeletingNotifications"
            class="action-btn delete-btn"
          >
            <i class="fas fa-trash"></i>
            Supprimer
          </button>
        </div>
      </div>

      <!-- Action globale -->
      <div class="global-actions">
        <button
          *ngIf="unreadNotificationCount > 0"
          (click)="markAllAsRead()"
          [disabled]="isMarkingAsRead"
          class="action-btn mark-all-read-btn"
        >
          <i class="fas fa-check-double"></i>
          Tout marquer comme lu
        </button>
        <button
          *ngIf="notifications.length > 0"
          (click)="deleteAllNotifications()"
          [disabled]="isDeletingNotifications"
          class="action-btn delete-all-btn"
        >
          <i
            class="fas fa-trash-alt"
            [ngClass]="{ 'fa-spin': isDeletingNotifications }"
          ></i>
          Supprimer tout
        </button>
      </div>
    </div>

    <!-- Liste des notifications -->
    <div class="notification-list">
      <div
        *ngIf="isLoadingNotifications && notifications.length === 0"
        class="loading-state"
      >
        <i class="fas fa-spinner fa-spin"></i>
        <span>Chargement des notifications...</span>
      </div>

      <div
        *ngIf="
          !isLoadingNotifications && getFilteredNotifications().length === 0
        "
        class="empty-state"
      >
        <i class="fas fa-bell-slash"></i>
        <span>Aucune notification</span>
      </div>

      <div
        *ngFor="
          let notification of getFilteredNotifications();
          trackBy: trackByNotificationId
        "
        class="notification-item"
        [ngClass]="{
          unread: !notification.isRead,
          selected: selectedNotifications.has(notification.id)
        }"
      >
        <!-- Checkbox de sélection -->
        <div class="notification-checkbox">
          <input
            type="checkbox"
            [checked]="selectedNotifications.has(notification.id)"
            (change)="toggleNotificationSelection(notification.id)"
          />
          <span class="checkmark"></span>
        </div>

        <!-- Contenu de la notification -->
        <div class="notification-content">
          <div
            class="notification-icon"
            [ngClass]="getNotificationColor(notification.type)"
          >
            <i [class]="getNotificationIcon(notification.type)"></i>
          </div>

          <div class="notification-body">
            <div class="notification-text">
              <span class="notification-sender" *ngIf="notification.senderId">
                {{ notification.senderId.username }}
              </span>
              <span class="notification-message">{{
                notification.content
              }}</span>
            </div>

            <div class="notification-meta">
              <span class="notification-time">
                {{ formatNotificationDate(notification.timestamp) }}
              </span>
              <span *ngIf="notification.isRead" class="read-indicator">
                <i class="fas fa-check"></i>
              </span>
            </div>
          </div>

          <!-- Actions individuelles -->
          <div class="notification-item-actions">
            <button
              *ngIf="!notification.isRead"
              (click)="
                markSelectedAsRead();
                selectedNotifications.clear();
                selectedNotifications.add(notification.id)
              "
              class="item-action-btn"
              title="Marquer comme lu"
            >
              <i class="fas fa-check"></i>
            </button>
            <button
              (click)="deleteNotification(notification.id)"
              class="item-action-btn delete"
              title="Supprimer"
            >
              <i class="fas fa-trash"></i>
            </button>
          </div>
        </div>
      </div>

      <!-- Bouton charger plus -->
      <div *ngIf="hasMoreNotifications" class="load-more-container">
        <button
          (click)="loadMoreNotifications()"
          [disabled]="isLoadingNotifications"
          class="load-more-btn"
        >
          <i
            class="fas fa-chevron-down"
            [ngClass]="{ 'fa-spin': isLoadingNotifications }"
          ></i>
          Charger plus
        </button>
      </div>
    </div>

    <!-- Paramètres de notification -->
    <div *ngIf="showNotificationSettings" class="notification-settings">
      <div class="settings-header">
        <h4>Paramètres de notification</h4>
        <button
          (click)="toggleNotificationSettings()"
          class="close-settings-btn"
        >
          <i class="fas fa-times"></i>
        </button>
      </div>

      <div class="settings-content">
        <div class="setting-item">
          <label class="setting-label">
            <input
              type="checkbox"
              [(ngModel)]="notificationSounds"
              (change)="saveNotificationSettings()"
            />
            <span class="setting-checkmark"></span>
            <span class="setting-text">Sons de notification</span>
          </label>
        </div>

        <div class="setting-item">
          <label class="setting-label">
            <input
              type="checkbox"
              [(ngModel)]="notificationPreview"
              (change)="saveNotificationSettings()"
            />
            <span class="setting-checkmark"></span>
            <span class="setting-text">Aperçu des notifications</span>
          </label>
        </div>

        <div class="setting-item">
          <label class="setting-label">
            <input
              type="checkbox"
              [(ngModel)]="autoMarkAsRead"
              (change)="saveNotificationSettings()"
            />
            <span class="setting-checkmark"></span>
            <span class="setting-text">Marquer automatiquement comme lu</span>
          </label>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Panneau de statut utilisateur détaillé -->
<div *ngIf="showUserStatusPanel" class="user-status-panel-overlay">
  <div class="user-status-panel">
    <!-- En-tête du panneau -->
    <div class="status-panel-header">
      <div class="status-panel-title">
        <i class="fas fa-users"></i>
        <span>Statut des utilisateurs</span>
        <span class="online-count-badge">
          {{ getOnlineUsersCount() }} en ligne
        </span>
      </div>
      <div class="status-panel-actions">
        <button
          (click)="loadOnlineUsers()"
          class="status-panel-btn"
          title="Actualiser"
        >
          <i class="fas fa-sync-alt"></i>
        </button>
        <button
          (click)="toggleUserStatusPanel()"
          class="status-panel-btn close-btn"
          title="Fermer"
        >
          <i class="fas fa-times"></i>
        </button>
      </div>
    </div>

    <!-- Filtres de statut -->
    <div class="status-filters">
      <div class="filter-tabs">
        <button
          (click)="setStatusFilter('all')"
          [ngClass]="{ active: statusFilterType === 'all' }"
          class="status-filter-tab"
        >
          <i class="fas fa-globe"></i>
          Tous
        </button>
        <button
          (click)="setStatusFilter('online')"
          [ngClass]="{ active: statusFilterType === 'online' }"
          class="status-filter-tab"
        >
          <i class="fas fa-circle text-green-500"></i>
          En ligne
        </button>
        <button
          (click)="setStatusFilter('away')"
          [ngClass]="{ active: statusFilterType === 'away' }"
          class="status-filter-tab"
        >
          <i class="fas fa-clock text-yellow-500"></i>
          Absent
        </button>
        <button
          (click)="setStatusFilter('busy')"
          [ngClass]="{ active: statusFilterType === 'busy' }"
          class="status-filter-tab"
        >
          <i class="fas fa-minus-circle text-red-500"></i>
          Occupé
        </button>
        <button
          (click)="setStatusFilter('offline')"
          [ngClass]="{ active: statusFilterType === 'offline' }"
          class="status-filter-tab"
        >
          <i class="far fa-circle text-gray-500"></i>
          Hors ligne
        </button>
      </div>
    </div>

    <!-- Liste des utilisateurs -->
    <div class="status-user-list">
      <div *ngIf="getFilteredUsers().length === 0" class="empty-status-state">
        <i class="fas fa-user-slash"></i>
        <span>Aucun utilisateur trouvé</span>
      </div>

      <div
        *ngFor="let user of getFilteredUsers(); trackBy: trackByUserId"
        class="status-user-item"
      >
        <!-- Avatar avec indicateur de statut -->
        <div class="status-user-avatar">
          <img
            [src]="user.image || 'assets/images/default-avatar.png'"
            [alt]="user.username"
            class="user-avatar-img"
            onerror="this.src='assets/images/default-avatar.png'"
          />
          <div
            class="status-indicator"
            [ngClass]="getStatusColor(user.isOnline ? 'online' : 'offline')"
          >
            <i
              [class]="getStatusIcon(user.isOnline ? 'online' : 'offline')"
            ></i>
          </div>
        </div>

        <!-- Informations utilisateur -->
        <div class="status-user-info">
          <div class="status-user-name">{{ user.username }}</div>
          <div class="status-user-details">
            <span
              class="status-text"
              [ngClass]="getStatusColor(user.isOnline ? 'online' : 'offline')"
            >
              {{ getStatusText(user.isOnline ? "online" : "offline") }}
            </span>
            <span class="status-separator">•</span>
            <span class="last-seen">
              {{
                user.isOnline
                  ? "Maintenant"
                  : formatLastSeen(
                      user.id
                        ? userStatusMap.get(user.id)?.lastActive || null
                        : null
                    )
              }}
            </span>
          </div>
        </div>

        <!-- Actions utilisateur -->
        <div class="status-user-actions">
          <button
            class="status-action-btn message-btn"
            title="Envoyer un message"
          >
            <i class="fas fa-comment"></i>
          </button>
          <button
            *ngIf="user.isOnline"
            class="status-action-btn call-btn"
            title="Appeler"
          >
            <i class="fas fa-phone"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- Historique de statut (optionnel) -->
    <div *ngIf="showDetailedStatus" class="status-history">
      <div class="status-history-header">
        <h4>Historique récent</h4>
        <button (click)="showDetailedStatus = false" class="close-history-btn">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <div class="status-history-list">
        <div
          *ngFor="let entry of statusHistory.slice(0, 20)"
          class="status-history-item"
        >
          <div class="history-time">
            {{ formatLastSeen(entry.timestamp) }}
          </div>
          <div class="history-content">
            <span class="history-user">{{ entry.userId }}</span>
            <span
              class="history-action"
              [ngClass]="getStatusColor(entry.status)"
            >
              {{ getStatusText(entry.status) }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Panneau d'historique des appels -->
<div
  *ngIf="showCallHistoryPanel"
  class="user-status-panel-overlay"
  (click)="showCallHistoryPanel = false"
>
  <div class="user-status-panel" (click)="$event.stopPropagation()">
    <!-- En-tête du panneau -->
    <div class="status-panel-header">
      <div class="status-panel-title">
        <i class="fas fa-history"></i>
        <span>Historique des appels</span>
      </div>
      <div class="status-panel-actions">
        <button
          (click)="loadCallHistory(true)"
          class="status-panel-btn"
          title="Actualiser"
          [disabled]="isLoadingCallHistory"
        >
          <i
            class="fas fa-sync-alt"
            [ngClass]="{ 'fa-spin': isLoadingCallHistory }"
          ></i>
        </button>
        <button
          (click)="showCallHistoryPanel = false"
          class="status-panel-btn close-btn"
          title="Fermer"
        >
          <i class="fas fa-times"></i>
        </button>
      </div>
    </div>

    <!-- Filtres d'historique -->
    <div class="status-filters">
      <div class="filter-tabs">
        <button
          (click)="setCallHistoryFilters({ status: [] })"
          class="status-filter-tab"
          [ngClass]="{ active: callHistoryFilters.status.length === 0 }"
        >
          <i class="fas fa-list"></i>
          Tous
        </button>
        <button
          (click)="setCallHistoryFilters({ status: ['COMPLETED'] })"
          class="status-filter-tab"
          [ngClass]="{
            active: callHistoryFilters.status.includes('COMPLETED')
          }"
        >
          <i class="fas fa-check-circle text-green-500"></i>
          Terminés
        </button>
        <button
          (click)="setCallHistoryFilters({ status: ['MISSED'] })"
          class="status-filter-tab"
          [ngClass]="{ active: callHistoryFilters.status.includes('MISSED') }"
        >
          <i class="fas fa-phone-slash text-red-500"></i>
          Manqués
        </button>
        <button
          (click)="setCallHistoryFilters({ status: ['REJECTED'] })"
          class="status-filter-tab"
          [ngClass]="{ active: callHistoryFilters.status.includes('REJECTED') }"
        >
          <i class="fas fa-times-circle text-orange-500"></i>
          Rejetés
        </button>
      </div>
    </div>

    <!-- Liste des appels -->
    <div class="status-user-list">
      <!-- État de chargement -->
      <div
        *ngIf="isLoadingCallHistory && callHistory.length === 0"
        class="empty-status-state"
      >
        <i class="fas fa-spinner fa-spin"></i>
        <span>Chargement de l'historique...</span>
      </div>

      <!-- État vide -->
      <div
        *ngIf="!isLoadingCallHistory && callHistory.length === 0"
        class="empty-status-state"
      >
        <i class="fas fa-phone-slash"></i>
        <span>Aucun appel trouvé</span>
      </div>

      <!-- Éléments d'historique -->
      <div
        *ngFor="let call of callHistory; trackBy: trackByCallId"
        class="status-user-item"
      >
        <!-- Avatar de l'utilisateur -->
        <div class="status-user-avatar">
          <img
            [src]="
              (call.caller?.id === currentUserId
                ? call.recipient?.image
                : call.caller?.image) || 'assets/images/default-avatar.png'
            "
            [alt]="
              call.caller?.id === currentUserId
                ? call.recipient?.username
                : call.caller?.username
            "
            class="user-avatar-img"
            onerror="this.src='assets/images/default-avatar.png'"
          />
          <!-- Indicateur de type d'appel -->
          <div class="status-indicator">
            <i [class]="getCallTypeIcon(call.type)" class="text-xs"></i>
          </div>
        </div>

        <!-- Informations de l'appel -->
        <div class="status-user-info">
          <div class="status-user-name">
            {{
              call.caller?.id === currentUserId
                ? call.recipient?.username
                : call.caller?.username
            }}
          </div>
          <div class="status-user-details">
            <span [class]="getCallStatusColor(call.status)" class="status-text">
              {{
                call.status === "COMPLETED"
                  ? "Terminé"
                  : call.status === "MISSED"
                  ? "Manqué"
                  : call.status === "REJECTED"
                  ? "Rejeté"
                  : call.status === "CANCELLED"
                  ? "Annulé"
                  : call.status
              }}
            </span>
            <span class="status-separator">•</span>
            <span class="last-seen">{{ formatCallDate(call.startTime) }}</span>
            <span *ngIf="call.duration" class="status-separator">•</span>
            <span *ngIf="call.duration" class="last-seen">{{
              formatCallDuration(call.duration)
            }}</span>
          </div>
        </div>

        <!-- Actions -->
        <div class="status-user-actions">
          <button class="status-action-btn message-btn" title="Rappeler">
            <i class="fas fa-phone"></i>
          </button>
          <button class="status-action-btn" title="Détails">
            <i class="fas fa-info-circle"></i>
          </button>
        </div>
      </div>

      <!-- Bouton charger plus -->
      <div *ngIf="hasMoreCallHistory" class="text-center p-4">
        <button
          (click)="loadMoreCallHistory()"
          [disabled]="isLoadingCallHistory"
          class="status-filter-tab"
        >
          <i
            class="fas fa-chevron-down"
            [ngClass]="{ 'fa-spin': isLoadingCallHistory }"
          ></i>
          Charger plus
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Panneau de statistiques d'appels -->
<div
  *ngIf="showCallStatsPanel"
  class="user-status-panel-overlay"
  (click)="showCallStatsPanel = false"
>
  <div class="user-status-panel" (click)="$event.stopPropagation()">
    <!-- En-tête du panneau -->
    <div class="status-panel-header">
      <div class="status-panel-title">
        <i class="fas fa-chart-bar"></i>
        <span>Statistiques d'appels</span>
      </div>
      <div class="status-panel-actions">
        <button
          (click)="loadCallStats()"
          class="status-panel-btn"
          title="Actualiser"
          [disabled]="isLoadingCallStats"
        >
          <i
            class="fas fa-sync-alt"
            [ngClass]="{ 'fa-spin': isLoadingCallStats }"
          ></i>
        </button>
        <button
          (click)="showCallStatsPanel = false"
          class="status-panel-btn close-btn"
          title="Fermer"
        >
          <i class="fas fa-times"></i>
        </button>
      </div>
    </div>

    <!-- Contenu des statistiques -->
    <div class="status-user-list">
      <!-- État de chargement -->
      <div *ngIf="isLoadingCallStats" class="empty-status-state">
        <i class="fas fa-spinner fa-spin"></i>
        <span>Chargement des statistiques...</span>
      </div>

      <!-- Statistiques -->
      <div *ngIf="!isLoadingCallStats && callStats" class="p-4 space-y-4">
        <!-- Statistiques générales -->
        <div class="grid grid-cols-2 gap-4">
          <div
            class="bg-gradient-to-r from-[#4f5fad]/10 to-[#6d78c9]/10 p-4 rounded-lg"
          >
            <div class="text-2xl font-bold text-[#4f5fad] dark:text-[#6d78c9]">
              {{ callStats.totalCalls || 0 }}
            </div>
            <div class="text-sm text-[#6d6870] dark:text-[#a0a0a0]">
              Total des appels
            </div>
          </div>
          <div
            class="bg-gradient-to-r from-green-500/10 to-green-600/10 p-4 rounded-lg"
          >
            <div class="text-2xl font-bold text-green-500">
              {{ formatCallDuration(callStats.totalDuration || 0) }}
            </div>
            <div class="text-sm text-[#6d6870] dark:text-[#a0a0a0]">
              Durée totale
            </div>
          </div>
          <div
            class="bg-gradient-to-r from-red-500/10 to-red-600/10 p-4 rounded-lg"
          >
            <div class="text-2xl font-bold text-red-500">
              {{ callStats.missedCalls || 0 }}
            </div>
            <div class="text-sm text-[#6d6870] dark:text-[#a0a0a0]">
              Appels manqués
            </div>
          </div>
          <div
            class="bg-gradient-to-r from-blue-500/10 to-blue-600/10 p-4 rounded-lg"
          >
            <div class="text-2xl font-bold text-blue-500">
              {{ formatCallDuration(callStats.averageCallDuration || 0) }}
            </div>
            <div class="text-sm text-[#6d6870] dark:text-[#a0a0a0]">
              Durée moyenne
            </div>
          </div>
        </div>

        <!-- Répartition par type -->
        <div *ngIf="callStats.callsByType" class="space-y-2">
          <h4 class="text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0]">
            Répartition par type
          </h4>
          <div
            *ngFor="let typeStats of callStats.callsByType"
            class="flex items-center justify-between p-2 bg-[#edf1f4]/30 dark:bg-[#2a2a2a]/30 rounded"
          >
            <div class="flex items-center space-x-2">
              <i [class]="getCallTypeIcon(typeStats.type)"></i>
              <span class="text-sm">{{
                typeStats.type === "VIDEO" ? "Vidéo" : "Audio"
              }}</span>
            </div>
            <span class="text-sm font-medium">{{ typeStats.count }}</span>
          </div>
        </div>

        <!-- Utilisateur le plus appelé -->
        <div *ngIf="callStats.mostCalledUser" class="space-y-2">
          <h4 class="text-sm font-medium text-[#6d6870] dark:text-[#a0a0a0]">
            Contact le plus appelé
          </h4>
          <div
            class="flex items-center space-x-3 p-3 bg-[#edf1f4]/30 dark:bg-[#2a2a2a]/30 rounded"
          >
            <img
              [src]="
                callStats.mostCalledUser.image ||
                'assets/images/default-avatar.png'
              "
              [alt]="callStats.mostCalledUser.username"
              class="w-10 h-10 rounded-full object-cover"
              onerror="this.src='assets/images/default-avatar.png'"
            />
            <div>
              <div class="font-medium text-[#6d6870] dark:text-[#a0a0a0]">
                {{ callStats.mostCalledUser.username }}
              </div>
              <div class="text-xs text-[#6d6870]/70 dark:text-[#a0a0a0]/70">
                Contact favori
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- État vide -->
      <div *ngIf="!isLoadingCallStats && !callStats" class="empty-status-state">
        <i class="fas fa-chart-bar"></i>
        <span>Aucune statistique disponible</span>
      </div>
    </div>
  </div>
</div>

<!-- Panneau des messages vocaux -->
<div
  *ngIf="showVoiceMessagesPanel"
  class="user-status-panel-overlay"
  (click)="showVoiceMessagesPanel = false"
>
  <div class="user-status-panel" (click)="$event.stopPropagation()">
    <!-- En-tête du panneau -->
    <div class="status-panel-header">
      <div class="status-panel-title">
        <i class="fas fa-microphone"></i>
        <span>Messages vocaux</span>
        <span *ngIf="voiceMessages.length > 0" class="online-count-badge">
          {{ voiceMessages.length }}
        </span>
      </div>
      <div class="status-panel-actions">
        <button
          (click)="loadVoiceMessages()"
          class="status-panel-btn"
          title="Actualiser"
          [disabled]="isLoadingVoiceMessages"
        >
          <i
            class="fas fa-sync-alt"
            [ngClass]="{ 'fa-spin': isLoadingVoiceMessages }"
          ></i>
        </button>
        <button
          (click)="showVoiceMessagesPanel = false"
          class="status-panel-btn close-btn"
          title="Fermer"
        >
          <i class="fas fa-times"></i>
        </button>
      </div>
    </div>

    <!-- Liste des messages vocaux -->
    <div class="status-user-list">
      <!-- État de chargement -->
      <div *ngIf="isLoadingVoiceMessages" class="empty-status-state">
        <i class="fas fa-spinner fa-spin"></i>
        <span>Chargement des messages vocaux...</span>
      </div>

      <!-- État vide -->
      <div
        *ngIf="!isLoadingVoiceMessages && voiceMessages.length === 0"
        class="empty-status-state"
      >
        <i class="fas fa-microphone-slash"></i>
        <span>Aucun message vocal</span>
      </div>

      <!-- Messages vocaux -->
      <div
        *ngFor="let voiceMessage of voiceMessages; trackBy: trackByCallId"
        class="status-user-item"
      >
        <!-- Avatar de l'utilisateur -->
        <div class="status-user-avatar">
          <img
            [src]="
              (voiceMessage.caller?.id === currentUserId
                ? voiceMessage.recipient?.image
                : voiceMessage.caller?.image) ||
              'assets/images/default-avatar.png'
            "
            [alt]="
              voiceMessage.caller?.id === currentUserId
                ? voiceMessage.recipient?.username
                : voiceMessage.caller?.username
            "
            class="user-avatar-img"
            onerror="this.src='assets/images/default-avatar.png'"
          />
          <!-- Indicateur de message vocal -->
          <div class="status-indicator">
            <i class="fas fa-microphone text-xs"></i>
          </div>
        </div>

        <!-- Informations du message vocal -->
        <div class="status-user-info">
          <div class="status-user-name">
            {{
              voiceMessage.caller?.id === currentUserId
                ? voiceMessage.recipient?.username
                : voiceMessage.caller?.username
            }}
          </div>
          <div class="status-user-details">
            <span class="status-text">Message vocal</span>
            <span class="status-separator">•</span>
            <span class="last-seen">{{
              formatCallDate(voiceMessage.startTime)
            }}</span>
            <span *ngIf="voiceMessage.duration" class="status-separator"
              >•</span
            >
            <span *ngIf="voiceMessage.duration" class="last-seen">{{
              formatCallDuration(voiceMessage.duration)
            }}</span>
          </div>
        </div>

        <!-- Actions -->
        <div class="status-user-actions">
          <button class="status-action-btn message-btn" title="Écouter">
            <i class="fas fa-play"></i>
          </button>
          <button class="status-action-btn" title="Télécharger">
            <i class="fas fa-download"></i>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Modal de confirmation de suppression -->
<div *ngIf="showDeleteConfirmModal" class="delete-confirm-modal">
  <div class="delete-confirm-content">
    <div class="delete-confirm-header">
      <i class="fas fa-exclamation-triangle"></i>
      <h3>Confirmer la suppression</h3>
    </div>
    <div class="delete-confirm-body">
      <p>
        Êtes-vous sûr de vouloir supprimer
        {{ selectedNotifications.size }} notification(s) ?
      </p>
      <p class="warning-text">Cette action est irréversible.</p>
    </div>
    <div class="delete-confirm-actions">
      <button (click)="cancelDeleteNotifications()" class="cancel-btn">
        Annuler
      </button>
      <button
        (click)="deleteSelectedNotifications()"
        [disabled]="isDeletingNotifications"
        class="confirm-delete-btn"
      >
        <i
          class="fas fa-trash"
          [ngClass]="{ 'fa-spin': isDeletingNotifications }"
        ></i>
        Supprimer
      </button>
    </div>
  </div>
</div>
