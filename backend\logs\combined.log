{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-26 01:59:48"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-26 01:59:48"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-26 02:03:05"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-26 02:03:41"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-26 02:03:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:05:55","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 9ms","timestamp":"2025-05-26 02:05:55"}
{"level":"http","message":"POST / 200 - 33ms","timestamp":"2025-05-26 02:05:55"}
{"level":"info","message":"WebSocket connection authenticated for user 68294132b5d5c86fd2e56e23","timestamp":"2025-05-26 02:05:55"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 29ms","timestamp":"2025-05-26 02:05:56"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-26 02:05:56","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 6ms","timestamp":"2025-05-26 02:05:56"}
{"level":"http","message":"GraphQL anonymous completed in 70ms","timestamp":"2025-05-26 02:05:56"}
{"level":"http","message":"POST / 200 - 79ms","timestamp":"2025-05-26 02:05:56"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:05:56"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:05:56"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:05:56"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:05:56"}
{"level":"http","message":"GET /users 200 - 42ms","timestamp":"2025-05-26 02:06:02"}
{"level":"http","message":"GET / 304 - 28ms","timestamp":"2025-05-26 02:06:04"}
{"level":"http","message":"GET / 304 - 27ms","timestamp":"2025-05-26 02:06:04"}
{"level":"http","message":"GET / 304 - 11ms","timestamp":"2025-05-26 02:06:04"}
{"level":"http","message":"GET / 304 - 9ms","timestamp":"2025-05-26 02:06:17"}
{"level":"http","message":"GET /users 304 - 10ms","timestamp":"2025-05-26 02:06:21"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:06:25","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:06:25"}
{"level":"http","message":"POST / 200 - 10ms","timestamp":"2025-05-26 02:06:25"}
{"level":"http","message":"GET / 304 - 40ms","timestamp":"2025-05-26 02:06:28"}
{"level":"http","message":"GET / 304 - 55ms","timestamp":"2025-05-26 02:06:28"}
{"level":"http","message":"GET /getev 304 - 11ms","timestamp":"2025-05-26 02:06:28"}
{"level":"http","message":"GET / 304 - 13ms","timestamp":"2025-05-26 02:06:30"}
{"level":"http","message":"GET / 304 - 21ms","timestamp":"2025-05-26 02:06:30"}
{"level":"http","message":"GET /user/68294132b5d5c86fd2e56e23 200 - 24ms","timestamp":"2025-05-26 02:06:33"}
{"level":"warn","message":"GET /user/68294132b5d5c86fd2e56e23 401 - 2ms","timestamp":"2025-05-26 02:06:35"}
{"level":"http","message":"GET /profile 200 - 17ms","timestamp":"2025-05-26 02:06:37"}
{"level":"http","message":"GET /users 304 - 28ms","timestamp":"2025-05-26 02:06:37"}
{"level":"http","message":"GET /users 304 - 12ms","timestamp":"2025-05-26 02:06:38"}
{"level":"http","message":"GET /profile 304 - 15ms","timestamp":"2025-05-26 02:06:39"}
{"level":"http","message":"GET /users 304 - 19ms","timestamp":"2025-05-26 02:06:39"}
{"level":"warn","message":"GET /user/68294132b5d5c86fd2e56e23 401 - 1ms","timestamp":"2025-05-26 02:06:40"}
{"level":"http","message":"GET /user/68294132b5d5c86fd2e56e23 304 - 29ms","timestamp":"2025-05-26 02:06:48"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-26 02:06:49","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 11ms","timestamp":"2025-05-26 02:06:49"}
{"level":"http","message":"GraphQL anonymous completed in 35ms","timestamp":"2025-05-26 02:06:49"}
{"level":"http","message":"POST / 200 - 41ms","timestamp":"2025-05-26 02:06:49"}
{"level":"http","message":"GET / 304 - 11ms","timestamp":"2025-05-26 02:06:55"}
{"level":"http","message":"GET / 304 - 15ms","timestamp":"2025-05-26 02:06:55"}
{"level":"http","message":"GET / 304 - 9ms","timestamp":"2025-05-26 02:06:55"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:06:55","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:06:55"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-26 02:06:55"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:06:56"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:06:56"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:06:56"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:06:56"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:06:56"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversations {\n  getConversations {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    lastMessage {\n      id\n      content\n      timestamp\n      isRead\n      sender {\n        id\n        username\n      }\n    }\n    unreadCount\n    updatedAt\n  }\n}","timestamp":"2025-05-26 02:06:56","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 63ms","timestamp":"2025-05-26 02:06:56"}
{"level":"http","message":"POST / 200 - 67ms","timestamp":"2025-05-26 02:06:56"}
{"level":"warn","message":"GET /user/68294132b5d5c86fd2e56e23 401 - 1ms","timestamp":"2025-05-26 02:06:57"}
{"level":"http","message":"GET /user/68294132b5d5c86fd2e56e23 304 - 29ms","timestamp":"2025-05-26 02:06:59"}
{"level":"http","message":"GET / 304 - 13ms","timestamp":"2025-05-26 02:07:01"}
{"level":"http","message":"GET /check/683325d1a097691878924d47/68294132b5d5c86fd2e56e23 304 - 8ms","timestamp":"2025-05-26 02:07:01"}
{"level":"http","message":"GET /check/68331b1748d1476bf3a21a9e/68294132b5d5c86fd2e56e23 304 - 5ms","timestamp":"2025-05-26 02:07:01"}
{"level":"http","message":"GET /check/68332594a097691878924d43/68294132b5d5c86fd2e56e23 304 - 4ms","timestamp":"2025-05-26 02:07:01"}
{"level":"http","message":"GET /user/68294132b5d5c86fd2e56e23 304 - 25ms","timestamp":"2025-05-26 02:07:03"}
{"level":"warn","message":"GET /user/68294132b5d5c86fd2e56e23 401 - 1ms","timestamp":"2025-05-26 02:07:04"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-26 02:07:09"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:07:10","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:07:10"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-26 02:07:10"}
{"level":"info","message":"WebSocket connection authenticated for user 68294132b5d5c86fd2e56e23","timestamp":"2025-05-26 02:07:10"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:07:10"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:07:10"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:07:10"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:07:10"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-26 02:07:10","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 15ms","timestamp":"2025-05-26 02:07:10"}
{"level":"warn","message":"GET /user/68294132b5d5c86fd2e56e23 401 - 1ms","timestamp":"2025-05-26 02:07:10"}
{"level":"http","message":"GraphQL anonymous completed in 44ms","timestamp":"2025-05-26 02:07:10"}
{"level":"http","message":"POST / 200 - 48ms","timestamp":"2025-05-26 02:07:10"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 35ms","timestamp":"2025-05-26 02:07:10"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:07:40","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:07:40"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-26 02:07:40"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:08:11","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:08:11"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-26 02:08:11"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-26 02:08:33"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:08:34","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:08:34"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-26 02:08:34"}
{"level":"info","message":"WebSocket connection authenticated for user 68294132b5d5c86fd2e56e23","timestamp":"2025-05-26 02:08:34"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:08:34"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:08:34"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:08:34"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:08:34"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 6ms","timestamp":"2025-05-26 02:08:34"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 3ms","timestamp":"2025-05-26 02:08:34"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-26 02:08:34","variables":{"limit":10,"page":1}}
{"level":"http","message":"GraphQL anonymous completed in 14ms","timestamp":"2025-05-26 02:08:34"}
{"level":"http","message":"POST / 200 - 19ms","timestamp":"2025-05-26 02:08:34"}
{"level":"warn","message":"GET /user/68294132b5d5c86fd2e56e23 401 - 1ms","timestamp":"2025-05-26 02:08:34"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:09:04","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:09:04"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-26 02:09:04"}
{"level":"info","message":"✅ Apollo Server ready","timestamp":"2025-05-26 02:14:28"}
{"level":"info","message":"\n      🚀 Server running at http://localhost:3000\n      🔐 REST API: http://localhost:3000/api/user\n      🔮 GraphQL: http://localhost:3000/graphql\n      🔌 Subscriptions endpoint: ws://localhost:3000/graphql\n    ","timestamp":"2025-05-26 02:14:28"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:14:45","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 14ms","timestamp":"2025-05-26 02:14:45"}
{"level":"http","message":"POST / 200 - 49ms","timestamp":"2025-05-26 02:14:45"}
{"level":"info","message":"WebSocket connection authenticated for user 68294132b5d5c86fd2e56e23","timestamp":"2025-05-26 02:14:45"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-26 02:14:45","variables":{"limit":10,"page":1}}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:14:45"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:14:45"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:14:45"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:14:45"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 89ms","timestamp":"2025-05-26 02:14:45"}
{"level":"http","message":"GraphQL anonymous completed in 213ms","timestamp":"2025-05-26 02:14:45"}
{"level":"http","message":"POST / 200 - 227ms","timestamp":"2025-05-26 02:14:45"}
{"level":"http","message":"GET /user/68294132b5d5c86fd2e56e23 304 - 212ms","timestamp":"2025-05-26 02:14:45"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 25ms","timestamp":"2025-05-26 02:14:45"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:14:54"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:14:54"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:14:54"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:14:54"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:14:54"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversations {\n  getConversations {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    lastMessage {\n      id\n      content\n      timestamp\n      isRead\n      sender {\n        id\n        username\n      }\n    }\n    unreadCount\n    updatedAt\n  }\n}","timestamp":"2025-05-26 02:14:54","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 35ms","timestamp":"2025-05-26 02:14:54"}
{"level":"http","message":"POST / 200 - 57ms","timestamp":"2025-05-26 02:14:54"}
{"level":"http","message":"GET / 304 - 33ms","timestamp":"2025-05-26 02:14:55"}
{"level":"http","message":"GET / 304 - 47ms","timestamp":"2025-05-26 02:14:55"}
{"level":"http","message":"GET / 304 - 107ms","timestamp":"2025-05-26 02:14:55"}
{"level":"http","message":"GET /user/68294132b5d5c86fd2e56e23 304 - 26ms","timestamp":"2025-05-26 02:14:57"}
{"level":"http","message":"GET /user/68294132b5d5c86fd2e56e23 304 - 19ms","timestamp":"2025-05-26 02:14:58"}
{"level":"http","message":"GET /getone/6820eccb3df1e540f8404a40 200 - 23ms","timestamp":"2025-05-26 02:15:00"}
{"level":"http","message":"GET /user/68294132b5d5c86fd2e56e23 304 - 28ms","timestamp":"2025-05-26 02:15:06"}
{"level":"http","message":"GET /getall?secret=2cinfo1&client=esprit 200 - 20ms","timestamp":"2025-05-26 02:15:08"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:15:15","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:15:15"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-26 02:15:15"}
{"level":"info","message":"WebSocket connection authenticated for user 68294132b5d5c86fd2e56e23","timestamp":"2025-05-26 02:15:42"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:15:42"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:15:42"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:15:42"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:15:42"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:15:45","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:15:45"}
{"level":"http","message":"POST / 200 - 13ms","timestamp":"2025-05-26 02:15:45"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:16:15","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 2ms","timestamp":"2025-05-26 02:16:15"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-26 02:16:15"}
{"level":"warn","message":"POST /add 400 - 7ms","timestamp":"2025-05-26 02:16:22"}
{"level":"http","message":"POST /add 201 - 77ms","timestamp":"2025-05-26 02:16:34"}
{"level":"http","message":"GET /user/68294132b5d5c86fd2e56e23 200 - 125ms","timestamp":"2025-05-26 02:16:34"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:16:46","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 2ms","timestamp":"2025-05-26 02:16:46"}
{"level":"http","message":"POST / 200 - 9ms","timestamp":"2025-05-26 02:16:46"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:17:15","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 3ms","timestamp":"2025-05-26 02:17:15"}
{"level":"http","message":"POST / 200 - 12ms","timestamp":"2025-05-26 02:17:15"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:17:45","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 2ms","timestamp":"2025-05-26 02:17:45"}
{"level":"http","message":"POST / 200 - 9ms","timestamp":"2025-05-26 02:17:45"}
{"level":"http","message":"POST /add 201 - 26ms","timestamp":"2025-05-26 02:18:01"}
{"level":"http","message":"GET /user/68294132b5d5c86fd2e56e23 200 - 21ms","timestamp":"2025-05-26 02:18:01"}
{"level":"http","message":"GET /user/68294132b5d5c86fd2e56e23 304 - 16ms","timestamp":"2025-05-26 02:18:04"}
{"level":"http","message":"GET /getall 200 - 38ms","timestamp":"2025-05-26 02:18:08"}
{"level":"http","message":"GET /getone/6820ed053df1e540f8404a47 200 - 36ms","timestamp":"2025-05-26 02:18:08"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:18:15","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:18:15"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-26 02:18:15"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:18:45","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 2ms","timestamp":"2025-05-26 02:18:45"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-26 02:18:45"}
{"level":"warn","message":"PUT /update/6820ed053df1e540f8404a47 403 - 9ms","timestamp":"2025-05-26 02:18:52"}
{"level":"http","message":"GET /user/68294132b5d5c86fd2e56e23 304 - 16ms","timestamp":"2025-05-26 02:19:13"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:19:16","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:19:16"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-26 02:19:16"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversations {\n  getConversations {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    lastMessage {\n      id\n      content\n      timestamp\n      isRead\n      sender {\n        id\n        username\n      }\n    }\n    unreadCount\n    updatedAt\n  }\n}","timestamp":"2025-05-26 02:19:31","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 34ms","timestamp":"2025-05-26 02:19:31"}
{"level":"http","message":"POST / 200 - 40ms","timestamp":"2025-05-26 02:19:31"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetVoiceMessages {\n  getVoiceMessages {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    endTime\n    duration\n    conversationId\n    metadata\n  }\n}","timestamp":"2025-05-26 02:19:33","variables":{}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-26 02:19:33","variables":{"conversationId":"6832451afe5136e30fe9dfaf","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6832451afe5136e30fe9dfaf, userId=68294132b5d5c86fd2e56e23","timestamp":"2025-05-26 02:19:33"}
{"level":"http","message":"GraphQL anonymous completed in 77ms","timestamp":"2025-05-26 02:19:33"}
{"level":"http","message":"POST / 200 - 84ms","timestamp":"2025-05-26 02:19:33"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6832451afe5136e30fe9dfaf, unread: 0, messages: 4","timestamp":"2025-05-26 02:19:33"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6832451afe5136e30fe9dfaf, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=68294132b5d5c86fd2e56e23, offset=0","timestamp":"2025-05-26 02:19:33"}
{"level":"info","message":"[MessageService] Retrieved 4 messages","timestamp":"2025-05-26 02:19:33"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-26 02:19:33"}
{"level":"http","message":"GraphQL anonymous completed in 87ms","timestamp":"2025-05-26 02:19:33"}
{"level":"http","message":"POST / 200 - 96ms","timestamp":"2025-05-26 02:19:33"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:19:33"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:19:33"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:19:33"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:19:45","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:19:45"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-26 02:19:45"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation SendMessage($receiverId: ID!, $content: String, $file: Upload, $type: MessageType, $metadata: JSON) {\n  sendMessage(\n    receiverId: $receiverId\n    content: $content\n    file: $file\n    type: $type\n    metadata: $metadata\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    conversation {\n      id\n    }\n    attachments {\n      url\n      type\n      duration\n    }\n    metadata\n  }\n}","timestamp":"2025-05-26 02:19:46","variables":{"content":"hello from 2cinfo 1","receiverId":"68294542b5d5c86fd2e56e4b","type":"TEXT"}}
{"level":"info","message":"[MessageService] Starting sendMessage flow: senderId=68294132b5d5c86fd2e56e23, receiverId=68294542b5d5c86fd2e56e4b, type=TEXT, hasMetadata=false","timestamp":"2025-05-26 02:19:46"}
{"level":"info","message":"[MessageService] Message saved successfully: 6833c1b269f38dababbaf303","timestamp":"2025-05-26 02:19:46"}
{"level":"info","message":"[MessageService] Message flow completed successfully: messageId=6833c1b269f38dababbaf303","timestamp":"2025-05-26 02:19:46"}
{"level":"http","message":"GraphQL anonymous completed in 122ms","timestamp":"2025-05-26 02:19:46"}
{"level":"http","message":"POST / 200 - 128ms","timestamp":"2025-05-26 02:19:46"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-26 02:20:05"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:20:15","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:20:15"}
{"level":"http","message":"POST / 200 - 8ms","timestamp":"2025-05-26 02:20:15"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:20:46","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:20:46"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-26 02:20:46"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:20:57","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:20:57"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-26 02:20:57"}
{"level":"info","message":"WebSocket connection authenticated for user 682bb95fb9b407dd58126686","timestamp":"2025-05-26 02:20:57"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:20:57"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:20:57"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:20:57"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:20:58"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-26 02:20:58","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 200 - 25ms","timestamp":"2025-05-26 02:20:58"}
{"level":"http","message":"GraphQL anonymous completed in 49ms","timestamp":"2025-05-26 02:20:58"}
{"level":"http","message":"POST / 200 - 57ms","timestamp":"2025-05-26 02:20:58"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 38ms","timestamp":"2025-05-26 02:20:58"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-26 02:21:06"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:21:07","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:21:07"}
{"level":"http","message":"POST / 200 - 14ms","timestamp":"2025-05-26 02:21:07"}
{"level":"info","message":"WebSocket connection authenticated for user 682bb95fb9b407dd58126686","timestamp":"2025-05-26 02:21:07"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:21:07"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:21:07"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:21:07"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:21:07"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-26 02:21:07","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 15ms","timestamp":"2025-05-26 02:21:07"}
{"level":"http","message":"GraphQL anonymous completed in 44ms","timestamp":"2025-05-26 02:21:07"}
{"level":"http","message":"POST / 200 - 49ms","timestamp":"2025-05-26 02:21:07"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 36ms","timestamp":"2025-05-26 02:21:07"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation MarkNotificationsAsRead($notificationIds: [ID!]!) {\n  markNotificationsAsRead(notificationIds: $notificationIds) {\n    success\n    readCount\n    remainingCount\n  }\n}","timestamp":"2025-05-26 02:21:09","variables":{"notificationIds":["682e8297b57e670e214ac608"]}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversations {\n  getConversations {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    lastMessage {\n      id\n      content\n      timestamp\n      isRead\n      sender {\n        id\n        username\n      }\n    }\n    unreadCount\n    updatedAt\n  }\n}","timestamp":"2025-05-26 02:21:09","variables":{}}
{"level":"warn","message":"GraphQL anonymous completed with errors in 20ms","timestamp":"2025-05-26 02:21:09"}
{"level":"http","message":"POST / 200 - 23ms","timestamp":"2025-05-26 02:21:09"}
{"level":"http","message":"GraphQL anonymous completed in 27ms","timestamp":"2025-05-26 02:21:09"}
{"level":"http","message":"POST / 200 - 33ms","timestamp":"2025-05-26 02:21:09"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation MarkNotificationsAsRead($notificationIds: [ID!]!) {\n  markNotificationsAsRead(notificationIds: $notificationIds) {\n    success\n    readCount\n    remainingCount\n  }\n}","timestamp":"2025-05-26 02:21:09","variables":{"notificationIds":["682e8297b57e670e214ac608"]}}
{"level":"warn","message":"GraphQL anonymous completed with errors in 4ms","timestamp":"2025-05-26 02:21:09"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-26 02:21:09"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation CreateConversation($userId: ID!) {\n  createConversation(userId: $userId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    lastMessage {\n      id\n      content\n      timestamp\n    }\n    unreadCount\n    updatedAt\n  }\n}","timestamp":"2025-05-26 02:21:09","variables":{"userId":"682bb95fb9b407dd58126686"}}
{"level":"warn","message":"GraphQL anonymous completed with errors in 4ms","timestamp":"2025-05-26 02:21:09"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-26 02:21:09"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation CreateConversation($userId: ID!) {\n  createConversation(userId: $userId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    lastMessage {\n      id\n      content\n      timestamp\n    }\n    unreadCount\n    updatedAt\n  }\n}","timestamp":"2025-05-26 02:21:09","variables":{"userId":"682bb95fb9b407dd58126686"}}
{"level":"warn","message":"GraphQL anonymous completed with errors in 1ms","timestamp":"2025-05-26 02:21:09"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-26 02:21:09"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-26 02:21:09"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:21:10","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:21:10"}
{"level":"http","message":"POST / 200 - 12ms","timestamp":"2025-05-26 02:21:10"}
{"level":"info","message":"WebSocket connection authenticated for user 682bb95fb9b407dd58126686","timestamp":"2025-05-26 02:21:10"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:21:10"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:21:10"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:21:10"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:21:10"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 11ms","timestamp":"2025-05-26 02:21:10"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-26 02:21:10","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 15ms","timestamp":"2025-05-26 02:21:10"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversations {\n  getConversations {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    lastMessage {\n      id\n      content\n      timestamp\n      isRead\n      sender {\n        id\n        username\n      }\n    }\n    unreadCount\n    updatedAt\n  }\n}","timestamp":"2025-05-26 02:21:10","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 65ms","timestamp":"2025-05-26 02:21:10"}
{"level":"http","message":"POST / 200 - 74ms","timestamp":"2025-05-26 02:21:10"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:21:10"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:21:10"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:21:10"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:21:10"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:21:10"}
{"level":"http","message":"GraphQL anonymous completed in 143ms","timestamp":"2025-05-26 02:21:10"}
{"level":"http","message":"POST / 200 - 152ms","timestamp":"2025-05-26 02:21:10"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetVoiceMessages {\n  getVoiceMessages {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    endTime\n    duration\n    conversationId\n    metadata\n  }\n}","timestamp":"2025-05-26 02:21:14","variables":{}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-26 02:21:14","variables":{"conversationId":"683245dbfe5136e30fe9e03a","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=683245dbfe5136e30fe9e03a, userId=682bb95fb9b407dd58126686","timestamp":"2025-05-26 02:21:14"}
{"level":"warn","message":"GraphQL anonymous completed with errors in 44ms","timestamp":"2025-05-26 02:21:14"}
{"level":"http","message":"POST / 200 - 53ms","timestamp":"2025-05-26 02:21:14"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 683245dbfe5136e30fe9e03a, unread: 1, messages: 1","timestamp":"2025-05-26 02:21:15"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=683245dbfe5136e30fe9e03a, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=682bb95fb9b407dd58126686, offset=0","timestamp":"2025-05-26 02:21:15"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetVoiceMessages {\n  getVoiceMessages {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    endTime\n    duration\n    conversationId\n    metadata\n  }\n}","timestamp":"2025-05-26 02:21:15","variables":{}}
{"level":"warn","message":"GraphQL anonymous completed with errors in 29ms","timestamp":"2025-05-26 02:21:15"}
{"level":"http","message":"POST / 200 - 38ms","timestamp":"2025-05-26 02:21:15"}
{"level":"info","message":"[MessageService] Retrieved 1 messages","timestamp":"2025-05-26 02:21:15"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-26 02:21:15"}
{"level":"http","message":"GraphQL anonymous completed in 131ms","timestamp":"2025-05-26 02:21:15"}
{"level":"http","message":"POST / 200 - 143ms","timestamp":"2025-05-26 02:21:15"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation MarkMessageAsRead($messageId: ID!) {\n  markMessageAsRead(messageId: $messageId) {\n    id\n    isRead\n    readAt\n  }\n}","timestamp":"2025-05-26 02:21:15","variables":{"messageId":"683245e2fe5136e30fe9e051"}}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:21:15"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:21:15"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:21:15"}
{"level":"http","message":"GraphQL anonymous completed in 67ms","timestamp":"2025-05-26 02:21:15"}
{"level":"http","message":"POST / 200 - 71ms","timestamp":"2025-05-26 02:21:15"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:21:16","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:21:16"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-26 02:21:16"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation SendMessage($receiverId: ID!, $content: String, $file: Upload, $type: MessageType, $metadata: JSON) {\n  sendMessage(\n    receiverId: $receiverId\n    content: $content\n    file: $file\n    type: $type\n    metadata: $metadata\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    conversation {\n      id\n    }\n    attachments {\n      url\n      type\n      duration\n    }\n    metadata\n  }\n}","timestamp":"2025-05-26 02:21:30","variables":{"content":"salut ","receiverId":"68294132b5d5c86fd2e56e23","type":"TEXT"}}
{"level":"info","message":"[MessageService] Starting sendMessage flow: senderId=682bb95fb9b407dd58126686, receiverId=68294132b5d5c86fd2e56e23, type=TEXT, hasMetadata=false","timestamp":"2025-05-26 02:21:30"}
{"level":"info","message":"[MessageService] Message saved successfully: 6833c21a69f38dababbaf33e","timestamp":"2025-05-26 02:21:30"}
{"level":"info","message":"[MessageService] Message flow completed successfully: messageId=6833c21a69f38dababbaf33e","timestamp":"2025-05-26 02:21:30"}
{"level":"http","message":"GraphQL anonymous completed in 58ms","timestamp":"2025-05-26 02:21:30"}
{"level":"http","message":"POST / 200 - 64ms","timestamp":"2025-05-26 02:21:30"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation SendMessage($receiverId: ID!, $content: String, $file: Upload, $type: MessageType, $metadata: JSON) {\n  sendMessage(\n    receiverId: $receiverId\n    content: $content\n    file: $file\n    type: $type\n    metadata: $metadata\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    conversation {\n      id\n    }\n    attachments {\n      url\n      type\n      duration\n    }\n    metadata\n  }\n}","timestamp":"2025-05-26 02:21:39","variables":{"content":null,"file":{"file":{"encoding":"7bit","filename":"ab3.png","mimetype":"image/png"},"promise":{}},"receiverId":"68294132b5d5c86fd2e56e23","type":"IMAGE"}}
{"level":"info","message":"[MessageService] Starting sendMessage flow: senderId=682bb95fb9b407dd58126686, receiverId=68294132b5d5c86fd2e56e23, type=IMAGE, hasMetadata=false","timestamp":"2025-05-26 02:21:39"}
{"error":"Upload service failed: Upload failed: getaddrinfo ENOTFOUND api.cloudinary.com","level":"error","message":"Send message error:","receiverId":"68294132b5d5c86fd2e56e23","senderId":"682bb95fb9b407dd58126686","stack":"Error: Upload service failed: Upload failed: getaddrinfo ENOTFOUND api.cloudinary.com\n    at uploadFile (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\src\\services\\fileUpload.service.js:62:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async MessageService.handleFileUpload (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\src\\services\\message.service.js:48:17)\n    at async MessageService.sendMessage (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\src\\services\\message.service.js:710:24)\n    at async Object.sendMessage (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\src\\graphql\\messageResolvers.js:721:24)","timestamp":"2025-05-26 02:21:39"}
{"level":"warn","message":"GraphQL anonymous completed with errors in 93ms","timestamp":"2025-05-26 02:21:39"}
{"level":"http","message":"POST / 200 - 106ms","timestamp":"2025-05-26 02:21:39"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:21:40","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:21:40"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-26 02:21:40"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation SendMessage($receiverId: ID!, $content: String, $file: Upload, $type: MessageType, $metadata: JSON) {\n  sendMessage(\n    receiverId: $receiverId\n    content: $content\n    file: $file\n    type: $type\n    metadata: $metadata\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    conversation {\n      id\n    }\n    attachments {\n      url\n      type\n      duration\n    }\n    metadata\n  }\n}","timestamp":"2025-05-26 02:21:40","variables":{"content":null,"file":{"file":{"encoding":"7bit","filename":"ab3.png","mimetype":"image/png"},"promise":{}},"receiverId":"68294132b5d5c86fd2e56e23","type":"IMAGE"}}
{"level":"info","message":"[MessageService] Starting sendMessage flow: senderId=682bb95fb9b407dd58126686, receiverId=68294132b5d5c86fd2e56e23, type=IMAGE, hasMetadata=false","timestamp":"2025-05-26 02:21:40"}
{"error":"Upload service failed: Upload failed: getaddrinfo ENOTFOUND api.cloudinary.com","level":"error","message":"Send message error:","receiverId":"68294132b5d5c86fd2e56e23","senderId":"682bb95fb9b407dd58126686","stack":"Error: Upload service failed: Upload failed: getaddrinfo ENOTFOUND api.cloudinary.com\n    at uploadFile (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\src\\services\\fileUpload.service.js:62:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async MessageService.handleFileUpload (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\src\\services\\message.service.js:48:17)\n    at async MessageService.sendMessage (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\src\\services\\message.service.js:710:24)\n    at async Object.sendMessage (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\src\\graphql\\messageResolvers.js:721:24)","timestamp":"2025-05-26 02:21:40"}
{"level":"warn","message":"GraphQL anonymous completed with errors in 18ms","timestamp":"2025-05-26 02:21:40"}
{"level":"http","message":"POST / 200 - 23ms","timestamp":"2025-05-26 02:21:40"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:21:46","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:21:46"}
{"level":"http","message":"POST / 200 - 8ms","timestamp":"2025-05-26 02:21:46"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation SendMessage($receiverId: ID!, $content: String, $file: Upload, $type: MessageType, $metadata: JSON) {\n  sendMessage(\n    receiverId: $receiverId\n    content: $content\n    file: $file\n    type: $type\n    metadata: $metadata\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    conversation {\n      id\n    }\n    attachments {\n      url\n      type\n      duration\n    }\n    metadata\n  }\n}","timestamp":"2025-05-26 02:21:57","variables":{"content":"💯","receiverId":"68294132b5d5c86fd2e56e23","type":"TEXT"}}
{"level":"info","message":"[MessageService] Starting sendMessage flow: senderId=682bb95fb9b407dd58126686, receiverId=68294132b5d5c86fd2e56e23, type=TEXT, hasMetadata=false","timestamp":"2025-05-26 02:21:57"}
{"level":"info","message":"[MessageService] Message saved successfully: 6833c23569f38dababbaf353","timestamp":"2025-05-26 02:21:57"}
{"level":"info","message":"[MessageService] Message flow completed successfully: messageId=6833c23569f38dababbaf353","timestamp":"2025-05-26 02:21:57"}
{"level":"http","message":"GraphQL anonymous completed in 58ms","timestamp":"2025-05-26 02:21:57"}
{"level":"http","message":"POST / 200 - 64ms","timestamp":"2025-05-26 02:21:57"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:22:11","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:22:11"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-26 02:22:11"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:22:16","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:22:16"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-26 02:22:16"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:22:40","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:22:40"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-26 02:22:40"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:22:46","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:22:46"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-26 02:22:46"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:23:10","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:23:10"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-26 02:23:10"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:23:16","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:23:16"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-26 02:23:16"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversations {\n  getConversations {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    lastMessage {\n      id\n      content\n      timestamp\n      isRead\n      sender {\n        id\n        username\n      }\n    }\n    unreadCount\n    updatedAt\n  }\n}","timestamp":"2025-05-26 02:23:18","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 25ms","timestamp":"2025-05-26 02:23:18"}
{"level":"http","message":"POST / 200 - 31ms","timestamp":"2025-05-26 02:23:18"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation InitiateCall($recipientId: ID!, $callType: CallType!, $callId: String!, $offer: String!, $conversationId: ID, $options: CallOptions) {\n  initiateCall(\n    recipientId: $recipientId\n    callType: $callType\n    callId: $callId\n    offer: $offer\n    conversationId: $conversationId\n    options: $options\n  ) {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    conversationId\n  }\n}","timestamp":"2025-05-26 02:23:25","variables":{"callId":"1748222605256m4ji6mx","callType":"VIDEO","conversationId":"683245dbfe5136e30fe9e03a","offer":"{\"sdp\":\"v=0\\r\\no=- 8934146461818264888 2 IN IP4 127.0.0.1\\r\\ns=-\\r\\nt=0 0\\r\\na=group:BUNDLE 0 1\\r\\na=extmap-allow-mixed\\r\\na=msid-semantic: WMS 1adab8a6-cfd5-4deb-a23b-1147d18407d0\\r\\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 9 0 8 13 110 126\\r\\nc=IN IP4 0.0.0.0\\r\\na=rtcp:9 IN IP4 0.0.0.0\\r\\na=ice-ufrag:FeYe\\r\\na=ice-pwd:Qf7F9j9jGZVXrEK+M49Lcwa0\\r\\na=ice-options:trickle\\r\\na=fingerprint:sha-256 66:D2:EB:F6:35:CA:5E:48:1A:FA:73:C0:88:99:B4:00:4A:FC:D7:B6:B5:1E:82:84:DC:4E:18:6B:AF:97:C4:97\\r\\na=setup:actpass\\r\\na=mid:0\\r\\na=extmap:1 urn:ietf:params:rtp-hdrext:ssrc-audio-level\\r\\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\\r\\na=extmap:3 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\\r\\na=extmap:4 urn:ietf:params:rtp-hdrext:sdes:mid\\r\\na=sendrecv\\r\\na=msid:1adab8a6-cfd5-4deb-a23b-1147d18407d0 8ae99832-40d3-41e5-90bd-7935d9a3e4c5\\r\\na=rtcp-mux\\r\\na=rtcp-rsize\\r\\na=rtpmap:111 opus/48000/2\\r\\na=rtcp-fb:111 transport-cc\\r\\na=fmtp:111 minptime=10;useinbandfec=1\\r\\na=rtpmap:63 red/48000/2\\r\\na=fmtp:63 111/111\\r\\na=rtpmap:9 G722/8000\\r\\na=rtpmap:0 PCMU/8000\\r\\na=rtpmap:8 PCMA/8000\\r\\na=rtpmap:13 CN/8000\\r\\na=rtpmap:110 telephone-event/48000\\r\\na=rtpmap:126 telephone-event/8000\\r\\na=ssrc:1382164633 cname:tjdXt5fEWBh69OeV\\r\\na=ssrc:1382164633 msid:1adab8a6-cfd5-4deb-a23b-1147d18407d0 8ae99832-40d3-41e5-90bd-7935d9a3e4c5\\r\\nm=video 9 UDP/TLS/RTP/SAVPF 96 97 103 104 107 108 109 114 115 116 117 118 39 40 45 46 98 99 100 101 119 120 49 50 123 124 125\\r\\nc=IN IP4 0.0.0.0\\r\\na=rtcp:9 IN IP4 0.0.0.0\\r\\na=ice-ufrag:FeYe\\r\\na=ice-pwd:Qf7F9j9jGZVXrEK+M49Lcwa0\\r\\na=ice-options:trickle\\r\\na=fingerprint:sha-256 66:D2:EB:F6:35:CA:5E:48:1A:FA:73:C0:88:99:B4:00:4A:FC:D7:B6:B5:1E:82:84:DC:4E:18:6B:AF:97:C4:97\\r\\na=setup:actpass\\r\\na=mid:1\\r\\na=extmap:14 urn:ietf:params:rtp-hdrext:toffset\\r\\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\\r\\na=extmap:13 urn:3gpp:video-orientation\\r\\na=extmap:3 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\\r\\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\\r\\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\\r\\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\\r\\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\\r\\na=extmap:4 urn:ietf:params:rtp-hdrext:sdes:mid\\r\\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\\r\\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\\r\\na=sendrecv\\r\\na=msid:1adab8a6-cfd5-4deb-a23b-1147d18407d0 f0c4f90c-28cd-4d20-b5c4-1040aad3d3d0\\r\\na=rtcp-mux\\r\\na=rtcp-rsize\\r\\na=rtpmap:96 VP8/90000\\r\\na=rtcp-fb:96 goog-remb\\r\\na=rtcp-fb:96 transport-cc\\r\\na=rtcp-fb:96 ccm fir\\r\\na=rtcp-fb:96 nack\\r\\na=rtcp-fb:96 nack pli\\r\\na=rtpmap:97 rtx/90000\\r\\na=fmtp:97 apt=96\\r\\na=rtpmap:103 H264/90000\\r\\na=rtcp-fb:103 goog-remb\\r\\na=rtcp-fb:103 transport-cc\\r\\na=rtcp-fb:103 ccm fir\\r\\na=rtcp-fb:103 nack\\r\\na=rtcp-fb:103 nack pli\\r\\na=fmtp:103 level-asymmetry-allowed=1;packetization-mode=1;profile-level-id=42001f\\r\\na=rtpmap:104 rtx/90000\\r\\na=fmtp:104 apt=103\\r\\na=rtpmap:107 H264/90000\\r\\na=rtcp-fb:107 goog-remb\\r\\na=rtcp-fb:107 transport-cc\\r\\na=rtcp-fb:107 ccm fir\\r\\na=rtcp-fb:107 nack\\r\\na=rtcp-fb:107 nack pli\\r\\na=fmtp:107 level-asymmetry-allowed=1;packetization-mode=0;profile-level-id=42001f\\r\\na=rtpmap:108 rtx/90000\\r\\na=fmtp:108 apt=107\\r\\na=rtpmap:109 H264/90000\\r\\na=rtcp-fb:109 goog-remb\\r\\na=rtcp-fb:109 transport-cc\\r\\na=rtcp-fb:109 ccm fir\\r\\na=rtcp-fb:109 nack\\r\\na=rtcp-fb:109 nack pli\\r\\na=fmtp:109 level-asymmetry-allowed=1;packetization-mode=1;profile-level-id=42e01f\\r\\na=rtpmap:114 rtx/90000\\r\\na=fmtp:114 apt=109\\r\\na=rtpmap:115 H264/90000\\r\\na=rtcp-fb:115 goog-remb\\r\\na=rtcp-fb:115 transport-cc\\r\\na=rtcp-fb:115 ccm fir\\r\\na=rtcp-fb:115 nack\\r\\na=rtcp-fb:115 nack pli\\r\\na=fmtp:115 level-asymmetry-allowed=1;packetization-mode=0;profile-level-id=42e01f\\r\\na=rtpmap:116 rtx/90000\\r\\na=fmtp:116 apt=115\\r\\na=rtpmap:117 H264/90000\\r\\na=rtcp-fb:117 goog-remb\\r\\na=rtcp-fb:117 transport-cc\\r\\na=rtcp-fb:117 ccm fir\\r\\na=rtcp-fb:117 nack\\r\\na=rtcp-fb:117 nack pli\\r\\na=fmtp:117 level-asymmetry-allowed=1;packetization-mode=1;profile-level-id=4d001f\\r\\na=rtpmap:118 rtx/90000\\r\\na=fmtp:118 apt=117\\r\\na=rtpmap:39 H264/90000\\r\\na=rtcp-fb:39 goog-remb\\r\\na=rtcp-fb:39 transport-cc\\r\\na=rtcp-fb:39 ccm fir\\r\\na=rtcp-fb:39 nack\\r\\na=rtcp-fb:39 nack pli\\r\\na=fmtp:39 level-asymmetry-allowed=1;packetization-mode=0;profile-level-id=4d001f\\r\\na=rtpmap:40 rtx/90000\\r\\na=fmtp:40 apt=39\\r\\na=rtpmap:45 AV1/90000\\r\\na=rtcp-fb:45 goog-remb\\r\\na=rtcp-fb:45 transport-cc\\r\\na=rtcp-fb:45 ccm fir\\r\\na=rtcp-fb:45 nack\\r\\na=rtcp-fb:45 nack pli\\r\\na=fmtp:45 level-idx=5;profile=0;tier=0\\r\\na=rtpmap:46 rtx/90000\\r\\na=fmtp:46 apt=45\\r\\na=rtpmap:98 VP9/90000\\r\\na=rtcp-fb:98 goog-remb\\r\\na=rtcp-fb:98 transport-cc\\r\\na=rtcp-fb:98 ccm fir\\r\\na=rtcp-fb:98 nack\\r\\na=rtcp-fb:98 nack pli\\r\\na=fmtp:98 profile-id=0\\r\\na=rtpmap:99 rtx/90000\\r\\na=fmtp:99 apt=98\\r\\na=rtpmap:100 VP9/90000\\r\\na=rtcp-fb:100 goog-remb\\r\\na=rtcp-fb:100 transport-cc\\r\\na=rtcp-fb:100 ccm fir\\r\\na=rtcp-fb:100 nack\\r\\na=rtcp-fb:100 nack pli\\r\\na=fmtp:100 profile-id=2\\r\\na=rtpmap:101 rtx/90000\\r\\na=fmtp:101 apt=100\\r\\na=rtpmap:119 H264/90000\\r\\na=rtcp-fb:119 goog-remb\\r\\na=rtcp-fb:119 transport-cc\\r\\na=rtcp-fb:119 ccm fir\\r\\na=rtcp-fb:119 nack\\r\\na=rtcp-fb:119 nack pli\\r\\na=fmtp:119 level-asymmetry-allowed=1;packetization-mode=1;profile-level-id=64001f\\r\\na=rtpmap:120 rtx/90000\\r\\na=fmtp:120 apt=119\\r\\na=rtpmap:49 H265/90000\\r\\na=rtcp-fb:49 goog-remb\\r\\na=rtcp-fb:49 transport-cc\\r\\na=rtcp-fb:49 ccm fir\\r\\na=rtcp-fb:49 nack\\r\\na=rtcp-fb:49 nack pli\\r\\na=fmtp:49 level-id=93;profile-id=1;tier-flag=0;tx-mode=SRST\\r\\na=rtpmap:50 rtx/90000\\r\\na=fmtp:50 apt=49\\r\\na=rtpmap:123 red/90000\\r\\na=rtpmap:124 rtx/90000\\r\\na=fmtp:124 apt=123\\r\\na=rtpmap:125 ulpfec/90000\\r\\na=ssrc-group:FID 873208131 2561013451\\r\\na=ssrc:873208131 cname:tjdXt5fEWBh69OeV\\r\\na=ssrc:873208131 msid:1adab8a6-cfd5-4deb-a23b-1147d18407d0 f0c4f90c-28cd-4d20-b5c4-1040aad3d3d0\\r\\na=ssrc:2561013451 cname:tjdXt5fEWBh69OeV\\r\\na=ssrc:2561013451 msid:1adab8a6-cfd5-4deb-a23b-1147d18407d0 f0c4f90c-28cd-4d20-b5c4-1040aad3d3d0\\r\\n\",\"type\":\"offer\"}","recipientId":"68294132b5d5c86fd2e56e23"}}
{"level":"warn","message":"GraphQL anonymous completed with errors in 6ms","timestamp":"2025-05-26 02:23:25"}
{"level":"http","message":"POST / 200 - 16ms","timestamp":"2025-05-26 02:23:25"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation InitiateCall($recipientId: ID!, $callType: CallType!, $callId: String!, $offer: String!, $conversationId: ID, $options: CallOptions) {\n  initiateCall(\n    recipientId: $recipientId\n    callType: $callType\n    callId: $callId\n    offer: $offer\n    conversationId: $conversationId\n    options: $options\n  ) {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    conversationId\n  }\n}","timestamp":"2025-05-26 02:23:25","variables":{"callId":"174822260528824umbhb","callType":"VIDEO","conversationId":"683245dbfe5136e30fe9e03a","offer":"{\"sdp\":\"v=0\\r\\no=- 2664237209879848673 2 IN IP4 127.0.0.1\\r\\ns=-\\r\\nt=0 0\\r\\na=group:BUNDLE 0 1\\r\\na=extmap-allow-mixed\\r\\na=msid-semantic: WMS 9f5613dc-27ea-44de-841c-8c9e4c5c54f3\\r\\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 9 0 8 13 110 126\\r\\nc=IN IP4 0.0.0.0\\r\\na=rtcp:9 IN IP4 0.0.0.0\\r\\na=ice-ufrag:yh6e\\r\\na=ice-pwd:eRtsWW8cjamijNZCvkWTV+hr\\r\\na=ice-options:trickle\\r\\na=fingerprint:sha-256 DC:64:CA:E2:DD:7C:FC:13:24:74:DA:08:80:27:C6:A7:90:AF:C1:AA:BF:48:AD:22:31:91:A4:B5:62:31:9E:54\\r\\na=setup:actpass\\r\\na=mid:0\\r\\na=extmap:1 urn:ietf:params:rtp-hdrext:ssrc-audio-level\\r\\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\\r\\na=extmap:3 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\\r\\na=extmap:4 urn:ietf:params:rtp-hdrext:sdes:mid\\r\\na=sendrecv\\r\\na=msid:9f5613dc-27ea-44de-841c-8c9e4c5c54f3 3b7a3f80-fe6c-4702-a4fb-76e4a9af0cae\\r\\na=rtcp-mux\\r\\na=rtcp-rsize\\r\\na=rtpmap:111 opus/48000/2\\r\\na=rtcp-fb:111 transport-cc\\r\\na=fmtp:111 minptime=10;useinbandfec=1\\r\\na=rtpmap:63 red/48000/2\\r\\na=fmtp:63 111/111\\r\\na=rtpmap:9 G722/8000\\r\\na=rtpmap:0 PCMU/8000\\r\\na=rtpmap:8 PCMA/8000\\r\\na=rtpmap:13 CN/8000\\r\\na=rtpmap:110 telephone-event/48000\\r\\na=rtpmap:126 telephone-event/8000\\r\\na=ssrc:1517130483 cname:Nh86Gl7bRQZlR0R3\\r\\na=ssrc:1517130483 msid:9f5613dc-27ea-44de-841c-8c9e4c5c54f3 3b7a3f80-fe6c-4702-a4fb-76e4a9af0cae\\r\\nm=video 9 UDP/TLS/RTP/SAVPF 96 97 103 104 107 108 109 114 115 116 117 118 39 40 45 46 98 99 100 101 119 120 49 50 123 124 125\\r\\nc=IN IP4 0.0.0.0\\r\\na=rtcp:9 IN IP4 0.0.0.0\\r\\na=ice-ufrag:yh6e\\r\\na=ice-pwd:eRtsWW8cjamijNZCvkWTV+hr\\r\\na=ice-options:trickle\\r\\na=fingerprint:sha-256 DC:64:CA:E2:DD:7C:FC:13:24:74:DA:08:80:27:C6:A7:90:AF:C1:AA:BF:48:AD:22:31:91:A4:B5:62:31:9E:54\\r\\na=setup:actpass\\r\\na=mid:1\\r\\na=extmap:14 urn:ietf:params:rtp-hdrext:toffset\\r\\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\\r\\na=extmap:13 urn:3gpp:video-orientation\\r\\na=extmap:3 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\\r\\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\\r\\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\\r\\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\\r\\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\\r\\na=extmap:4 urn:ietf:params:rtp-hdrext:sdes:mid\\r\\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\\r\\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\\r\\na=sendrecv\\r\\na=msid:9f5613dc-27ea-44de-841c-8c9e4c5c54f3 9b14cbd3-276f-4ecb-87e2-858dd8df6e50\\r\\na=rtcp-mux\\r\\na=rtcp-rsize\\r\\na=rtpmap:96 VP8/90000\\r\\na=rtcp-fb:96 goog-remb\\r\\na=rtcp-fb:96 transport-cc\\r\\na=rtcp-fb:96 ccm fir\\r\\na=rtcp-fb:96 nack\\r\\na=rtcp-fb:96 nack pli\\r\\na=rtpmap:97 rtx/90000\\r\\na=fmtp:97 apt=96\\r\\na=rtpmap:103 H264/90000\\r\\na=rtcp-fb:103 goog-remb\\r\\na=rtcp-fb:103 transport-cc\\r\\na=rtcp-fb:103 ccm fir\\r\\na=rtcp-fb:103 nack\\r\\na=rtcp-fb:103 nack pli\\r\\na=fmtp:103 level-asymmetry-allowed=1;packetization-mode=1;profile-level-id=42001f\\r\\na=rtpmap:104 rtx/90000\\r\\na=fmtp:104 apt=103\\r\\na=rtpmap:107 H264/90000\\r\\na=rtcp-fb:107 goog-remb\\r\\na=rtcp-fb:107 transport-cc\\r\\na=rtcp-fb:107 ccm fir\\r\\na=rtcp-fb:107 nack\\r\\na=rtcp-fb:107 nack pli\\r\\na=fmtp:107 level-asymmetry-allowed=1;packetization-mode=0;profile-level-id=42001f\\r\\na=rtpmap:108 rtx/90000\\r\\na=fmtp:108 apt=107\\r\\na=rtpmap:109 H264/90000\\r\\na=rtcp-fb:109 goog-remb\\r\\na=rtcp-fb:109 transport-cc\\r\\na=rtcp-fb:109 ccm fir\\r\\na=rtcp-fb:109 nack\\r\\na=rtcp-fb:109 nack pli\\r\\na=fmtp:109 level-asymmetry-allowed=1;packetization-mode=1;profile-level-id=42e01f\\r\\na=rtpmap:114 rtx/90000\\r\\na=fmtp:114 apt=109\\r\\na=rtpmap:115 H264/90000\\r\\na=rtcp-fb:115 goog-remb\\r\\na=rtcp-fb:115 transport-cc\\r\\na=rtcp-fb:115 ccm fir\\r\\na=rtcp-fb:115 nack\\r\\na=rtcp-fb:115 nack pli\\r\\na=fmtp:115 level-asymmetry-allowed=1;packetization-mode=0;profile-level-id=42e01f\\r\\na=rtpmap:116 rtx/90000\\r\\na=fmtp:116 apt=115\\r\\na=rtpmap:117 H264/90000\\r\\na=rtcp-fb:117 goog-remb\\r\\na=rtcp-fb:117 transport-cc\\r\\na=rtcp-fb:117 ccm fir\\r\\na=rtcp-fb:117 nack\\r\\na=rtcp-fb:117 nack pli\\r\\na=fmtp:117 level-asymmetry-allowed=1;packetization-mode=1;profile-level-id=4d001f\\r\\na=rtpmap:118 rtx/90000\\r\\na=fmtp:118 apt=117\\r\\na=rtpmap:39 H264/90000\\r\\na=rtcp-fb:39 goog-remb\\r\\na=rtcp-fb:39 transport-cc\\r\\na=rtcp-fb:39 ccm fir\\r\\na=rtcp-fb:39 nack\\r\\na=rtcp-fb:39 nack pli\\r\\na=fmtp:39 level-asymmetry-allowed=1;packetization-mode=0;profile-level-id=4d001f\\r\\na=rtpmap:40 rtx/90000\\r\\na=fmtp:40 apt=39\\r\\na=rtpmap:45 AV1/90000\\r\\na=rtcp-fb:45 goog-remb\\r\\na=rtcp-fb:45 transport-cc\\r\\na=rtcp-fb:45 ccm fir\\r\\na=rtcp-fb:45 nack\\r\\na=rtcp-fb:45 nack pli\\r\\na=fmtp:45 level-idx=5;profile=0;tier=0\\r\\na=rtpmap:46 rtx/90000\\r\\na=fmtp:46 apt=45\\r\\na=rtpmap:98 VP9/90000\\r\\na=rtcp-fb:98 goog-remb\\r\\na=rtcp-fb:98 transport-cc\\r\\na=rtcp-fb:98 ccm fir\\r\\na=rtcp-fb:98 nack\\r\\na=rtcp-fb:98 nack pli\\r\\na=fmtp:98 profile-id=0\\r\\na=rtpmap:99 rtx/90000\\r\\na=fmtp:99 apt=98\\r\\na=rtpmap:100 VP9/90000\\r\\na=rtcp-fb:100 goog-remb\\r\\na=rtcp-fb:100 transport-cc\\r\\na=rtcp-fb:100 ccm fir\\r\\na=rtcp-fb:100 nack\\r\\na=rtcp-fb:100 nack pli\\r\\na=fmtp:100 profile-id=2\\r\\na=rtpmap:101 rtx/90000\\r\\na=fmtp:101 apt=100\\r\\na=rtpmap:119 H264/90000\\r\\na=rtcp-fb:119 goog-remb\\r\\na=rtcp-fb:119 transport-cc\\r\\na=rtcp-fb:119 ccm fir\\r\\na=rtcp-fb:119 nack\\r\\na=rtcp-fb:119 nack pli\\r\\na=fmtp:119 level-asymmetry-allowed=1;packetization-mode=1;profile-level-id=64001f\\r\\na=rtpmap:120 rtx/90000\\r\\na=fmtp:120 apt=119\\r\\na=rtpmap:49 H265/90000\\r\\na=rtcp-fb:49 goog-remb\\r\\na=rtcp-fb:49 transport-cc\\r\\na=rtcp-fb:49 ccm fir\\r\\na=rtcp-fb:49 nack\\r\\na=rtcp-fb:49 nack pli\\r\\na=fmtp:49 level-id=93;profile-id=1;tier-flag=0;tx-mode=SRST\\r\\na=rtpmap:50 rtx/90000\\r\\na=fmtp:50 apt=49\\r\\na=rtpmap:123 red/90000\\r\\na=rtpmap:124 rtx/90000\\r\\na=fmtp:124 apt=123\\r\\na=rtpmap:125 ulpfec/90000\\r\\na=ssrc-group:FID 1082709584 1846545046\\r\\na=ssrc:1082709584 cname:Nh86Gl7bRQZlR0R3\\r\\na=ssrc:1082709584 msid:9f5613dc-27ea-44de-841c-8c9e4c5c54f3 9b14cbd3-276f-4ecb-87e2-858dd8df6e50\\r\\na=ssrc:1846545046 cname:Nh86Gl7bRQZlR0R3\\r\\na=ssrc:1846545046 msid:9f5613dc-27ea-44de-841c-8c9e4c5c54f3 9b14cbd3-276f-4ecb-87e2-858dd8df6e50\\r\\n\",\"type\":\"offer\"}","recipientId":"68294132b5d5c86fd2e56e23"}}
{"level":"warn","message":"GraphQL anonymous completed with errors in 3ms","timestamp":"2025-05-26 02:23:25"}
{"level":"http","message":"POST / 200 - 8ms","timestamp":"2025-05-26 02:23:25"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation InitiateCall($recipientId: ID!, $callType: CallType!, $callId: String!, $offer: String!, $conversationId: ID, $options: CallOptions) {\n  initiateCall(\n    recipientId: $recipientId\n    callType: $callType\n    callId: $callId\n    offer: $offer\n    conversationId: $conversationId\n    options: $options\n  ) {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    conversationId\n  }\n}","timestamp":"2025-05-26 02:23:25","variables":{"callId":"1748222605256m4ji6mx","callType":"VIDEO","conversationId":"683245dbfe5136e30fe9e03a","offer":"{\"sdp\":\"v=0\\r\\no=- 8934146461818264888 2 IN IP4 127.0.0.1\\r\\ns=-\\r\\nt=0 0\\r\\na=group:BUNDLE 0 1\\r\\na=extmap-allow-mixed\\r\\na=msid-semantic: WMS 1adab8a6-cfd5-4deb-a23b-1147d18407d0\\r\\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 9 0 8 13 110 126\\r\\nc=IN IP4 0.0.0.0\\r\\na=rtcp:9 IN IP4 0.0.0.0\\r\\na=ice-ufrag:FeYe\\r\\na=ice-pwd:Qf7F9j9jGZVXrEK+M49Lcwa0\\r\\na=ice-options:trickle\\r\\na=fingerprint:sha-256 66:D2:EB:F6:35:CA:5E:48:1A:FA:73:C0:88:99:B4:00:4A:FC:D7:B6:B5:1E:82:84:DC:4E:18:6B:AF:97:C4:97\\r\\na=setup:actpass\\r\\na=mid:0\\r\\na=extmap:1 urn:ietf:params:rtp-hdrext:ssrc-audio-level\\r\\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\\r\\na=extmap:3 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\\r\\na=extmap:4 urn:ietf:params:rtp-hdrext:sdes:mid\\r\\na=sendrecv\\r\\na=msid:1adab8a6-cfd5-4deb-a23b-1147d18407d0 8ae99832-40d3-41e5-90bd-7935d9a3e4c5\\r\\na=rtcp-mux\\r\\na=rtcp-rsize\\r\\na=rtpmap:111 opus/48000/2\\r\\na=rtcp-fb:111 transport-cc\\r\\na=fmtp:111 minptime=10;useinbandfec=1\\r\\na=rtpmap:63 red/48000/2\\r\\na=fmtp:63 111/111\\r\\na=rtpmap:9 G722/8000\\r\\na=rtpmap:0 PCMU/8000\\r\\na=rtpmap:8 PCMA/8000\\r\\na=rtpmap:13 CN/8000\\r\\na=rtpmap:110 telephone-event/48000\\r\\na=rtpmap:126 telephone-event/8000\\r\\na=ssrc:1382164633 cname:tjdXt5fEWBh69OeV\\r\\na=ssrc:1382164633 msid:1adab8a6-cfd5-4deb-a23b-1147d18407d0 8ae99832-40d3-41e5-90bd-7935d9a3e4c5\\r\\nm=video 9 UDP/TLS/RTP/SAVPF 96 97 103 104 107 108 109 114 115 116 117 118 39 40 45 46 98 99 100 101 119 120 49 50 123 124 125\\r\\nc=IN IP4 0.0.0.0\\r\\na=rtcp:9 IN IP4 0.0.0.0\\r\\na=ice-ufrag:FeYe\\r\\na=ice-pwd:Qf7F9j9jGZVXrEK+M49Lcwa0\\r\\na=ice-options:trickle\\r\\na=fingerprint:sha-256 66:D2:EB:F6:35:CA:5E:48:1A:FA:73:C0:88:99:B4:00:4A:FC:D7:B6:B5:1E:82:84:DC:4E:18:6B:AF:97:C4:97\\r\\na=setup:actpass\\r\\na=mid:1\\r\\na=extmap:14 urn:ietf:params:rtp-hdrext:toffset\\r\\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\\r\\na=extmap:13 urn:3gpp:video-orientation\\r\\na=extmap:3 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\\r\\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\\r\\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\\r\\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\\r\\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\\r\\na=extmap:4 urn:ietf:params:rtp-hdrext:sdes:mid\\r\\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\\r\\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\\r\\na=sendrecv\\r\\na=msid:1adab8a6-cfd5-4deb-a23b-1147d18407d0 f0c4f90c-28cd-4d20-b5c4-1040aad3d3d0\\r\\na=rtcp-mux\\r\\na=rtcp-rsize\\r\\na=rtpmap:96 VP8/90000\\r\\na=rtcp-fb:96 goog-remb\\r\\na=rtcp-fb:96 transport-cc\\r\\na=rtcp-fb:96 ccm fir\\r\\na=rtcp-fb:96 nack\\r\\na=rtcp-fb:96 nack pli\\r\\na=rtpmap:97 rtx/90000\\r\\na=fmtp:97 apt=96\\r\\na=rtpmap:103 H264/90000\\r\\na=rtcp-fb:103 goog-remb\\r\\na=rtcp-fb:103 transport-cc\\r\\na=rtcp-fb:103 ccm fir\\r\\na=rtcp-fb:103 nack\\r\\na=rtcp-fb:103 nack pli\\r\\na=fmtp:103 level-asymmetry-allowed=1;packetization-mode=1;profile-level-id=42001f\\r\\na=rtpmap:104 rtx/90000\\r\\na=fmtp:104 apt=103\\r\\na=rtpmap:107 H264/90000\\r\\na=rtcp-fb:107 goog-remb\\r\\na=rtcp-fb:107 transport-cc\\r\\na=rtcp-fb:107 ccm fir\\r\\na=rtcp-fb:107 nack\\r\\na=rtcp-fb:107 nack pli\\r\\na=fmtp:107 level-asymmetry-allowed=1;packetization-mode=0;profile-level-id=42001f\\r\\na=rtpmap:108 rtx/90000\\r\\na=fmtp:108 apt=107\\r\\na=rtpmap:109 H264/90000\\r\\na=rtcp-fb:109 goog-remb\\r\\na=rtcp-fb:109 transport-cc\\r\\na=rtcp-fb:109 ccm fir\\r\\na=rtcp-fb:109 nack\\r\\na=rtcp-fb:109 nack pli\\r\\na=fmtp:109 level-asymmetry-allowed=1;packetization-mode=1;profile-level-id=42e01f\\r\\na=rtpmap:114 rtx/90000\\r\\na=fmtp:114 apt=109\\r\\na=rtpmap:115 H264/90000\\r\\na=rtcp-fb:115 goog-remb\\r\\na=rtcp-fb:115 transport-cc\\r\\na=rtcp-fb:115 ccm fir\\r\\na=rtcp-fb:115 nack\\r\\na=rtcp-fb:115 nack pli\\r\\na=fmtp:115 level-asymmetry-allowed=1;packetization-mode=0;profile-level-id=42e01f\\r\\na=rtpmap:116 rtx/90000\\r\\na=fmtp:116 apt=115\\r\\na=rtpmap:117 H264/90000\\r\\na=rtcp-fb:117 goog-remb\\r\\na=rtcp-fb:117 transport-cc\\r\\na=rtcp-fb:117 ccm fir\\r\\na=rtcp-fb:117 nack\\r\\na=rtcp-fb:117 nack pli\\r\\na=fmtp:117 level-asymmetry-allowed=1;packetization-mode=1;profile-level-id=4d001f\\r\\na=rtpmap:118 rtx/90000\\r\\na=fmtp:118 apt=117\\r\\na=rtpmap:39 H264/90000\\r\\na=rtcp-fb:39 goog-remb\\r\\na=rtcp-fb:39 transport-cc\\r\\na=rtcp-fb:39 ccm fir\\r\\na=rtcp-fb:39 nack\\r\\na=rtcp-fb:39 nack pli\\r\\na=fmtp:39 level-asymmetry-allowed=1;packetization-mode=0;profile-level-id=4d001f\\r\\na=rtpmap:40 rtx/90000\\r\\na=fmtp:40 apt=39\\r\\na=rtpmap:45 AV1/90000\\r\\na=rtcp-fb:45 goog-remb\\r\\na=rtcp-fb:45 transport-cc\\r\\na=rtcp-fb:45 ccm fir\\r\\na=rtcp-fb:45 nack\\r\\na=rtcp-fb:45 nack pli\\r\\na=fmtp:45 level-idx=5;profile=0;tier=0\\r\\na=rtpmap:46 rtx/90000\\r\\na=fmtp:46 apt=45\\r\\na=rtpmap:98 VP9/90000\\r\\na=rtcp-fb:98 goog-remb\\r\\na=rtcp-fb:98 transport-cc\\r\\na=rtcp-fb:98 ccm fir\\r\\na=rtcp-fb:98 nack\\r\\na=rtcp-fb:98 nack pli\\r\\na=fmtp:98 profile-id=0\\r\\na=rtpmap:99 rtx/90000\\r\\na=fmtp:99 apt=98\\r\\na=rtpmap:100 VP9/90000\\r\\na=rtcp-fb:100 goog-remb\\r\\na=rtcp-fb:100 transport-cc\\r\\na=rtcp-fb:100 ccm fir\\r\\na=rtcp-fb:100 nack\\r\\na=rtcp-fb:100 nack pli\\r\\na=fmtp:100 profile-id=2\\r\\na=rtpmap:101 rtx/90000\\r\\na=fmtp:101 apt=100\\r\\na=rtpmap:119 H264/90000\\r\\na=rtcp-fb:119 goog-remb\\r\\na=rtcp-fb:119 transport-cc\\r\\na=rtcp-fb:119 ccm fir\\r\\na=rtcp-fb:119 nack\\r\\na=rtcp-fb:119 nack pli\\r\\na=fmtp:119 level-asymmetry-allowed=1;packetization-mode=1;profile-level-id=64001f\\r\\na=rtpmap:120 rtx/90000\\r\\na=fmtp:120 apt=119\\r\\na=rtpmap:49 H265/90000\\r\\na=rtcp-fb:49 goog-remb\\r\\na=rtcp-fb:49 transport-cc\\r\\na=rtcp-fb:49 ccm fir\\r\\na=rtcp-fb:49 nack\\r\\na=rtcp-fb:49 nack pli\\r\\na=fmtp:49 level-id=93;profile-id=1;tier-flag=0;tx-mode=SRST\\r\\na=rtpmap:50 rtx/90000\\r\\na=fmtp:50 apt=49\\r\\na=rtpmap:123 red/90000\\r\\na=rtpmap:124 rtx/90000\\r\\na=fmtp:124 apt=123\\r\\na=rtpmap:125 ulpfec/90000\\r\\na=ssrc-group:FID 873208131 2561013451\\r\\na=ssrc:873208131 cname:tjdXt5fEWBh69OeV\\r\\na=ssrc:873208131 msid:1adab8a6-cfd5-4deb-a23b-1147d18407d0 f0c4f90c-28cd-4d20-b5c4-1040aad3d3d0\\r\\na=ssrc:2561013451 cname:tjdXt5fEWBh69OeV\\r\\na=ssrc:2561013451 msid:1adab8a6-cfd5-4deb-a23b-1147d18407d0 f0c4f90c-28cd-4d20-b5c4-1040aad3d3d0\\r\\n\",\"type\":\"offer\"}","recipientId":"68294132b5d5c86fd2e56e23"}}
{"level":"warn","message":"GraphQL anonymous completed with errors in 1ms","timestamp":"2025-05-26 02:23:25"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-26 02:23:25"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation InitiateCall($recipientId: ID!, $callType: CallType!, $callId: String!, $offer: String!, $conversationId: ID, $options: CallOptions) {\n  initiateCall(\n    recipientId: $recipientId\n    callType: $callType\n    callId: $callId\n    offer: $offer\n    conversationId: $conversationId\n    options: $options\n  ) {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    conversationId\n  }\n}","timestamp":"2025-05-26 02:23:25","variables":{"callId":"174822260528824umbhb","callType":"VIDEO","conversationId":"683245dbfe5136e30fe9e03a","offer":"{\"sdp\":\"v=0\\r\\no=- 2664237209879848673 2 IN IP4 127.0.0.1\\r\\ns=-\\r\\nt=0 0\\r\\na=group:BUNDLE 0 1\\r\\na=extmap-allow-mixed\\r\\na=msid-semantic: WMS 9f5613dc-27ea-44de-841c-8c9e4c5c54f3\\r\\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 9 0 8 13 110 126\\r\\nc=IN IP4 0.0.0.0\\r\\na=rtcp:9 IN IP4 0.0.0.0\\r\\na=ice-ufrag:yh6e\\r\\na=ice-pwd:eRtsWW8cjamijNZCvkWTV+hr\\r\\na=ice-options:trickle\\r\\na=fingerprint:sha-256 DC:64:CA:E2:DD:7C:FC:13:24:74:DA:08:80:27:C6:A7:90:AF:C1:AA:BF:48:AD:22:31:91:A4:B5:62:31:9E:54\\r\\na=setup:actpass\\r\\na=mid:0\\r\\na=extmap:1 urn:ietf:params:rtp-hdrext:ssrc-audio-level\\r\\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\\r\\na=extmap:3 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\\r\\na=extmap:4 urn:ietf:params:rtp-hdrext:sdes:mid\\r\\na=sendrecv\\r\\na=msid:9f5613dc-27ea-44de-841c-8c9e4c5c54f3 3b7a3f80-fe6c-4702-a4fb-76e4a9af0cae\\r\\na=rtcp-mux\\r\\na=rtcp-rsize\\r\\na=rtpmap:111 opus/48000/2\\r\\na=rtcp-fb:111 transport-cc\\r\\na=fmtp:111 minptime=10;useinbandfec=1\\r\\na=rtpmap:63 red/48000/2\\r\\na=fmtp:63 111/111\\r\\na=rtpmap:9 G722/8000\\r\\na=rtpmap:0 PCMU/8000\\r\\na=rtpmap:8 PCMA/8000\\r\\na=rtpmap:13 CN/8000\\r\\na=rtpmap:110 telephone-event/48000\\r\\na=rtpmap:126 telephone-event/8000\\r\\na=ssrc:1517130483 cname:Nh86Gl7bRQZlR0R3\\r\\na=ssrc:1517130483 msid:9f5613dc-27ea-44de-841c-8c9e4c5c54f3 3b7a3f80-fe6c-4702-a4fb-76e4a9af0cae\\r\\nm=video 9 UDP/TLS/RTP/SAVPF 96 97 103 104 107 108 109 114 115 116 117 118 39 40 45 46 98 99 100 101 119 120 49 50 123 124 125\\r\\nc=IN IP4 0.0.0.0\\r\\na=rtcp:9 IN IP4 0.0.0.0\\r\\na=ice-ufrag:yh6e\\r\\na=ice-pwd:eRtsWW8cjamijNZCvkWTV+hr\\r\\na=ice-options:trickle\\r\\na=fingerprint:sha-256 DC:64:CA:E2:DD:7C:FC:13:24:74:DA:08:80:27:C6:A7:90:AF:C1:AA:BF:48:AD:22:31:91:A4:B5:62:31:9E:54\\r\\na=setup:actpass\\r\\na=mid:1\\r\\na=extmap:14 urn:ietf:params:rtp-hdrext:toffset\\r\\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\\r\\na=extmap:13 urn:3gpp:video-orientation\\r\\na=extmap:3 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\\r\\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\\r\\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\\r\\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\\r\\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\\r\\na=extmap:4 urn:ietf:params:rtp-hdrext:sdes:mid\\r\\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\\r\\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\\r\\na=sendrecv\\r\\na=msid:9f5613dc-27ea-44de-841c-8c9e4c5c54f3 9b14cbd3-276f-4ecb-87e2-858dd8df6e50\\r\\na=rtcp-mux\\r\\na=rtcp-rsize\\r\\na=rtpmap:96 VP8/90000\\r\\na=rtcp-fb:96 goog-remb\\r\\na=rtcp-fb:96 transport-cc\\r\\na=rtcp-fb:96 ccm fir\\r\\na=rtcp-fb:96 nack\\r\\na=rtcp-fb:96 nack pli\\r\\na=rtpmap:97 rtx/90000\\r\\na=fmtp:97 apt=96\\r\\na=rtpmap:103 H264/90000\\r\\na=rtcp-fb:103 goog-remb\\r\\na=rtcp-fb:103 transport-cc\\r\\na=rtcp-fb:103 ccm fir\\r\\na=rtcp-fb:103 nack\\r\\na=rtcp-fb:103 nack pli\\r\\na=fmtp:103 level-asymmetry-allowed=1;packetization-mode=1;profile-level-id=42001f\\r\\na=rtpmap:104 rtx/90000\\r\\na=fmtp:104 apt=103\\r\\na=rtpmap:107 H264/90000\\r\\na=rtcp-fb:107 goog-remb\\r\\na=rtcp-fb:107 transport-cc\\r\\na=rtcp-fb:107 ccm fir\\r\\na=rtcp-fb:107 nack\\r\\na=rtcp-fb:107 nack pli\\r\\na=fmtp:107 level-asymmetry-allowed=1;packetization-mode=0;profile-level-id=42001f\\r\\na=rtpmap:108 rtx/90000\\r\\na=fmtp:108 apt=107\\r\\na=rtpmap:109 H264/90000\\r\\na=rtcp-fb:109 goog-remb\\r\\na=rtcp-fb:109 transport-cc\\r\\na=rtcp-fb:109 ccm fir\\r\\na=rtcp-fb:109 nack\\r\\na=rtcp-fb:109 nack pli\\r\\na=fmtp:109 level-asymmetry-allowed=1;packetization-mode=1;profile-level-id=42e01f\\r\\na=rtpmap:114 rtx/90000\\r\\na=fmtp:114 apt=109\\r\\na=rtpmap:115 H264/90000\\r\\na=rtcp-fb:115 goog-remb\\r\\na=rtcp-fb:115 transport-cc\\r\\na=rtcp-fb:115 ccm fir\\r\\na=rtcp-fb:115 nack\\r\\na=rtcp-fb:115 nack pli\\r\\na=fmtp:115 level-asymmetry-allowed=1;packetization-mode=0;profile-level-id=42e01f\\r\\na=rtpmap:116 rtx/90000\\r\\na=fmtp:116 apt=115\\r\\na=rtpmap:117 H264/90000\\r\\na=rtcp-fb:117 goog-remb\\r\\na=rtcp-fb:117 transport-cc\\r\\na=rtcp-fb:117 ccm fir\\r\\na=rtcp-fb:117 nack\\r\\na=rtcp-fb:117 nack pli\\r\\na=fmtp:117 level-asymmetry-allowed=1;packetization-mode=1;profile-level-id=4d001f\\r\\na=rtpmap:118 rtx/90000\\r\\na=fmtp:118 apt=117\\r\\na=rtpmap:39 H264/90000\\r\\na=rtcp-fb:39 goog-remb\\r\\na=rtcp-fb:39 transport-cc\\r\\na=rtcp-fb:39 ccm fir\\r\\na=rtcp-fb:39 nack\\r\\na=rtcp-fb:39 nack pli\\r\\na=fmtp:39 level-asymmetry-allowed=1;packetization-mode=0;profile-level-id=4d001f\\r\\na=rtpmap:40 rtx/90000\\r\\na=fmtp:40 apt=39\\r\\na=rtpmap:45 AV1/90000\\r\\na=rtcp-fb:45 goog-remb\\r\\na=rtcp-fb:45 transport-cc\\r\\na=rtcp-fb:45 ccm fir\\r\\na=rtcp-fb:45 nack\\r\\na=rtcp-fb:45 nack pli\\r\\na=fmtp:45 level-idx=5;profile=0;tier=0\\r\\na=rtpmap:46 rtx/90000\\r\\na=fmtp:46 apt=45\\r\\na=rtpmap:98 VP9/90000\\r\\na=rtcp-fb:98 goog-remb\\r\\na=rtcp-fb:98 transport-cc\\r\\na=rtcp-fb:98 ccm fir\\r\\na=rtcp-fb:98 nack\\r\\na=rtcp-fb:98 nack pli\\r\\na=fmtp:98 profile-id=0\\r\\na=rtpmap:99 rtx/90000\\r\\na=fmtp:99 apt=98\\r\\na=rtpmap:100 VP9/90000\\r\\na=rtcp-fb:100 goog-remb\\r\\na=rtcp-fb:100 transport-cc\\r\\na=rtcp-fb:100 ccm fir\\r\\na=rtcp-fb:100 nack\\r\\na=rtcp-fb:100 nack pli\\r\\na=fmtp:100 profile-id=2\\r\\na=rtpmap:101 rtx/90000\\r\\na=fmtp:101 apt=100\\r\\na=rtpmap:119 H264/90000\\r\\na=rtcp-fb:119 goog-remb\\r\\na=rtcp-fb:119 transport-cc\\r\\na=rtcp-fb:119 ccm fir\\r\\na=rtcp-fb:119 nack\\r\\na=rtcp-fb:119 nack pli\\r\\na=fmtp:119 level-asymmetry-allowed=1;packetization-mode=1;profile-level-id=64001f\\r\\na=rtpmap:120 rtx/90000\\r\\na=fmtp:120 apt=119\\r\\na=rtpmap:49 H265/90000\\r\\na=rtcp-fb:49 goog-remb\\r\\na=rtcp-fb:49 transport-cc\\r\\na=rtcp-fb:49 ccm fir\\r\\na=rtcp-fb:49 nack\\r\\na=rtcp-fb:49 nack pli\\r\\na=fmtp:49 level-id=93;profile-id=1;tier-flag=0;tx-mode=SRST\\r\\na=rtpmap:50 rtx/90000\\r\\na=fmtp:50 apt=49\\r\\na=rtpmap:123 red/90000\\r\\na=rtpmap:124 rtx/90000\\r\\na=fmtp:124 apt=123\\r\\na=rtpmap:125 ulpfec/90000\\r\\na=ssrc-group:FID 1082709584 1846545046\\r\\na=ssrc:1082709584 cname:Nh86Gl7bRQZlR0R3\\r\\na=ssrc:1082709584 msid:9f5613dc-27ea-44de-841c-8c9e4c5c54f3 9b14cbd3-276f-4ecb-87e2-858dd8df6e50\\r\\na=ssrc:1846545046 cname:Nh86Gl7bRQZlR0R3\\r\\na=ssrc:1846545046 msid:9f5613dc-27ea-44de-841c-8c9e4c5c54f3 9b14cbd3-276f-4ecb-87e2-858dd8df6e50\\r\\n\",\"type\":\"offer\"}","recipientId":"68294132b5d5c86fd2e56e23"}}
{"level":"warn","message":"GraphQL anonymous completed with errors in 1ms","timestamp":"2025-05-26 02:23:25"}
{"level":"http","message":"POST / 200 - 8ms","timestamp":"2025-05-26 02:23:25"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetVoiceMessages {\n  getVoiceMessages {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    endTime\n    duration\n    conversationId\n    metadata\n  }\n}","timestamp":"2025-05-26 02:23:25","variables":{}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-26 02:23:25","variables":{"conversationId":"683245dbfe5136e30fe9e03a","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=683245dbfe5136e30fe9e03a, userId=682bb95fb9b407dd58126686","timestamp":"2025-05-26 02:23:25"}
{"level":"warn","message":"GraphQL anonymous completed with errors in 31ms","timestamp":"2025-05-26 02:23:25"}
{"level":"http","message":"POST / 200 - 37ms","timestamp":"2025-05-26 02:23:25"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 683245dbfe5136e30fe9e03a, unread: 0, messages: 3","timestamp":"2025-05-26 02:23:25"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=683245dbfe5136e30fe9e03a, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=682bb95fb9b407dd58126686, offset=0","timestamp":"2025-05-26 02:23:25"}
{"level":"info","message":"[MessageService] Retrieved 3 messages","timestamp":"2025-05-26 02:23:25"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-26 02:23:25"}
{"level":"http","message":"GraphQL anonymous completed in 66ms","timestamp":"2025-05-26 02:23:25"}
{"level":"http","message":"POST / 200 - 72ms","timestamp":"2025-05-26 02:23:25"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetVoiceMessages {\n  getVoiceMessages {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    endTime\n    duration\n    conversationId\n    metadata\n  }\n}","timestamp":"2025-05-26 02:23:25","variables":{}}
{"level":"warn","message":"GraphQL anonymous completed with errors in 17ms","timestamp":"2025-05-26 02:23:25"}
{"level":"http","message":"POST / 200 - 22ms","timestamp":"2025-05-26 02:23:25"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:23:25"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation InitiateCall($recipientId: ID!, $callType: CallType!, $callId: String!, $offer: String!, $conversationId: ID, $options: CallOptions) {\n  initiateCall(\n    recipientId: $recipientId\n    callType: $callType\n    callId: $callId\n    offer: $offer\n    conversationId: $conversationId\n    options: $options\n  ) {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    conversationId\n  }\n}","timestamp":"2025-05-26 02:23:26","variables":{"callId":"1748222606062715xpg5","callType":"AUDIO","conversationId":"683245dbfe5136e30fe9e03a","offer":"{\"sdp\":\"v=0\\r\\no=- 5837706811821852710 2 IN IP4 127.0.0.1\\r\\ns=-\\r\\nt=0 0\\r\\na=group:BUNDLE 0\\r\\na=extmap-allow-mixed\\r\\na=msid-semantic: WMS 1947153a-cd50-4776-b599-3ef490bc193c\\r\\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 9 0 8 13 110 126\\r\\nc=IN IP4 0.0.0.0\\r\\na=rtcp:9 IN IP4 0.0.0.0\\r\\na=ice-ufrag:0V88\\r\\na=ice-pwd:a6MroyNZ9oxsoneDub0xKPzQ\\r\\na=ice-options:trickle\\r\\na=fingerprint:sha-256 48:A6:00:A0:06:4A:9B:34:6C:81:9A:2D:78:C3:B1:93:7A:AC:B8:F1:0D:44:D5:FB:20:DF:9A:29:74:E1:A4:13\\r\\na=setup:actpass\\r\\na=mid:0\\r\\na=extmap:1 urn:ietf:params:rtp-hdrext:ssrc-audio-level\\r\\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\\r\\na=extmap:3 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\\r\\na=extmap:4 urn:ietf:params:rtp-hdrext:sdes:mid\\r\\na=sendrecv\\r\\na=msid:1947153a-cd50-4776-b599-3ef490bc193c 07c1f7ec-dd37-4cb9-a0e9-47cec636665e\\r\\na=rtcp-mux\\r\\na=rtcp-rsize\\r\\na=rtpmap:111 opus/48000/2\\r\\na=rtcp-fb:111 transport-cc\\r\\na=fmtp:111 minptime=10;useinbandfec=1\\r\\na=rtpmap:63 red/48000/2\\r\\na=fmtp:63 111/111\\r\\na=rtpmap:9 G722/8000\\r\\na=rtpmap:0 PCMU/8000\\r\\na=rtpmap:8 PCMA/8000\\r\\na=rtpmap:13 CN/8000\\r\\na=rtpmap:110 telephone-event/48000\\r\\na=rtpmap:126 telephone-event/8000\\r\\na=ssrc:3751477412 cname:QsndXxy8zZqCo/8n\\r\\na=ssrc:3751477412 msid:1947153a-cd50-4776-b599-3ef490bc193c 07c1f7ec-dd37-4cb9-a0e9-47cec636665e\\r\\n\",\"type\":\"offer\"}","recipientId":"68294132b5d5c86fd2e56e23"}}
{"level":"warn","message":"GraphQL anonymous completed with errors in 1ms","timestamp":"2025-05-26 02:23:26"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-26 02:23:26"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation InitiateCall($recipientId: ID!, $callType: CallType!, $callId: String!, $offer: String!, $conversationId: ID, $options: CallOptions) {\n  initiateCall(\n    recipientId: $recipientId\n    callType: $callType\n    callId: $callId\n    offer: $offer\n    conversationId: $conversationId\n    options: $options\n  ) {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    conversationId\n  }\n}","timestamp":"2025-05-26 02:23:26","variables":{"callId":"1748222606062715xpg5","callType":"AUDIO","conversationId":"683245dbfe5136e30fe9e03a","offer":"{\"sdp\":\"v=0\\r\\no=- 5837706811821852710 2 IN IP4 127.0.0.1\\r\\ns=-\\r\\nt=0 0\\r\\na=group:BUNDLE 0\\r\\na=extmap-allow-mixed\\r\\na=msid-semantic: WMS 1947153a-cd50-4776-b599-3ef490bc193c\\r\\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 9 0 8 13 110 126\\r\\nc=IN IP4 0.0.0.0\\r\\na=rtcp:9 IN IP4 0.0.0.0\\r\\na=ice-ufrag:0V88\\r\\na=ice-pwd:a6MroyNZ9oxsoneDub0xKPzQ\\r\\na=ice-options:trickle\\r\\na=fingerprint:sha-256 48:A6:00:A0:06:4A:9B:34:6C:81:9A:2D:78:C3:B1:93:7A:AC:B8:F1:0D:44:D5:FB:20:DF:9A:29:74:E1:A4:13\\r\\na=setup:actpass\\r\\na=mid:0\\r\\na=extmap:1 urn:ietf:params:rtp-hdrext:ssrc-audio-level\\r\\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\\r\\na=extmap:3 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\\r\\na=extmap:4 urn:ietf:params:rtp-hdrext:sdes:mid\\r\\na=sendrecv\\r\\na=msid:1947153a-cd50-4776-b599-3ef490bc193c 07c1f7ec-dd37-4cb9-a0e9-47cec636665e\\r\\na=rtcp-mux\\r\\na=rtcp-rsize\\r\\na=rtpmap:111 opus/48000/2\\r\\na=rtcp-fb:111 transport-cc\\r\\na=fmtp:111 minptime=10;useinbandfec=1\\r\\na=rtpmap:63 red/48000/2\\r\\na=fmtp:63 111/111\\r\\na=rtpmap:9 G722/8000\\r\\na=rtpmap:0 PCMU/8000\\r\\na=rtpmap:8 PCMA/8000\\r\\na=rtpmap:13 CN/8000\\r\\na=rtpmap:110 telephone-event/48000\\r\\na=rtpmap:126 telephone-event/8000\\r\\na=ssrc:3751477412 cname:QsndXxy8zZqCo/8n\\r\\na=ssrc:3751477412 msid:1947153a-cd50-4776-b599-3ef490bc193c 07c1f7ec-dd37-4cb9-a0e9-47cec636665e\\r\\n\",\"type\":\"offer\"}","recipientId":"68294132b5d5c86fd2e56e23"}}
{"level":"warn","message":"GraphQL anonymous completed with errors in 1ms","timestamp":"2025-05-26 02:23:26"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-26 02:23:26"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation SendMessage($receiverId: ID!, $content: String, $file: Upload, $type: MessageType, $metadata: JSON) {\n  sendMessage(\n    receiverId: $receiverId\n    content: $content\n    file: $file\n    type: $type\n    metadata: $metadata\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    conversation {\n      id\n    }\n    attachments {\n      url\n      type\n      duration\n    }\n    metadata\n  }\n}","timestamp":"2025-05-26 02:23:35","variables":{"content":"","receiverId":"68294132b5d5c86fd2e56e23","type":"TEXT"}}
{"level":"info","message":"[MessageService] Starting sendMessage flow: senderId=682bb95fb9b407dd58126686, receiverId=68294132b5d5c86fd2e56e23, type=TEXT, hasMetadata=false","timestamp":"2025-05-26 02:23:35"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"68294132b5d5c86fd2e56e23","senderId":"682bb95fb9b407dd58126686","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-26 02:23:35"}
{"level":"warn","message":"GraphQL anonymous completed with errors in 16ms","timestamp":"2025-05-26 02:23:35"}
{"level":"http","message":"POST / 200 - 19ms","timestamp":"2025-05-26 02:23:35"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation SendMessage($receiverId: ID!, $content: String, $file: Upload, $type: MessageType, $metadata: JSON) {\n  sendMessage(\n    receiverId: $receiverId\n    content: $content\n    file: $file\n    type: $type\n    metadata: $metadata\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    conversation {\n      id\n    }\n    attachments {\n      url\n      type\n      duration\n    }\n    metadata\n  }\n}","timestamp":"2025-05-26 02:23:35","variables":{"content":"","receiverId":"68294132b5d5c86fd2e56e23","type":"TEXT"}}
{"level":"info","message":"[MessageService] Starting sendMessage flow: senderId=682bb95fb9b407dd58126686, receiverId=68294132b5d5c86fd2e56e23, type=TEXT, hasMetadata=false","timestamp":"2025-05-26 02:23:35"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"68294132b5d5c86fd2e56e23","senderId":"682bb95fb9b407dd58126686","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-26 02:23:35"}
{"level":"warn","message":"GraphQL anonymous completed with errors in 16ms","timestamp":"2025-05-26 02:23:35"}
{"level":"http","message":"POST / 200 - 19ms","timestamp":"2025-05-26 02:23:35"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:23:40","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:23:40"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-26 02:23:40"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation SendMessage($receiverId: ID!, $content: String, $file: Upload, $type: MessageType, $metadata: JSON) {\n  sendMessage(\n    receiverId: $receiverId\n    content: $content\n    file: $file\n    type: $type\n    metadata: $metadata\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    conversation {\n      id\n    }\n    attachments {\n      url\n      type\n      duration\n    }\n    metadata\n  }\n}","timestamp":"2025-05-26 02:23:41","variables":{"content":null,"receiverId":"68294132b5d5c86fd2e56e23","type":"TEXT"}}
{"level":"info","message":"[MessageService] Starting sendMessage flow: senderId=682bb95fb9b407dd58126686, receiverId=68294132b5d5c86fd2e56e23, type=TEXT, hasMetadata=false","timestamp":"2025-05-26 02:23:41"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"68294132b5d5c86fd2e56e23","senderId":"682bb95fb9b407dd58126686","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-26 02:23:41"}
{"level":"warn","message":"GraphQL anonymous completed with errors in 17ms","timestamp":"2025-05-26 02:23:41"}
{"level":"http","message":"POST / 200 - 20ms","timestamp":"2025-05-26 02:23:42"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation SendMessage($receiverId: ID!, $content: String, $file: Upload, $type: MessageType, $metadata: JSON) {\n  sendMessage(\n    receiverId: $receiverId\n    content: $content\n    file: $file\n    type: $type\n    metadata: $metadata\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    conversation {\n      id\n    }\n    attachments {\n      url\n      type\n      duration\n    }\n    metadata\n  }\n}","timestamp":"2025-05-26 02:23:42","variables":{"content":null,"receiverId":"68294132b5d5c86fd2e56e23","type":"TEXT"}}
{"level":"info","message":"[MessageService] Starting sendMessage flow: senderId=682bb95fb9b407dd58126686, receiverId=68294132b5d5c86fd2e56e23, type=TEXT, hasMetadata=false","timestamp":"2025-05-26 02:23:42"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"68294132b5d5c86fd2e56e23","senderId":"682bb95fb9b407dd58126686","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-26 02:23:42"}
{"level":"warn","message":"GraphQL anonymous completed with errors in 12ms","timestamp":"2025-05-26 02:23:42"}
{"level":"http","message":"POST / 200 - 17ms","timestamp":"2025-05-26 02:23:42"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:23:46","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:23:46"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-26 02:23:46"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation SendMessage($receiverId: ID!, $content: String, $file: Upload, $type: MessageType, $metadata: JSON) {\n  sendMessage(\n    receiverId: $receiverId\n    content: $content\n    file: $file\n    type: $type\n    metadata: $metadata\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    conversation {\n      id\n    }\n    attachments {\n      url\n      type\n      duration\n    }\n    metadata\n  }\n}","timestamp":"2025-05-26 02:23:48","variables":{"content":null,"receiverId":"68294132b5d5c86fd2e56e23","type":"TEXT"}}
{"level":"info","message":"[MessageService] Starting sendMessage flow: senderId=682bb95fb9b407dd58126686, receiverId=68294132b5d5c86fd2e56e23, type=TEXT, hasMetadata=false","timestamp":"2025-05-26 02:23:48"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"68294132b5d5c86fd2e56e23","senderId":"682bb95fb9b407dd58126686","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-26 02:23:48"}
{"level":"warn","message":"GraphQL anonymous completed with errors in 17ms","timestamp":"2025-05-26 02:23:48"}
{"level":"http","message":"POST / 200 - 22ms","timestamp":"2025-05-26 02:23:48"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation SendMessage($receiverId: ID!, $content: String, $file: Upload, $type: MessageType, $metadata: JSON) {\n  sendMessage(\n    receiverId: $receiverId\n    content: $content\n    file: $file\n    type: $type\n    metadata: $metadata\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    conversation {\n      id\n    }\n    attachments {\n      url\n      type\n      duration\n    }\n    metadata\n  }\n}","timestamp":"2025-05-26 02:23:48","variables":{"content":null,"receiverId":"68294132b5d5c86fd2e56e23","type":"TEXT"}}
{"level":"info","message":"[MessageService] Starting sendMessage flow: senderId=682bb95fb9b407dd58126686, receiverId=68294132b5d5c86fd2e56e23, type=TEXT, hasMetadata=false","timestamp":"2025-05-26 02:23:48"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"68294132b5d5c86fd2e56e23","senderId":"682bb95fb9b407dd58126686","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-26 02:23:48"}
{"level":"warn","message":"GraphQL anonymous completed with errors in 19ms","timestamp":"2025-05-26 02:23:48"}
{"level":"http","message":"POST / 200 - 22ms","timestamp":"2025-05-26 02:23:48"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:24:10","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:24:10"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-26 02:24:10"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:24:16","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:24:16"}
{"level":"http","message":"POST / 200 - 8ms","timestamp":"2025-05-26 02:24:16"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation SendMessage($receiverId: ID!, $content: String, $file: Upload, $type: MessageType, $metadata: JSON) {\n  sendMessage(\n    receiverId: $receiverId\n    content: $content\n    file: $file\n    type: $type\n    metadata: $metadata\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    conversation {\n      id\n    }\n    attachments {\n      url\n      type\n      duration\n    }\n    metadata\n  }\n}","timestamp":"2025-05-26 02:24:23","variables":{"content":null,"receiverId":"68294132b5d5c86fd2e56e23","type":"TEXT"}}
{"level":"info","message":"[MessageService] Starting sendMessage flow: senderId=682bb95fb9b407dd58126686, receiverId=68294132b5d5c86fd2e56e23, type=TEXT, hasMetadata=false","timestamp":"2025-05-26 02:24:23"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"68294132b5d5c86fd2e56e23","senderId":"682bb95fb9b407dd58126686","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-26 02:24:23"}
{"level":"warn","message":"GraphQL anonymous completed with errors in 18ms","timestamp":"2025-05-26 02:24:23"}
{"level":"http","message":"POST / 200 - 25ms","timestamp":"2025-05-26 02:24:23"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation SendMessage($receiverId: ID!, $content: String, $file: Upload, $type: MessageType, $metadata: JSON) {\n  sendMessage(\n    receiverId: $receiverId\n    content: $content\n    file: $file\n    type: $type\n    metadata: $metadata\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    conversation {\n      id\n    }\n    attachments {\n      url\n      type\n      duration\n    }\n    metadata\n  }\n}","timestamp":"2025-05-26 02:24:24","variables":{"content":null,"receiverId":"68294132b5d5c86fd2e56e23","type":"TEXT"}}
{"level":"info","message":"[MessageService] Starting sendMessage flow: senderId=682bb95fb9b407dd58126686, receiverId=68294132b5d5c86fd2e56e23, type=TEXT, hasMetadata=false","timestamp":"2025-05-26 02:24:24"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"68294132b5d5c86fd2e56e23","senderId":"682bb95fb9b407dd58126686","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-26 02:24:24"}
{"level":"warn","message":"GraphQL anonymous completed with errors in 26ms","timestamp":"2025-05-26 02:24:24"}
{"level":"http","message":"POST / 200 - 28ms","timestamp":"2025-05-26 02:24:24"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation InitiateCall($recipientId: ID!, $callType: CallType!, $callId: String!, $offer: String!, $conversationId: ID, $options: CallOptions) {\n  initiateCall(\n    recipientId: $recipientId\n    callType: $callType\n    callId: $callId\n    offer: $offer\n    conversationId: $conversationId\n    options: $options\n  ) {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    conversationId\n  }\n}","timestamp":"2025-05-26 02:24:34","variables":{"callId":"1748222674949c7y78li","callType":"AUDIO","conversationId":"683245dbfe5136e30fe9e03a","offer":"{\"sdp\":\"v=0\\r\\no=- 2823850953987605145 2 IN IP4 127.0.0.1\\r\\ns=-\\r\\nt=0 0\\r\\na=group:BUNDLE 0\\r\\na=extmap-allow-mixed\\r\\na=msid-semantic: WMS dc081060-e328-4b05-8268-00ada8cac5be\\r\\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 9 0 8 13 110 126\\r\\nc=IN IP4 0.0.0.0\\r\\na=rtcp:9 IN IP4 0.0.0.0\\r\\na=ice-ufrag:kB/r\\r\\na=ice-pwd:OrI7miruQE7rPFtmxkk0+BrI\\r\\na=ice-options:trickle\\r\\na=fingerprint:sha-256 E3:B7:16:9B:43:AA:9C:A3:38:64:FF:44:37:54:06:6B:E2:C8:F9:61:45:A6:BB:59:F1:CE:1D:85:67:72:61:E5\\r\\na=setup:actpass\\r\\na=mid:0\\r\\na=extmap:1 urn:ietf:params:rtp-hdrext:ssrc-audio-level\\r\\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\\r\\na=extmap:3 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\\r\\na=extmap:4 urn:ietf:params:rtp-hdrext:sdes:mid\\r\\na=sendrecv\\r\\na=msid:dc081060-e328-4b05-8268-00ada8cac5be b154b585-4259-4ffe-b5e9-9ab22734dc1e\\r\\na=rtcp-mux\\r\\na=rtcp-rsize\\r\\na=rtpmap:111 opus/48000/2\\r\\na=rtcp-fb:111 transport-cc\\r\\na=fmtp:111 minptime=10;useinbandfec=1\\r\\na=rtpmap:63 red/48000/2\\r\\na=fmtp:63 111/111\\r\\na=rtpmap:9 G722/8000\\r\\na=rtpmap:0 PCMU/8000\\r\\na=rtpmap:8 PCMA/8000\\r\\na=rtpmap:13 CN/8000\\r\\na=rtpmap:110 telephone-event/48000\\r\\na=rtpmap:126 telephone-event/8000\\r\\na=ssrc:4101211784 cname:TmHyR7out7zMHaje\\r\\na=ssrc:4101211784 msid:dc081060-e328-4b05-8268-00ada8cac5be b154b585-4259-4ffe-b5e9-9ab22734dc1e\\r\\n\",\"type\":\"offer\"}","recipientId":"68294132b5d5c86fd2e56e23"}}
{"level":"warn","message":"GraphQL anonymous completed with errors in 3ms","timestamp":"2025-05-26 02:24:34"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-26 02:24:34"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation InitiateCall($recipientId: ID!, $callType: CallType!, $callId: String!, $offer: String!, $conversationId: ID, $options: CallOptions) {\n  initiateCall(\n    recipientId: $recipientId\n    callType: $callType\n    callId: $callId\n    offer: $offer\n    conversationId: $conversationId\n    options: $options\n  ) {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    conversationId\n  }\n}","timestamp":"2025-05-26 02:24:35","variables":{"callId":"1748222674949c7y78li","callType":"AUDIO","conversationId":"683245dbfe5136e30fe9e03a","offer":"{\"sdp\":\"v=0\\r\\no=- 2823850953987605145 2 IN IP4 127.0.0.1\\r\\ns=-\\r\\nt=0 0\\r\\na=group:BUNDLE 0\\r\\na=extmap-allow-mixed\\r\\na=msid-semantic: WMS dc081060-e328-4b05-8268-00ada8cac5be\\r\\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 9 0 8 13 110 126\\r\\nc=IN IP4 0.0.0.0\\r\\na=rtcp:9 IN IP4 0.0.0.0\\r\\na=ice-ufrag:kB/r\\r\\na=ice-pwd:OrI7miruQE7rPFtmxkk0+BrI\\r\\na=ice-options:trickle\\r\\na=fingerprint:sha-256 E3:B7:16:9B:43:AA:9C:A3:38:64:FF:44:37:54:06:6B:E2:C8:F9:61:45:A6:BB:59:F1:CE:1D:85:67:72:61:E5\\r\\na=setup:actpass\\r\\na=mid:0\\r\\na=extmap:1 urn:ietf:params:rtp-hdrext:ssrc-audio-level\\r\\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\\r\\na=extmap:3 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\\r\\na=extmap:4 urn:ietf:params:rtp-hdrext:sdes:mid\\r\\na=sendrecv\\r\\na=msid:dc081060-e328-4b05-8268-00ada8cac5be b154b585-4259-4ffe-b5e9-9ab22734dc1e\\r\\na=rtcp-mux\\r\\na=rtcp-rsize\\r\\na=rtpmap:111 opus/48000/2\\r\\na=rtcp-fb:111 transport-cc\\r\\na=fmtp:111 minptime=10;useinbandfec=1\\r\\na=rtpmap:63 red/48000/2\\r\\na=fmtp:63 111/111\\r\\na=rtpmap:9 G722/8000\\r\\na=rtpmap:0 PCMU/8000\\r\\na=rtpmap:8 PCMA/8000\\r\\na=rtpmap:13 CN/8000\\r\\na=rtpmap:110 telephone-event/48000\\r\\na=rtpmap:126 telephone-event/8000\\r\\na=ssrc:4101211784 cname:TmHyR7out7zMHaje\\r\\na=ssrc:4101211784 msid:dc081060-e328-4b05-8268-00ada8cac5be b154b585-4259-4ffe-b5e9-9ab22734dc1e\\r\\n\",\"type\":\"offer\"}","recipientId":"68294132b5d5c86fd2e56e23"}}
{"level":"warn","message":"GraphQL anonymous completed with errors in 1ms","timestamp":"2025-05-26 02:24:35"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-26 02:24:35"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:24:40","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:24:40"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-26 02:24:40"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:24:46","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:24:46"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-26 02:24:46"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation SendMessage($receiverId: ID!, $content: String, $file: Upload, $type: MessageType, $metadata: JSON) {\n  sendMessage(\n    receiverId: $receiverId\n    content: $content\n    file: $file\n    type: $type\n    metadata: $metadata\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    conversation {\n      id\n    }\n    attachments {\n      url\n      type\n      duration\n    }\n    metadata\n  }\n}","timestamp":"2025-05-26 02:24:57","variables":{"content":null,"receiverId":"68294132b5d5c86fd2e56e23","type":"TEXT"}}
{"level":"info","message":"[MessageService] Starting sendMessage flow: senderId=682bb95fb9b407dd58126686, receiverId=68294132b5d5c86fd2e56e23, type=TEXT, hasMetadata=false","timestamp":"2025-05-26 02:24:57"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"68294132b5d5c86fd2e56e23","senderId":"682bb95fb9b407dd58126686","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-26 02:24:57"}
{"level":"warn","message":"GraphQL anonymous completed with errors in 29ms","timestamp":"2025-05-26 02:24:57"}
{"level":"http","message":"POST / 200 - 38ms","timestamp":"2025-05-26 02:24:57"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation SendMessage($receiverId: ID!, $content: String, $file: Upload, $type: MessageType, $metadata: JSON) {\n  sendMessage(\n    receiverId: $receiverId\n    content: $content\n    file: $file\n    type: $type\n    metadata: $metadata\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    conversation {\n      id\n    }\n    attachments {\n      url\n      type\n      duration\n    }\n    metadata\n  }\n}","timestamp":"2025-05-26 02:24:57","variables":{"content":null,"receiverId":"68294132b5d5c86fd2e56e23","type":"TEXT"}}
{"level":"info","message":"[MessageService] Starting sendMessage flow: senderId=682bb95fb9b407dd58126686, receiverId=68294132b5d5c86fd2e56e23, type=TEXT, hasMetadata=false","timestamp":"2025-05-26 02:24:57"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"68294132b5d5c86fd2e56e23","senderId":"682bb95fb9b407dd58126686","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-26 02:24:57"}
{"level":"warn","message":"GraphQL anonymous completed with errors in 16ms","timestamp":"2025-05-26 02:24:57"}
{"level":"http","message":"POST / 200 - 21ms","timestamp":"2025-05-26 02:24:57"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:25:11","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:25:11"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-26 02:25:11"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:25:16","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:25:16"}
{"level":"http","message":"POST / 200 - 9ms","timestamp":"2025-05-26 02:25:16"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:25:40","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:25:40"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-26 02:25:40"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:25:46","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:25:46"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-26 02:25:46"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:26:11","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:26:11"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-26 02:26:11"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:26:16","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:26:16"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-26 02:26:16"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:26:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:26:41"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-26 02:26:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:26:46","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:26:46"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-26 02:26:46"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:27:11","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:27:11"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-26 02:27:11"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:27:15","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:27:15"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-26 02:27:15"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:27:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:27:41"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-26 02:27:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:27:46","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:27:46"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-26 02:27:46"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:28:11","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:28:11"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-26 02:28:11"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:28:16","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:28:16"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-26 02:28:16"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:28:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:28:41"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-26 02:28:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:28:45","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:28:45"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-26 02:28:45"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:29:11","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:29:11"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-26 02:29:11"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:29:18","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:29:18"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-26 02:29:18"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:29:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:29:41"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-26 02:29:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:29:47","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:29:47"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-26 02:29:47"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:30:11","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:30:11"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-26 02:30:11"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:30:17","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:30:17"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-26 02:30:17"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-26 02:30:29"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:30:29","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:30:29"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-26 02:30:29"}
{"level":"info","message":"WebSocket connection authenticated for user 68294132b5d5c86fd2e56e23","timestamp":"2025-05-26 02:30:29"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:30:29"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:30:29"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:30:29"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:30:29"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-26 02:30:29","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 200 - 9ms","timestamp":"2025-05-26 02:30:29"}
{"level":"http","message":"GraphQL anonymous completed in 41ms","timestamp":"2025-05-26 02:30:29"}
{"level":"http","message":"POST / 200 - 47ms","timestamp":"2025-05-26 02:30:29"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 38ms","timestamp":"2025-05-26 02:30:29"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetVoiceMessages {\n  getVoiceMessages {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    endTime\n    duration\n    conversationId\n    metadata\n  }\n}","timestamp":"2025-05-26 02:30:29","variables":{}}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:30:29"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:30:29"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:30:29"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:30:29"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-26 02:30:29","variables":{"conversationId":"6832451afe5136e30fe9dfaf","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6832451afe5136e30fe9dfaf, userId=68294132b5d5c86fd2e56e23","timestamp":"2025-05-26 02:30:29"}
{"level":"http","message":"GraphQL anonymous completed in 37ms","timestamp":"2025-05-26 02:30:29"}
{"level":"http","message":"POST / 200 - 46ms","timestamp":"2025-05-26 02:30:29"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6832451afe5136e30fe9dfaf, unread: 0, messages: 5","timestamp":"2025-05-26 02:30:29"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6832451afe5136e30fe9dfaf, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=68294132b5d5c86fd2e56e23, offset=0","timestamp":"2025-05-26 02:30:29"}
{"level":"info","message":"[MessageService] Retrieved 5 messages","timestamp":"2025-05-26 02:30:29"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-26 02:30:29"}
{"level":"http","message":"GraphQL anonymous completed in 69ms","timestamp":"2025-05-26 02:30:29"}
{"level":"http","message":"POST / 200 - 72ms","timestamp":"2025-05-26 02:30:29"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:30:29"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:30:29"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:30:29"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:30:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:30:41"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-26 02:30:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation SendMessage($receiverId: ID!, $content: String, $file: Upload, $type: MessageType, $metadata: JSON) {\n  sendMessage(\n    receiverId: $receiverId\n    content: $content\n    file: $file\n    type: $type\n    metadata: $metadata\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    conversation {\n      id\n    }\n    attachments {\n      url\n      type\n      duration\n    }\n    metadata\n  }\n}","timestamp":"2025-05-26 02:30:51","variables":{"content":"","receiverId":"68294542b5d5c86fd2e56e4b","type":"TEXT"}}
{"level":"info","message":"[MessageService] Starting sendMessage flow: senderId=68294132b5d5c86fd2e56e23, receiverId=68294542b5d5c86fd2e56e4b, type=TEXT, hasMetadata=false","timestamp":"2025-05-26 02:30:51"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"68294542b5d5c86fd2e56e4b","senderId":"68294132b5d5c86fd2e56e23","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-26 02:30:51"}
{"level":"warn","message":"GraphQL anonymous completed with errors in 15ms","timestamp":"2025-05-26 02:30:51"}
{"level":"http","message":"POST / 200 - 18ms","timestamp":"2025-05-26 02:30:51"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation SendMessage($receiverId: ID!, $content: String, $file: Upload, $type: MessageType, $metadata: JSON) {\n  sendMessage(\n    receiverId: $receiverId\n    content: $content\n    file: $file\n    type: $type\n    metadata: $metadata\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    conversation {\n      id\n    }\n    attachments {\n      url\n      type\n      duration\n    }\n    metadata\n  }\n}","timestamp":"2025-05-26 02:30:52","variables":{"content":"","receiverId":"68294542b5d5c86fd2e56e4b","type":"TEXT"}}
{"level":"info","message":"[MessageService] Starting sendMessage flow: senderId=68294132b5d5c86fd2e56e23, receiverId=68294542b5d5c86fd2e56e4b, type=TEXT, hasMetadata=false","timestamp":"2025-05-26 02:30:52"}
{"error":"Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe","level":"error","message":"Send message error:","receiverId":"68294542b5d5c86fd2e56e4b","senderId":"68294132b5d5c86fd2e56e23","stack":"ValidationError: Message validation failed: content: Le message doit avoir du contenu ou au moins une pièce jointe\n    at Document.invalidate (C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3329:32)\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\document.js:3090:17\n    at C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\backend\\node_modules\\mongoose\\lib\\schemaType.js:1407:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:77:11)","timestamp":"2025-05-26 02:30:52"}
{"level":"warn","message":"GraphQL anonymous completed with errors in 14ms","timestamp":"2025-05-26 02:30:52"}
{"level":"http","message":"POST / 200 - 18ms","timestamp":"2025-05-26 02:30:52"}
{"level":"http","message":"GraphQL anonymous operation started","query":"mutation SendMessage($receiverId: ID!, $content: String, $file: Upload, $type: MessageType, $metadata: JSON) {\n  sendMessage(\n    receiverId: $receiverId\n    content: $content\n    file: $file\n    type: $type\n    metadata: $metadata\n  ) {\n    id\n    content\n    type\n    timestamp\n    isRead\n    sender {\n      id\n      username\n      image\n    }\n    conversation {\n      id\n    }\n    attachments {\n      url\n      type\n      duration\n    }\n    metadata\n  }\n}","timestamp":"2025-05-26 02:30:57","variables":{"content":" ","conversationId":"6832451afe5136e30fe9dfaf","file":{"file":{"encoding":"7bit","filename":"voice-message-1748223057212.webm","mimetype":"audio/webm"},"promise":{}},"metadata":{"duration":0,"isVoiceMessage":true,"timestamp":1748223057212},"receiverId":"68294542b5d5c86fd2e56e4b","type":"VOICE_MESSAGE"}}
{"level":"info","message":"[MessageService] Starting sendMessage flow: senderId=68294132b5d5c86fd2e56e23, receiverId=68294542b5d5c86fd2e56e4b, type=VOICE_MESSAGE, hasMetadata=true","timestamp":"2025-05-26 02:30:57"}
{"level":"info","message":"[MessageService] Message saved successfully: 6833c45269f38dababbaf3c3","timestamp":"2025-05-26 02:30:58"}
{"level":"info","message":"[MessageService] Message flow completed successfully: messageId=6833c45269f38dababbaf3c3","timestamp":"2025-05-26 02:30:58"}
{"level":"http","message":"GraphQL anonymous completed in 1776ms","timestamp":"2025-05-26 02:30:59"}
{"level":"http","message":"POST / 200 - 1782ms","timestamp":"2025-05-26 02:30:59"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:30:59","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:30:59"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-26 02:30:59"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:31:11","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:31:11"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-26 02:31:11"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:31:30","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:31:30"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-26 02:31:30"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:31:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:31:41"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-26 02:31:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:32:00","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:32:00"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-26 02:32:00"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:32:11","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:32:11"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-26 02:32:11"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:32:29","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:32:29"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-26 02:32:29"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:32:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:32:41"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-26 02:32:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:32:59","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:32:59"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-26 02:32:59"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:33:11","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:33:11"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-26 02:33:11"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:33:29","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:33:29"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-26 02:33:29"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:33:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:33:41"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-26 02:33:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:33:59","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:33:59"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-26 02:33:59"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:34:11","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:34:11"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-26 02:34:11"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:34:29","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:34:29"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-26 02:34:29"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:34:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:34:41"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-26 02:34:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:34:59","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:34:59"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-26 02:34:59"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:35:11","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:35:11"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-26 02:35:11"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:35:29","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:35:29"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-26 02:35:29"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:35:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:35:41"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-26 02:35:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:35:59","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:35:59"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-26 02:35:59"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:36:11","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:36:11"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-26 02:36:11"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:36:29","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:36:29"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-26 02:36:29"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:36:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:36:41"}
{"level":"http","message":"POST / 200 - 9ms","timestamp":"2025-05-26 02:36:41"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-26 02:36:42"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:36:44","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:36:44"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-26 02:36:44"}
{"level":"info","message":"WebSocket connection authenticated for user 682bb95fb9b407dd58126686","timestamp":"2025-05-26 02:36:44"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:36:44"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:36:44"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:36:44"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:36:44"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 9ms","timestamp":"2025-05-26 02:36:44"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 7ms","timestamp":"2025-05-26 02:36:44"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-26 02:36:44","variables":{"limit":10,"page":1}}
{"level":"http","message":"GraphQL anonymous completed in 44ms","timestamp":"2025-05-26 02:36:44"}
{"level":"http","message":"POST / 200 - 53ms","timestamp":"2025-05-26 02:36:44"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetVoiceMessages {\n  getVoiceMessages {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    endTime\n    duration\n    conversationId\n    metadata\n  }\n}","timestamp":"2025-05-26 02:36:44","variables":{}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-26 02:36:44","variables":{"conversationId":"683245dbfe5136e30fe9e03a","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=683245dbfe5136e30fe9e03a, userId=682bb95fb9b407dd58126686","timestamp":"2025-05-26 02:36:44"}
{"level":"warn","message":"GraphQL anonymous completed with errors in 22ms","timestamp":"2025-05-26 02:36:44"}
{"level":"http","message":"POST / 200 - 28ms","timestamp":"2025-05-26 02:36:44"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 683245dbfe5136e30fe9e03a, unread: 0, messages: 3","timestamp":"2025-05-26 02:36:44"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=683245dbfe5136e30fe9e03a, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=682bb95fb9b407dd58126686, offset=0","timestamp":"2025-05-26 02:36:44"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:36:44"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:36:44"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:36:44"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:36:44"}
{"level":"info","message":"[MessageService] Retrieved 3 messages","timestamp":"2025-05-26 02:36:44"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-26 02:36:44"}
{"level":"http","message":"GraphQL anonymous completed in 83ms","timestamp":"2025-05-26 02:36:44"}
{"level":"http","message":"POST / 200 - 86ms","timestamp":"2025-05-26 02:36:44"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetVoiceMessages {\n  getVoiceMessages {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    endTime\n    duration\n    conversationId\n    metadata\n  }\n}","timestamp":"2025-05-26 02:36:45","variables":{}}
{"level":"warn","message":"GraphQL anonymous completed with errors in 10ms","timestamp":"2025-05-26 02:36:45"}
{"level":"http","message":"POST / 200 - 13ms","timestamp":"2025-05-26 02:36:45"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:36:45"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:36:45"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:36:45"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-26 02:36:46"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:36:46","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:36:46"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-26 02:36:46"}
{"level":"info","message":"WebSocket connection authenticated for user 68294132b5d5c86fd2e56e23","timestamp":"2025-05-26 02:36:46"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:36:46"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:36:46"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:36:46"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:36:46"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-26 02:36:46","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 17ms","timestamp":"2025-05-26 02:36:46"}
{"level":"http","message":"GraphQL anonymous completed in 47ms","timestamp":"2025-05-26 02:36:46"}
{"level":"http","message":"POST / 200 - 53ms","timestamp":"2025-05-26 02:36:46"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 37ms","timestamp":"2025-05-26 02:36:46"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetVoiceMessages {\n  getVoiceMessages {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    endTime\n    duration\n    conversationId\n    metadata\n  }\n}","timestamp":"2025-05-26 02:36:46","variables":{}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-26 02:36:46","variables":{"conversationId":"6832451afe5136e30fe9dfaf","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6832451afe5136e30fe9dfaf, userId=68294132b5d5c86fd2e56e23","timestamp":"2025-05-26 02:36:46"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:36:46"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:36:46"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:36:46"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:36:46"}
{"level":"http","message":"GraphQL anonymous completed in 56ms","timestamp":"2025-05-26 02:36:46"}
{"level":"http","message":"POST / 200 - 60ms","timestamp":"2025-05-26 02:36:46"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6832451afe5136e30fe9dfaf, unread: 0, messages: 6","timestamp":"2025-05-26 02:36:46"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6832451afe5136e30fe9dfaf, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=68294132b5d5c86fd2e56e23, offset=0","timestamp":"2025-05-26 02:36:46"}
{"level":"info","message":"[MessageService] Retrieved 6 messages","timestamp":"2025-05-26 02:36:46"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-26 02:36:46"}
{"level":"http","message":"GraphQL anonymous completed in 102ms","timestamp":"2025-05-26 02:36:47"}
{"level":"http","message":"POST / 200 - 114ms","timestamp":"2025-05-26 02:36:47"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:36:47"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:36:47"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:36:47"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:37:15","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:37:15"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-26 02:37:15"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:37:16","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:37:16"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-26 02:37:16"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:37:45","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:37:45"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-26 02:37:45"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:37:46","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:37:46"}
{"level":"http","message":"POST / 200 - 9ms","timestamp":"2025-05-26 02:37:46"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-26 02:37:58"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-26 02:38:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:38:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:38:01"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-26 02:38:01"}
{"level":"info","message":"WebSocket connection authenticated for user 68294132b5d5c86fd2e56e23","timestamp":"2025-05-26 02:38:01"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:38:01"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:38:01"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:38:01"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:38:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-26 02:38:01","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 21ms","timestamp":"2025-05-26 02:38:01"}
{"level":"http","message":"GraphQL anonymous completed in 48ms","timestamp":"2025-05-26 02:38:01"}
{"level":"http","message":"POST / 200 - 54ms","timestamp":"2025-05-26 02:38:01"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 43ms","timestamp":"2025-05-26 02:38:01"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:38:01"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:38:01"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:38:01"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:38:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetVoiceMessages {\n  getVoiceMessages {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    endTime\n    duration\n    conversationId\n    metadata\n  }\n}","timestamp":"2025-05-26 02:38:01","variables":{}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-26 02:38:01","variables":{"conversationId":"6832451afe5136e30fe9dfaf","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6832451afe5136e30fe9dfaf, userId=68294132b5d5c86fd2e56e23","timestamp":"2025-05-26 02:38:01"}
{"level":"http","message":"GraphQL anonymous completed in 49ms","timestamp":"2025-05-26 02:38:01"}
{"level":"http","message":"POST / 200 - 53ms","timestamp":"2025-05-26 02:38:01"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6832451afe5136e30fe9dfaf, unread: 0, messages: 6","timestamp":"2025-05-26 02:38:01"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6832451afe5136e30fe9dfaf, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=68294132b5d5c86fd2e56e23, offset=0","timestamp":"2025-05-26 02:38:01"}
{"level":"info","message":"[MessageService] Retrieved 6 messages","timestamp":"2025-05-26 02:38:01"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-26 02:38:01"}
{"level":"http","message":"GraphQL anonymous completed in 88ms","timestamp":"2025-05-26 02:38:01"}
{"level":"http","message":"POST / 200 - 97ms","timestamp":"2025-05-26 02:38:01"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:38:02"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:38:02"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:38:02"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:38:02","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:38:02"}
{"level":"http","message":"POST / 200 - 12ms","timestamp":"2025-05-26 02:38:02"}
{"level":"info","message":"WebSocket connection authenticated for user 682bb95fb9b407dd58126686","timestamp":"2025-05-26 02:38:02"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:38:02"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:38:02"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:38:02"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:38:02"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 6ms","timestamp":"2025-05-26 02:38:02"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 6ms","timestamp":"2025-05-26 02:38:02"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-26 02:38:02","variables":{"limit":10,"page":1}}
{"level":"http","message":"GraphQL anonymous completed in 198ms","timestamp":"2025-05-26 02:38:03"}
{"level":"http","message":"POST / 200 - 339ms","timestamp":"2025-05-26 02:38:03"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetVoiceMessages {\n  getVoiceMessages {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    endTime\n    duration\n    conversationId\n    metadata\n  }\n}","timestamp":"2025-05-26 02:38:03","variables":{}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-26 02:38:03","variables":{"conversationId":"683245dbfe5136e30fe9e03a","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=683245dbfe5136e30fe9e03a, userId=682bb95fb9b407dd58126686","timestamp":"2025-05-26 02:38:03"}
{"level":"warn","message":"GraphQL anonymous completed with errors in 52ms","timestamp":"2025-05-26 02:38:03"}
{"level":"http","message":"POST / 200 - 72ms","timestamp":"2025-05-26 02:38:03"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 683245dbfe5136e30fe9e03a, unread: 0, messages: 3","timestamp":"2025-05-26 02:38:03"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=683245dbfe5136e30fe9e03a, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=682bb95fb9b407dd58126686, offset=0","timestamp":"2025-05-26 02:38:03"}
{"level":"info","message":"[MessageService] Retrieved 3 messages","timestamp":"2025-05-26 02:38:03"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-26 02:38:03"}
{"level":"http","message":"GraphQL anonymous completed in 128ms","timestamp":"2025-05-26 02:38:03"}
{"level":"http","message":"POST / 200 - 140ms","timestamp":"2025-05-26 02:38:03"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:38:03"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:38:03"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:38:03"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:38:03"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetVoiceMessages {\n  getVoiceMessages {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    endTime\n    duration\n    conversationId\n    metadata\n  }\n}","timestamp":"2025-05-26 02:38:04","variables":{}}
{"level":"warn","message":"GraphQL anonymous completed with errors in 15ms","timestamp":"2025-05-26 02:38:04"}
{"level":"http","message":"POST / 200 - 21ms","timestamp":"2025-05-26 02:38:04"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:38:04"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:38:04"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:38:04"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:38:31","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:38:31"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-26 02:38:31"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:38:33","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:38:33"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-26 02:38:33"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:39:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:39:01"}
{"level":"http","message":"POST / 200 - 8ms","timestamp":"2025-05-26 02:39:01"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-26 02:39:02"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-26 02:39:06"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:39:06","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:39:06"}
{"level":"http","message":"POST / 200 - 14ms","timestamp":"2025-05-26 02:39:06"}
{"level":"info","message":"WebSocket connection authenticated for user 68294132b5d5c86fd2e56e23","timestamp":"2025-05-26 02:39:06"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:39:06"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:39:06"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:39:06"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:39:06"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 8ms","timestamp":"2025-05-26 02:39:06"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-26 02:39:06","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 48ms","timestamp":"2025-05-26 02:39:06"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetVoiceMessages {\n  getVoiceMessages {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    endTime\n    duration\n    conversationId\n    metadata\n  }\n}","timestamp":"2025-05-26 02:39:06","variables":{}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-26 02:39:06","variables":{"conversationId":"6832451afe5136e30fe9dfaf","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6832451afe5136e30fe9dfaf, userId=68294132b5d5c86fd2e56e23","timestamp":"2025-05-26 02:39:06"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:39:06"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:39:06"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:39:06"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:39:06"}
{"level":"http","message":"GraphQL anonymous completed in 146ms","timestamp":"2025-05-26 02:39:07"}
{"level":"http","message":"POST / 200 - 164ms","timestamp":"2025-05-26 02:39:07"}
{"level":"http","message":"GraphQL anonymous completed in 142ms","timestamp":"2025-05-26 02:39:07"}
{"level":"http","message":"POST / 200 - 152ms","timestamp":"2025-05-26 02:39:07"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6832451afe5136e30fe9dfaf, unread: 0, messages: 6","timestamp":"2025-05-26 02:39:07"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6832451afe5136e30fe9dfaf, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=68294132b5d5c86fd2e56e23, offset=0","timestamp":"2025-05-26 02:39:07"}
{"level":"info","message":"[MessageService] Retrieved 6 messages","timestamp":"2025-05-26 02:39:07"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-26 02:39:07"}
{"level":"http","message":"GraphQL anonymous completed in 203ms","timestamp":"2025-05-26 02:39:07"}
{"level":"http","message":"POST / 200 - 212ms","timestamp":"2025-05-26 02:39:07"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:39:07"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:39:07"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:39:07"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:39:07","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:39:07"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-26 02:39:07"}
{"level":"info","message":"WebSocket connection authenticated for user 682bb95fb9b407dd58126686","timestamp":"2025-05-26 02:39:07"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:39:07"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:39:07"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:39:07"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:39:07"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 8ms","timestamp":"2025-05-26 02:39:07"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 9ms","timestamp":"2025-05-26 02:39:07"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-26 02:39:07","variables":{"limit":10,"page":1}}
{"level":"http","message":"GraphQL anonymous completed in 33ms","timestamp":"2025-05-26 02:39:07"}
{"level":"http","message":"POST / 200 - 40ms","timestamp":"2025-05-26 02:39:07"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetVoiceMessages {\n  getVoiceMessages {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    endTime\n    duration\n    conversationId\n    metadata\n  }\n}","timestamp":"2025-05-26 02:39:08","variables":{}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-26 02:39:08","variables":{"conversationId":"683245dbfe5136e30fe9e03a","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=683245dbfe5136e30fe9e03a, userId=682bb95fb9b407dd58126686","timestamp":"2025-05-26 02:39:08"}
{"level":"warn","message":"GraphQL anonymous completed with errors in 63ms","timestamp":"2025-05-26 02:39:08"}
{"level":"http","message":"POST / 200 - 90ms","timestamp":"2025-05-26 02:39:08"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 683245dbfe5136e30fe9e03a, unread: 0, messages: 3","timestamp":"2025-05-26 02:39:08"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=683245dbfe5136e30fe9e03a, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=682bb95fb9b407dd58126686, offset=0","timestamp":"2025-05-26 02:39:08"}
{"level":"info","message":"[MessageService] Retrieved 3 messages","timestamp":"2025-05-26 02:39:08"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-26 02:39:08"}
{"level":"http","message":"GraphQL anonymous completed in 164ms","timestamp":"2025-05-26 02:39:08"}
{"level":"http","message":"POST / 200 - 169ms","timestamp":"2025-05-26 02:39:08"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:39:08"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:39:08"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:39:08"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:39:08"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetVoiceMessages {\n  getVoiceMessages {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    endTime\n    duration\n    conversationId\n    metadata\n  }\n}","timestamp":"2025-05-26 02:39:09","variables":{}}
{"level":"warn","message":"GraphQL anonymous completed with errors in 16ms","timestamp":"2025-05-26 02:39:09"}
{"level":"http","message":"POST / 200 - 22ms","timestamp":"2025-05-26 02:39:09"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:39:09"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:39:09"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:39:09"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-26 02:39:23"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:39:24","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:39:24"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-26 02:39:24"}
{"level":"info","message":"WebSocket connection authenticated for user 682bb95fb9b407dd58126686","timestamp":"2025-05-26 02:39:24"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:39:24"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:39:24"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:39:24"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:39:24"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 6ms","timestamp":"2025-05-26 02:39:24"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 7ms","timestamp":"2025-05-26 02:39:24"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-26 02:39:24","variables":{"limit":10,"page":1}}
{"level":"http","message":"GraphQL anonymous completed in 28ms","timestamp":"2025-05-26 02:39:24"}
{"level":"http","message":"POST / 200 - 34ms","timestamp":"2025-05-26 02:39:24"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetVoiceMessages {\n  getVoiceMessages {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    endTime\n    duration\n    conversationId\n    metadata\n  }\n}","timestamp":"2025-05-26 02:39:25","variables":{}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-26 02:39:25","variables":{"conversationId":"683245dbfe5136e30fe9e03a","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=683245dbfe5136e30fe9e03a, userId=682bb95fb9b407dd58126686","timestamp":"2025-05-26 02:39:25"}
{"level":"warn","message":"GraphQL anonymous completed with errors in 26ms","timestamp":"2025-05-26 02:39:25"}
{"level":"http","message":"POST / 200 - 32ms","timestamp":"2025-05-26 02:39:25"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 683245dbfe5136e30fe9e03a, unread: 0, messages: 3","timestamp":"2025-05-26 02:39:25"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=683245dbfe5136e30fe9e03a, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=682bb95fb9b407dd58126686, offset=0","timestamp":"2025-05-26 02:39:25"}
{"level":"info","message":"[MessageService] Retrieved 3 messages","timestamp":"2025-05-26 02:39:25"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-26 02:39:25"}
{"level":"http","message":"GraphQL anonymous completed in 107ms","timestamp":"2025-05-26 02:39:25"}
{"level":"http","message":"POST / 200 - 113ms","timestamp":"2025-05-26 02:39:25"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:39:25"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:39:25"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:39:25"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:39:25"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetVoiceMessages {\n  getVoiceMessages {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    endTime\n    duration\n    conversationId\n    metadata\n  }\n}","timestamp":"2025-05-26 02:39:25","variables":{}}
{"level":"warn","message":"GraphQL anonymous completed with errors in 8ms","timestamp":"2025-05-26 02:39:25"}
{"level":"http","message":"POST / 200 - 11ms","timestamp":"2025-05-26 02:39:25"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:39:25"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:39:25"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:39:25"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-26 02:39:25"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:39:26","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:39:26"}
{"level":"http","message":"POST / 200 - 8ms","timestamp":"2025-05-26 02:39:26"}
{"level":"info","message":"WebSocket connection authenticated for user 68294132b5d5c86fd2e56e23","timestamp":"2025-05-26 02:39:26"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:39:26"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:39:26"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:39:26"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:39:26"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-26 02:39:26","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 11ms","timestamp":"2025-05-26 02:39:26"}
{"level":"http","message":"GraphQL anonymous completed in 81ms","timestamp":"2025-05-26 02:39:26"}
{"level":"http","message":"POST / 200 - 88ms","timestamp":"2025-05-26 02:39:26"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetVoiceMessages {\n  getVoiceMessages {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    endTime\n    duration\n    conversationId\n    metadata\n  }\n}","timestamp":"2025-05-26 02:39:26","variables":{}}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:39:26"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:39:26"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:39:26"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:39:26"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-26 02:39:26","variables":{"conversationId":"6832451afe5136e30fe9dfaf","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6832451afe5136e30fe9dfaf, userId=68294132b5d5c86fd2e56e23","timestamp":"2025-05-26 02:39:26"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 19ms","timestamp":"2025-05-26 02:39:26"}
{"level":"http","message":"GraphQL anonymous completed in 51ms","timestamp":"2025-05-26 02:39:26"}
{"level":"http","message":"POST / 200 - 57ms","timestamp":"2025-05-26 02:39:26"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6832451afe5136e30fe9dfaf, unread: 0, messages: 6","timestamp":"2025-05-26 02:39:26"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6832451afe5136e30fe9dfaf, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=68294132b5d5c86fd2e56e23, offset=0","timestamp":"2025-05-26 02:39:26"}
{"level":"info","message":"[MessageService] Retrieved 6 messages","timestamp":"2025-05-26 02:39:26"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-26 02:39:26"}
{"level":"http","message":"GraphQL anonymous completed in 53ms","timestamp":"2025-05-26 02:39:26"}
{"level":"http","message":"POST / 200 - 59ms","timestamp":"2025-05-26 02:39:26"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:39:26"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:39:26"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:39:26"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-26 02:39:42"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-26 02:39:43"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:39:44","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:39:44"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-26 02:39:44"}
{"level":"info","message":"WebSocket connection authenticated for user 68294132b5d5c86fd2e56e23","timestamp":"2025-05-26 02:39:44"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:39:44"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:39:44"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:39:44"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:39:44"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-26 02:39:44","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 10ms","timestamp":"2025-05-26 02:39:44"}
{"level":"http","message":"GraphQL anonymous completed in 31ms","timestamp":"2025-05-26 02:39:44"}
{"level":"http","message":"POST / 200 - 37ms","timestamp":"2025-05-26 02:39:44"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetVoiceMessages {\n  getVoiceMessages {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    endTime\n    duration\n    conversationId\n    metadata\n  }\n}","timestamp":"2025-05-26 02:39:44","variables":{}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 14ms","timestamp":"2025-05-26 02:39:44"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-26 02:39:44","variables":{"conversationId":"6832451afe5136e30fe9dfaf","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6832451afe5136e30fe9dfaf, userId=68294132b5d5c86fd2e56e23","timestamp":"2025-05-26 02:39:44"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:39:44"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:39:44"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:39:44"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:39:44"}
{"level":"http","message":"GraphQL anonymous completed in 58ms","timestamp":"2025-05-26 02:39:44"}
{"level":"http","message":"POST / 200 - 62ms","timestamp":"2025-05-26 02:39:44"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6832451afe5136e30fe9dfaf, unread: 0, messages: 6","timestamp":"2025-05-26 02:39:44"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6832451afe5136e30fe9dfaf, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=68294132b5d5c86fd2e56e23, offset=0","timestamp":"2025-05-26 02:39:44"}
{"level":"info","message":"[MessageService] Retrieved 6 messages","timestamp":"2025-05-26 02:39:44"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-26 02:39:44"}
{"level":"http","message":"GraphQL anonymous completed in 69ms","timestamp":"2025-05-26 02:39:44"}
{"level":"http","message":"POST / 200 - 78ms","timestamp":"2025-05-26 02:39:44"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:39:44","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:39:44"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-26 02:39:44"}
{"level":"info","message":"WebSocket connection authenticated for user 682bb95fb9b407dd58126686","timestamp":"2025-05-26 02:39:44"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:39:44"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:39:44"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:39:44"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:39:44"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 5ms","timestamp":"2025-05-26 02:39:44"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 5ms","timestamp":"2025-05-26 02:39:44"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-26 02:39:44","variables":{"limit":10,"page":1}}
{"level":"http","message":"GraphQL anonymous completed in 24ms","timestamp":"2025-05-26 02:39:44"}
{"level":"http","message":"POST / 200 - 27ms","timestamp":"2025-05-26 02:39:44"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:39:44"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:39:44"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:39:44"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetVoiceMessages {\n  getVoiceMessages {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    endTime\n    duration\n    conversationId\n    metadata\n  }\n}","timestamp":"2025-05-26 02:39:44","variables":{}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-26 02:39:44","variables":{"conversationId":"683245dbfe5136e30fe9e03a","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=683245dbfe5136e30fe9e03a, userId=682bb95fb9b407dd58126686","timestamp":"2025-05-26 02:39:44"}
{"level":"warn","message":"GraphQL anonymous completed with errors in 17ms","timestamp":"2025-05-26 02:39:44"}
{"level":"http","message":"POST / 200 - 19ms","timestamp":"2025-05-26 02:39:44"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:39:44"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:39:44"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:39:44"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:39:44"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 683245dbfe5136e30fe9e03a, unread: 0, messages: 3","timestamp":"2025-05-26 02:39:44"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=683245dbfe5136e30fe9e03a, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=682bb95fb9b407dd58126686, offset=0","timestamp":"2025-05-26 02:39:44"}
{"level":"info","message":"[MessageService] Retrieved 3 messages","timestamp":"2025-05-26 02:39:44"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-26 02:39:44"}
{"level":"http","message":"GraphQL anonymous completed in 37ms","timestamp":"2025-05-26 02:39:44"}
{"level":"http","message":"POST / 200 - 40ms","timestamp":"2025-05-26 02:39:44"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetVoiceMessages {\n  getVoiceMessages {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    endTime\n    duration\n    conversationId\n    metadata\n  }\n}","timestamp":"2025-05-26 02:39:44","variables":{}}
{"level":"warn","message":"GraphQL anonymous completed with errors in 9ms","timestamp":"2025-05-26 02:39:44"}
{"level":"http","message":"POST / 200 - 11ms","timestamp":"2025-05-26 02:39:44"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:39:44"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:39:44"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:39:44"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:40:14","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:40:14"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-26 02:40:14"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:40:15","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:40:15"}
{"level":"http","message":"POST / 200 - 9ms","timestamp":"2025-05-26 02:40:15"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:40:44","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:40:44"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-26 02:40:44"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:40:45","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:40:45"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-26 02:40:45"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:41:14","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:41:14"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-26 02:41:14"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:41:15","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:41:15"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-26 02:41:15"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:41:44","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:41:44"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-26 02:41:44"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:41:45","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:41:45"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-26 02:41:45"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:42:14","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:42:14"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-26 02:42:14"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:42:15","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:42:15"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-26 02:42:15"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-26 02:42:36"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-26 02:42:36"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:42:40","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:42:40"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-26 02:42:40"}
{"level":"info","message":"WebSocket connection authenticated for user 68294132b5d5c86fd2e56e23","timestamp":"2025-05-26 02:42:40"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:42:40"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:42:40"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:42:40"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:42:40"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-26 02:42:40","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 12ms","timestamp":"2025-05-26 02:42:40"}
{"level":"http","message":"GraphQL anonymous completed in 33ms","timestamp":"2025-05-26 02:42:40"}
{"level":"http","message":"POST / 200 - 40ms","timestamp":"2025-05-26 02:42:40"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 12ms","timestamp":"2025-05-26 02:42:40"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetVoiceMessages {\n  getVoiceMessages {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    endTime\n    duration\n    conversationId\n    metadata\n  }\n}","timestamp":"2025-05-26 02:42:40","variables":{}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-26 02:42:40","variables":{"conversationId":"6832451afe5136e30fe9dfaf","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6832451afe5136e30fe9dfaf, userId=68294132b5d5c86fd2e56e23","timestamp":"2025-05-26 02:42:40"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:42:40"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:42:40"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:42:40"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:42:40"}
{"level":"http","message":"GraphQL anonymous completed in 44ms","timestamp":"2025-05-26 02:42:40"}
{"level":"http","message":"POST / 200 - 50ms","timestamp":"2025-05-26 02:42:40"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6832451afe5136e30fe9dfaf, unread: 0, messages: 6","timestamp":"2025-05-26 02:42:40"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6832451afe5136e30fe9dfaf, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=68294132b5d5c86fd2e56e23, offset=0","timestamp":"2025-05-26 02:42:40"}
{"level":"info","message":"[MessageService] Retrieved 6 messages","timestamp":"2025-05-26 02:42:40"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-26 02:42:40"}
{"level":"http","message":"GraphQL anonymous completed in 71ms","timestamp":"2025-05-26 02:42:40"}
{"level":"http","message":"POST / 200 - 80ms","timestamp":"2025-05-26 02:42:40"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:42:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:42:41"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-26 02:42:41"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:42:41"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:42:41"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:42:41"}
{"level":"info","message":"WebSocket connection authenticated for user 682bb95fb9b407dd58126686","timestamp":"2025-05-26 02:42:41"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:42:41"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:42:41"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:42:41"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:42:41"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 9ms","timestamp":"2025-05-26 02:42:41"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 6ms","timestamp":"2025-05-26 02:42:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-26 02:42:41","variables":{"limit":10,"page":1}}
{"level":"http","message":"GraphQL anonymous completed in 19ms","timestamp":"2025-05-26 02:42:41"}
{"level":"http","message":"POST / 200 - 23ms","timestamp":"2025-05-26 02:42:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetVoiceMessages {\n  getVoiceMessages {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    endTime\n    duration\n    conversationId\n    metadata\n  }\n}","timestamp":"2025-05-26 02:42:41","variables":{}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-26 02:42:41","variables":{"conversationId":"683245dbfe5136e30fe9e03a","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=683245dbfe5136e30fe9e03a, userId=682bb95fb9b407dd58126686","timestamp":"2025-05-26 02:42:41"}
{"level":"warn","message":"GraphQL anonymous completed with errors in 26ms","timestamp":"2025-05-26 02:42:41"}
{"level":"http","message":"POST / 200 - 28ms","timestamp":"2025-05-26 02:42:41"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:42:41"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:42:41"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:42:41"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:42:41"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 683245dbfe5136e30fe9e03a, unread: 0, messages: 3","timestamp":"2025-05-26 02:42:41"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=683245dbfe5136e30fe9e03a, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=682bb95fb9b407dd58126686, offset=0","timestamp":"2025-05-26 02:42:41"}
{"level":"info","message":"[MessageService] Retrieved 3 messages","timestamp":"2025-05-26 02:42:41"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-26 02:42:41"}
{"level":"http","message":"GraphQL anonymous completed in 66ms","timestamp":"2025-05-26 02:42:41"}
{"level":"http","message":"POST / 200 - 69ms","timestamp":"2025-05-26 02:42:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetVoiceMessages {\n  getVoiceMessages {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    endTime\n    duration\n    conversationId\n    metadata\n  }\n}","timestamp":"2025-05-26 02:42:41","variables":{}}
{"level":"warn","message":"GraphQL anonymous completed with errors in 10ms","timestamp":"2025-05-26 02:42:41"}
{"level":"http","message":"POST / 200 - 15ms","timestamp":"2025-05-26 02:42:41"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:42:41"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:42:41"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:42:41"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-26 02:43:04"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-26 02:43:06"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:43:06","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:43:06"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-26 02:43:06"}
{"level":"info","message":"WebSocket connection authenticated for user 68294132b5d5c86fd2e56e23","timestamp":"2025-05-26 02:43:06"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:43:06"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:43:06"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:43:06"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:43:06"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-26 02:43:06","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 10ms","timestamp":"2025-05-26 02:43:07"}
{"level":"http","message":"GraphQL anonymous completed in 53ms","timestamp":"2025-05-26 02:43:07"}
{"level":"http","message":"POST / 200 - 60ms","timestamp":"2025-05-26 02:43:07"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:43:07"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:43:07"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:43:07"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:43:07"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetVoiceMessages {\n  getVoiceMessages {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    endTime\n    duration\n    conversationId\n    metadata\n  }\n}","timestamp":"2025-05-26 02:43:07","variables":{}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-26 02:43:07","variables":{"conversationId":"6832451afe5136e30fe9dfaf","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6832451afe5136e30fe9dfaf, userId=68294132b5d5c86fd2e56e23","timestamp":"2025-05-26 02:43:07"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 175ms","timestamp":"2025-05-26 02:43:07"}
{"level":"http","message":"GraphQL anonymous completed in 87ms","timestamp":"2025-05-26 02:43:07"}
{"level":"http","message":"POST / 200 - 172ms","timestamp":"2025-05-26 02:43:07"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6832451afe5136e30fe9dfaf, unread: 0, messages: 6","timestamp":"2025-05-26 02:43:07"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6832451afe5136e30fe9dfaf, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=68294132b5d5c86fd2e56e23, offset=0","timestamp":"2025-05-26 02:43:07"}
{"level":"info","message":"[MessageService] Retrieved 6 messages","timestamp":"2025-05-26 02:43:07"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-26 02:43:07"}
{"level":"http","message":"GraphQL anonymous completed in 79ms","timestamp":"2025-05-26 02:43:07"}
{"level":"http","message":"POST / 200 - 90ms","timestamp":"2025-05-26 02:43:07"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:43:07"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:43:07"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:43:07"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:43:07","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:43:07"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-26 02:43:07"}
{"level":"info","message":"WebSocket connection authenticated for user 682bb95fb9b407dd58126686","timestamp":"2025-05-26 02:43:07"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:43:07"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:43:07"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:43:07"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:43:07"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 5ms","timestamp":"2025-05-26 02:43:07"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 4ms","timestamp":"2025-05-26 02:43:07"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-26 02:43:07","variables":{"limit":10,"page":1}}
{"level":"http","message":"GraphQL anonymous completed in 22ms","timestamp":"2025-05-26 02:43:07"}
{"level":"http","message":"POST / 200 - 27ms","timestamp":"2025-05-26 02:43:07"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetVoiceMessages {\n  getVoiceMessages {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    endTime\n    duration\n    conversationId\n    metadata\n  }\n}","timestamp":"2025-05-26 02:43:07","variables":{}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-26 02:43:07","variables":{"conversationId":"683245dbfe5136e30fe9e03a","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=683245dbfe5136e30fe9e03a, userId=682bb95fb9b407dd58126686","timestamp":"2025-05-26 02:43:07"}
{"level":"warn","message":"GraphQL anonymous completed with errors in 20ms","timestamp":"2025-05-26 02:43:07"}
{"level":"http","message":"POST / 200 - 24ms","timestamp":"2025-05-26 02:43:07"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 683245dbfe5136e30fe9e03a, unread: 0, messages: 3","timestamp":"2025-05-26 02:43:07"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=683245dbfe5136e30fe9e03a, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=682bb95fb9b407dd58126686, offset=0","timestamp":"2025-05-26 02:43:07"}
{"level":"info","message":"[MessageService] Retrieved 3 messages","timestamp":"2025-05-26 02:43:07"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-26 02:43:07"}
{"level":"http","message":"GraphQL anonymous completed in 65ms","timestamp":"2025-05-26 02:43:07"}
{"level":"http","message":"POST / 200 - 71ms","timestamp":"2025-05-26 02:43:07"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:43:07"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:43:07"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:43:07"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:43:07"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetVoiceMessages {\n  getVoiceMessages {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    endTime\n    duration\n    conversationId\n    metadata\n  }\n}","timestamp":"2025-05-26 02:43:08","variables":{}}
{"level":"warn","message":"GraphQL anonymous completed with errors in 10ms","timestamp":"2025-05-26 02:43:08"}
{"level":"http","message":"POST / 200 - 14ms","timestamp":"2025-05-26 02:43:08"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:43:08"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:43:08"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:43:08"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:43:37","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:43:37"}
{"level":"http","message":"POST / 200 - 25ms","timestamp":"2025-05-26 02:43:37"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:43:38","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:43:38"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-26 02:43:38"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-26 02:43:39"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-26 02:43:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:43:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:43:41"}
{"level":"http","message":"POST / 200 - 32ms","timestamp":"2025-05-26 02:43:41"}
{"level":"info","message":"WebSocket connection authenticated for user 68294132b5d5c86fd2e56e23","timestamp":"2025-05-26 02:43:41"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:43:41"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:43:41"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:43:41"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:43:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-26 02:43:41","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 10ms","timestamp":"2025-05-26 02:43:42"}
{"level":"http","message":"GraphQL anonymous completed in 38ms","timestamp":"2025-05-26 02:43:42"}
{"level":"http","message":"POST / 200 - 49ms","timestamp":"2025-05-26 02:43:42"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:43:42"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:43:42"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:43:42"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:43:42"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetVoiceMessages {\n  getVoiceMessages {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    endTime\n    duration\n    conversationId\n    metadata\n  }\n}","timestamp":"2025-05-26 02:43:42","variables":{}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-26 02:43:42","variables":{"conversationId":"6832451afe5136e30fe9dfaf","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6832451afe5136e30fe9dfaf, userId=68294132b5d5c86fd2e56e23","timestamp":"2025-05-26 02:43:42"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 62ms","timestamp":"2025-05-26 02:43:42"}
{"level":"http","message":"GraphQL anonymous completed in 49ms","timestamp":"2025-05-26 02:43:42"}
{"level":"http","message":"POST / 200 - 59ms","timestamp":"2025-05-26 02:43:42"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6832451afe5136e30fe9dfaf, unread: 0, messages: 6","timestamp":"2025-05-26 02:43:42"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6832451afe5136e30fe9dfaf, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=68294132b5d5c86fd2e56e23, offset=0","timestamp":"2025-05-26 02:43:42"}
{"level":"info","message":"[MessageService] Retrieved 6 messages","timestamp":"2025-05-26 02:43:42"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-26 02:43:42"}
{"level":"http","message":"GraphQL anonymous completed in 107ms","timestamp":"2025-05-26 02:43:42"}
{"level":"http","message":"POST / 200 - 118ms","timestamp":"2025-05-26 02:43:42"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:43:42"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:43:42"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:43:42"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:43:42","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:43:42"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-26 02:43:42"}
{"level":"info","message":"WebSocket connection authenticated for user 682bb95fb9b407dd58126686","timestamp":"2025-05-26 02:43:42"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:43:42"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:43:42"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:43:42"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:43:42"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 8ms","timestamp":"2025-05-26 02:43:42"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 8ms","timestamp":"2025-05-26 02:43:42"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-26 02:43:42","variables":{"limit":10,"page":1}}
{"level":"http","message":"GraphQL anonymous completed in 22ms","timestamp":"2025-05-26 02:43:42"}
{"level":"http","message":"POST / 200 - 28ms","timestamp":"2025-05-26 02:43:42"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetVoiceMessages {\n  getVoiceMessages {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    endTime\n    duration\n    conversationId\n    metadata\n  }\n}","timestamp":"2025-05-26 02:43:42","variables":{}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-26 02:43:42","variables":{"conversationId":"683245dbfe5136e30fe9e03a","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=683245dbfe5136e30fe9e03a, userId=682bb95fb9b407dd58126686","timestamp":"2025-05-26 02:43:42"}
{"level":"warn","message":"GraphQL anonymous completed with errors in 29ms","timestamp":"2025-05-26 02:43:42"}
{"level":"http","message":"POST / 200 - 38ms","timestamp":"2025-05-26 02:43:42"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 683245dbfe5136e30fe9e03a, unread: 0, messages: 3","timestamp":"2025-05-26 02:43:43"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=683245dbfe5136e30fe9e03a, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=682bb95fb9b407dd58126686, offset=0","timestamp":"2025-05-26 02:43:43"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:43:43"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:43:43"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:43:43"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:43:43"}
{"level":"info","message":"[MessageService] Retrieved 3 messages","timestamp":"2025-05-26 02:43:43"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-26 02:43:43"}
{"level":"http","message":"GraphQL anonymous completed in 122ms","timestamp":"2025-05-26 02:43:43"}
{"level":"http","message":"POST / 200 - 128ms","timestamp":"2025-05-26 02:43:43"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetVoiceMessages {\n  getVoiceMessages {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    endTime\n    duration\n    conversationId\n    metadata\n  }\n}","timestamp":"2025-05-26 02:43:43","variables":{}}
{"level":"warn","message":"GraphQL anonymous completed with errors in 9ms","timestamp":"2025-05-26 02:43:43"}
{"level":"http","message":"POST / 200 - 15ms","timestamp":"2025-05-26 02:43:43"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:43:43"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:43:43"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:43:43"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:44:13","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:44:13"}
{"level":"http","message":"POST / 200 - 8ms","timestamp":"2025-05-26 02:44:13"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:44:13","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:44:13"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-26 02:44:13"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-26 02:44:17"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-26 02:44:19"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:44:19","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:44:19"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-26 02:44:19"}
{"level":"info","message":"WebSocket connection authenticated for user 68294132b5d5c86fd2e56e23","timestamp":"2025-05-26 02:44:19"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:44:19"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:44:20"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:44:20"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:44:20"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-26 02:44:20","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 9ms","timestamp":"2025-05-26 02:44:20"}
{"level":"http","message":"GraphQL anonymous completed in 98ms","timestamp":"2025-05-26 02:44:20"}
{"level":"http","message":"POST / 200 - 132ms","timestamp":"2025-05-26 02:44:20"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:44:20"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:44:20"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:44:20"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:44:20"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetVoiceMessages {\n  getVoiceMessages {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    endTime\n    duration\n    conversationId\n    metadata\n  }\n}","timestamp":"2025-05-26 02:44:20","variables":{}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-26 02:44:20","variables":{"conversationId":"6832451afe5136e30fe9dfaf","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6832451afe5136e30fe9dfaf, userId=68294132b5d5c86fd2e56e23","timestamp":"2025-05-26 02:44:20"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 69ms","timestamp":"2025-05-26 02:44:20"}
{"level":"http","message":"GraphQL anonymous completed in 40ms","timestamp":"2025-05-26 02:44:20"}
{"level":"http","message":"POST / 200 - 45ms","timestamp":"2025-05-26 02:44:20"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6832451afe5136e30fe9dfaf, unread: 0, messages: 6","timestamp":"2025-05-26 02:44:20"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6832451afe5136e30fe9dfaf, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=68294132b5d5c86fd2e56e23, offset=0","timestamp":"2025-05-26 02:44:20"}
{"level":"info","message":"[MessageService] Retrieved 6 messages","timestamp":"2025-05-26 02:44:20"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-26 02:44:20"}
{"level":"http","message":"GraphQL anonymous completed in 86ms","timestamp":"2025-05-26 02:44:20"}
{"level":"http","message":"POST / 200 - 93ms","timestamp":"2025-05-26 02:44:20"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:44:20"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:44:20"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:44:20"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:44:20","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:44:20"}
{"level":"http","message":"POST / 200 - 11ms","timestamp":"2025-05-26 02:44:20"}
{"level":"info","message":"WebSocket connection authenticated for user 682bb95fb9b407dd58126686","timestamp":"2025-05-26 02:44:20"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:44:20"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:44:20"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:44:20"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:44:20"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 7ms","timestamp":"2025-05-26 02:44:20"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 5ms","timestamp":"2025-05-26 02:44:20"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-26 02:44:20","variables":{"limit":10,"page":1}}
{"level":"http","message":"GraphQL anonymous completed in 23ms","timestamp":"2025-05-26 02:44:20"}
{"level":"http","message":"POST / 200 - 28ms","timestamp":"2025-05-26 02:44:20"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetVoiceMessages {\n  getVoiceMessages {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    endTime\n    duration\n    conversationId\n    metadata\n  }\n}","timestamp":"2025-05-26 02:44:21","variables":{}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-26 02:44:21","variables":{"conversationId":"683245dbfe5136e30fe9e03a","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=683245dbfe5136e30fe9e03a, userId=682bb95fb9b407dd58126686","timestamp":"2025-05-26 02:44:21"}
{"level":"warn","message":"GraphQL anonymous completed with errors in 22ms","timestamp":"2025-05-26 02:44:21"}
{"level":"http","message":"POST / 200 - 27ms","timestamp":"2025-05-26 02:44:21"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 683245dbfe5136e30fe9e03a, unread: 0, messages: 3","timestamp":"2025-05-26 02:44:21"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=683245dbfe5136e30fe9e03a, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=682bb95fb9b407dd58126686, offset=0","timestamp":"2025-05-26 02:44:21"}
{"level":"info","message":"[MessageService] Retrieved 3 messages","timestamp":"2025-05-26 02:44:21"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-26 02:44:21"}
{"level":"http","message":"GraphQL anonymous completed in 90ms","timestamp":"2025-05-26 02:44:21"}
{"level":"http","message":"POST / 200 - 97ms","timestamp":"2025-05-26 02:44:21"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:44:21"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:44:21"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:44:21"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:44:21"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetVoiceMessages {\n  getVoiceMessages {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    endTime\n    duration\n    conversationId\n    metadata\n  }\n}","timestamp":"2025-05-26 02:44:21","variables":{}}
{"level":"warn","message":"GraphQL anonymous completed with errors in 14ms","timestamp":"2025-05-26 02:44:21"}
{"level":"http","message":"POST / 200 - 19ms","timestamp":"2025-05-26 02:44:21"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:44:21"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:44:21"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:44:21"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-26 02:44:49"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:44:50","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:44:50"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-26 02:44:50"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-26 02:44:51"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:44:51","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:44:51"}
{"level":"http","message":"POST / 200 - 17ms","timestamp":"2025-05-26 02:44:51"}
{"level":"info","message":"WebSocket connection authenticated for user 68294132b5d5c86fd2e56e23","timestamp":"2025-05-26 02:44:51"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:44:51"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:44:51"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:44:51"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:44:51"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 8ms","timestamp":"2025-05-26 02:44:51"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-26 02:44:51","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 16ms","timestamp":"2025-05-26 02:44:51"}
{"level":"http","message":"GraphQL anonymous completed in 32ms","timestamp":"2025-05-26 02:44:51"}
{"level":"http","message":"POST / 200 - 36ms","timestamp":"2025-05-26 02:44:51"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetVoiceMessages {\n  getVoiceMessages {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    endTime\n    duration\n    conversationId\n    metadata\n  }\n}","timestamp":"2025-05-26 02:44:51","variables":{}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-26 02:44:51","variables":{"conversationId":"6832451afe5136e30fe9dfaf","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6832451afe5136e30fe9dfaf, userId=68294132b5d5c86fd2e56e23","timestamp":"2025-05-26 02:44:51"}
{"level":"http","message":"GraphQL anonymous completed in 19ms","timestamp":"2025-05-26 02:44:51"}
{"level":"http","message":"POST / 200 - 26ms","timestamp":"2025-05-26 02:44:51"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:44:52"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:44:52"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:44:52"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:44:52"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:44:52","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:44:52"}
{"level":"http","message":"POST / 200 - 16ms","timestamp":"2025-05-26 02:44:52"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6832451afe5136e30fe9dfaf, unread: 0, messages: 6","timestamp":"2025-05-26 02:44:52"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6832451afe5136e30fe9dfaf, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=68294132b5d5c86fd2e56e23, offset=0","timestamp":"2025-05-26 02:44:52"}
{"level":"info","message":"[MessageService] Retrieved 6 messages","timestamp":"2025-05-26 02:44:52"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-26 02:44:52"}
{"level":"http","message":"GraphQL anonymous completed in 183ms","timestamp":"2025-05-26 02:44:52"}
{"level":"http","message":"POST / 200 - 190ms","timestamp":"2025-05-26 02:44:52"}
{"level":"info","message":"WebSocket connection authenticated for user 682bb95fb9b407dd58126686","timestamp":"2025-05-26 02:44:52"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:44:52"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:44:52"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:44:52"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:44:52"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 6ms","timestamp":"2025-05-26 02:44:52"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 6ms","timestamp":"2025-05-26 02:44:52"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-26 02:44:52","variables":{"limit":10,"page":1}}
{"level":"http","message":"GraphQL anonymous completed in 41ms","timestamp":"2025-05-26 02:44:52"}
{"level":"http","message":"POST / 200 - 49ms","timestamp":"2025-05-26 02:44:52"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetVoiceMessages {\n  getVoiceMessages {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    endTime\n    duration\n    conversationId\n    metadata\n  }\n}","timestamp":"2025-05-26 02:44:52","variables":{}}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:44:52"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:44:52"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:44:52"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-26 02:44:52","variables":{"conversationId":"683245dbfe5136e30fe9e03a","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=683245dbfe5136e30fe9e03a, userId=682bb95fb9b407dd58126686","timestamp":"2025-05-26 02:44:52"}
{"level":"warn","message":"GraphQL anonymous completed with errors in 33ms","timestamp":"2025-05-26 02:44:52"}
{"level":"http","message":"POST / 200 - 37ms","timestamp":"2025-05-26 02:44:52"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:44:52"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:44:52"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:44:52"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:44:52"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 683245dbfe5136e30fe9e03a, unread: 0, messages: 3","timestamp":"2025-05-26 02:44:52"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=683245dbfe5136e30fe9e03a, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=682bb95fb9b407dd58126686, offset=0","timestamp":"2025-05-26 02:44:52"}
{"level":"info","message":"[MessageService] Retrieved 3 messages","timestamp":"2025-05-26 02:44:52"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-26 02:44:52"}
{"level":"http","message":"GraphQL anonymous completed in 88ms","timestamp":"2025-05-26 02:44:52"}
{"level":"http","message":"POST / 200 - 96ms","timestamp":"2025-05-26 02:44:52"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetVoiceMessages {\n  getVoiceMessages {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    endTime\n    duration\n    conversationId\n    metadata\n  }\n}","timestamp":"2025-05-26 02:44:52","variables":{}}
{"level":"warn","message":"GraphQL anonymous completed with errors in 10ms","timestamp":"2025-05-26 02:44:52"}
{"level":"http","message":"POST / 200 - 14ms","timestamp":"2025-05-26 02:44:52"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:44:52"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:44:52"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:44:52"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-26 02:45:14"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-26 02:45:15"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:45:16","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:45:16"}
{"level":"http","message":"POST / 200 - 41ms","timestamp":"2025-05-26 02:45:16"}
{"level":"info","message":"WebSocket connection authenticated for user 68294132b5d5c86fd2e56e23","timestamp":"2025-05-26 02:45:16"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:45:16"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:45:16"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:45:16"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:45:16"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-26 02:45:16","variables":{"limit":10,"page":1}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetVoiceMessages {\n  getVoiceMessages {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    endTime\n    duration\n    conversationId\n    metadata\n  }\n}","timestamp":"2025-05-26 02:45:16","variables":{}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-26 02:45:16","variables":{"conversationId":"6832451afe5136e30fe9dfaf","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6832451afe5136e30fe9dfaf, userId=68294132b5d5c86fd2e56e23","timestamp":"2025-05-26 02:45:16"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:45:16"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:45:16"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:45:16"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:45:16"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 49ms","timestamp":"2025-05-26 02:45:16"}
{"level":"http","message":"GraphQL anonymous completed in 238ms","timestamp":"2025-05-26 02:45:16"}
{"level":"http","message":"POST / 200 - 255ms","timestamp":"2025-05-26 02:45:16"}
{"level":"http","message":"GraphQL anonymous completed in 240ms","timestamp":"2025-05-26 02:45:16"}
{"level":"http","message":"POST / 200 - 247ms","timestamp":"2025-05-26 02:45:16"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 13ms","timestamp":"2025-05-26 02:45:16"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6832451afe5136e30fe9dfaf, unread: 0, messages: 6","timestamp":"2025-05-26 02:45:16"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6832451afe5136e30fe9dfaf, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=68294132b5d5c86fd2e56e23, offset=0","timestamp":"2025-05-26 02:45:16"}
{"level":"info","message":"[MessageService] Retrieved 6 messages","timestamp":"2025-05-26 02:45:16"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-26 02:45:16"}
{"level":"http","message":"GraphQL anonymous completed in 307ms","timestamp":"2025-05-26 02:45:16"}
{"level":"http","message":"POST / 200 - 320ms","timestamp":"2025-05-26 02:45:16"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:45:16","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:45:16"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-26 02:45:16"}
{"level":"info","message":"WebSocket connection authenticated for user 682bb95fb9b407dd58126686","timestamp":"2025-05-26 02:45:16"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:45:16"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:45:16"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:45:16"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:45:16"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:45:16"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:45:16"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:45:16"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 11ms","timestamp":"2025-05-26 02:45:16"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 7ms","timestamp":"2025-05-26 02:45:16"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-26 02:45:16","variables":{"limit":10,"page":1}}
{"level":"http","message":"GraphQL anonymous completed in 29ms","timestamp":"2025-05-26 02:45:17"}
{"level":"http","message":"POST / 200 - 36ms","timestamp":"2025-05-26 02:45:17"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetVoiceMessages {\n  getVoiceMessages {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    endTime\n    duration\n    conversationId\n    metadata\n  }\n}","timestamp":"2025-05-26 02:45:17","variables":{}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-26 02:45:17","variables":{"conversationId":"683245dbfe5136e30fe9e03a","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=683245dbfe5136e30fe9e03a, userId=682bb95fb9b407dd58126686","timestamp":"2025-05-26 02:45:17"}
{"level":"warn","message":"GraphQL anonymous completed with errors in 33ms","timestamp":"2025-05-26 02:45:17"}
{"level":"http","message":"POST / 200 - 42ms","timestamp":"2025-05-26 02:45:17"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:45:17"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:45:17"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:45:17"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:45:17"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 683245dbfe5136e30fe9e03a, unread: 0, messages: 3","timestamp":"2025-05-26 02:45:17"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=683245dbfe5136e30fe9e03a, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=682bb95fb9b407dd58126686, offset=0","timestamp":"2025-05-26 02:45:17"}
{"level":"info","message":"[MessageService] Retrieved 3 messages","timestamp":"2025-05-26 02:45:17"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-26 02:45:17"}
{"level":"http","message":"GraphQL anonymous completed in 118ms","timestamp":"2025-05-26 02:45:17"}
{"level":"http","message":"POST / 200 - 125ms","timestamp":"2025-05-26 02:45:17"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetVoiceMessages {\n  getVoiceMessages {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    endTime\n    duration\n    conversationId\n    metadata\n  }\n}","timestamp":"2025-05-26 02:45:17","variables":{}}
{"level":"warn","message":"GraphQL anonymous completed with errors in 15ms","timestamp":"2025-05-26 02:45:17"}
{"level":"http","message":"POST / 200 - 20ms","timestamp":"2025-05-26 02:45:17"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:45:17"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:45:17"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:45:17"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-26 02:45:40"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:45:42","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:45:42"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-26 02:45:42"}
{"level":"info","message":"WebSocket connection authenticated for user 682bb95fb9b407dd58126686","timestamp":"2025-05-26 02:45:42"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:45:42"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:45:42"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:45:42"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:45:42"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 5ms","timestamp":"2025-05-26 02:45:42"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 4ms","timestamp":"2025-05-26 02:45:42"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-26 02:45:42","variables":{"limit":10,"page":1}}
{"level":"http","message":"GraphQL anonymous completed in 26ms","timestamp":"2025-05-26 02:45:42"}
{"level":"http","message":"POST / 200 - 32ms","timestamp":"2025-05-26 02:45:42"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetVoiceMessages {\n  getVoiceMessages {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    endTime\n    duration\n    conversationId\n    metadata\n  }\n}","timestamp":"2025-05-26 02:45:42","variables":{}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-26 02:45:42","variables":{"conversationId":"683245dbfe5136e30fe9e03a","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=683245dbfe5136e30fe9e03a, userId=682bb95fb9b407dd58126686","timestamp":"2025-05-26 02:45:42"}
{"level":"warn","message":"GraphQL anonymous completed with errors in 23ms","timestamp":"2025-05-26 02:45:42"}
{"level":"http","message":"POST / 200 - 29ms","timestamp":"2025-05-26 02:45:42"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:45:42"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:45:42"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:45:42"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:45:42"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 683245dbfe5136e30fe9e03a, unread: 0, messages: 3","timestamp":"2025-05-26 02:45:42"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=683245dbfe5136e30fe9e03a, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=682bb95fb9b407dd58126686, offset=0","timestamp":"2025-05-26 02:45:42"}
{"level":"info","message":"[MessageService] Retrieved 3 messages","timestamp":"2025-05-26 02:45:42"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-26 02:45:42"}
{"level":"http","message":"GraphQL anonymous completed in 118ms","timestamp":"2025-05-26 02:45:42"}
{"level":"http","message":"POST / 200 - 124ms","timestamp":"2025-05-26 02:45:42"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-26 02:45:42"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetVoiceMessages {\n  getVoiceMessages {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    endTime\n    duration\n    conversationId\n    metadata\n  }\n}","timestamp":"2025-05-26 02:45:43","variables":{}}
{"level":"warn","message":"GraphQL anonymous completed with errors in 48ms","timestamp":"2025-05-26 02:45:43"}
{"level":"http","message":"POST / 200 - 52ms","timestamp":"2025-05-26 02:45:43"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:45:43","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:45:43"}
{"level":"http","message":"POST / 200 - 23ms","timestamp":"2025-05-26 02:45:43"}
{"level":"info","message":"WebSocket connection authenticated for user 68294132b5d5c86fd2e56e23","timestamp":"2025-05-26 02:45:43"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:45:43"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:45:43"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:45:43"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:45:43"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-26 02:45:43","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 18ms","timestamp":"2025-05-26 02:45:43"}
{"level":"http","message":"GraphQL anonymous completed in 61ms","timestamp":"2025-05-26 02:45:43"}
{"level":"http","message":"POST / 200 - 66ms","timestamp":"2025-05-26 02:45:43"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 36ms","timestamp":"2025-05-26 02:45:43"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetVoiceMessages {\n  getVoiceMessages {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    endTime\n    duration\n    conversationId\n    metadata\n  }\n}","timestamp":"2025-05-26 02:45:43","variables":{}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-26 02:45:43","variables":{"conversationId":"6832451afe5136e30fe9dfaf","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6832451afe5136e30fe9dfaf, userId=68294132b5d5c86fd2e56e23","timestamp":"2025-05-26 02:45:43"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:45:43"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:45:43"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:45:43"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:45:43"}
{"level":"http","message":"GraphQL anonymous completed in 75ms","timestamp":"2025-05-26 02:45:43"}
{"level":"http","message":"POST / 200 - 88ms","timestamp":"2025-05-26 02:45:43"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6832451afe5136e30fe9dfaf, unread: 0, messages: 6","timestamp":"2025-05-26 02:45:43"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6832451afe5136e30fe9dfaf, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=68294132b5d5c86fd2e56e23, offset=0","timestamp":"2025-05-26 02:45:43"}
{"level":"info","message":"[MessageService] Retrieved 6 messages","timestamp":"2025-05-26 02:45:43"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-26 02:45:43"}
{"level":"http","message":"GraphQL anonymous completed in 101ms","timestamp":"2025-05-26 02:45:43"}
{"level":"http","message":"POST / 200 - 110ms","timestamp":"2025-05-26 02:45:43"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:45:43"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:45:43"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:45:43"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:45:44"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:45:44"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:45:44"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-26 02:46:07"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-26 02:46:09"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:46:09","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:46:09"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-26 02:46:09"}
{"level":"info","message":"WebSocket connection authenticated for user 68294132b5d5c86fd2e56e23","timestamp":"2025-05-26 02:46:09"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:46:09"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:46:09"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:46:09"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:46:09"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-26 02:46:09","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 13ms","timestamp":"2025-05-26 02:46:09"}
{"level":"http","message":"GraphQL anonymous completed in 45ms","timestamp":"2025-05-26 02:46:09"}
{"level":"http","message":"POST / 200 - 50ms","timestamp":"2025-05-26 02:46:09"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-26 02:46:09","variables":{"conversationId":"6832451afe5136e30fe9dfaf","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6832451afe5136e30fe9dfaf, userId=68294132b5d5c86fd2e56e23","timestamp":"2025-05-26 02:46:09"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetVoiceMessages {\n  getVoiceMessages {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    endTime\n    duration\n    conversationId\n    metadata\n  }\n}","timestamp":"2025-05-26 02:46:09","variables":{}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 26ms","timestamp":"2025-05-26 02:46:09"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:46:10"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:46:10"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:46:10"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:46:10"}
{"level":"http","message":"GraphQL anonymous completed in 50ms","timestamp":"2025-05-26 02:46:10"}
{"level":"http","message":"POST / 200 - 64ms","timestamp":"2025-05-26 02:46:10"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6832451afe5136e30fe9dfaf, unread: 0, messages: 6","timestamp":"2025-05-26 02:46:10"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6832451afe5136e30fe9dfaf, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=68294132b5d5c86fd2e56e23, offset=0","timestamp":"2025-05-26 02:46:10"}
{"level":"info","message":"[MessageService] Retrieved 6 messages","timestamp":"2025-05-26 02:46:10"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-26 02:46:10"}
{"level":"http","message":"GraphQL anonymous completed in 105ms","timestamp":"2025-05-26 02:46:10"}
{"level":"http","message":"POST / 200 - 110ms","timestamp":"2025-05-26 02:46:10"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:46:10"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:46:10"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:46:10"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:46:10","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:46:10"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-26 02:46:10"}
{"level":"info","message":"WebSocket connection authenticated for user 682bb95fb9b407dd58126686","timestamp":"2025-05-26 02:46:10"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:46:10"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:46:10"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:46:10"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:46:10"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 6ms","timestamp":"2025-05-26 02:46:10"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 7ms","timestamp":"2025-05-26 02:46:10"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-26 02:46:10","variables":{"limit":10,"page":1}}
{"level":"http","message":"GraphQL anonymous completed in 22ms","timestamp":"2025-05-26 02:46:10"}
{"level":"http","message":"POST / 200 - 26ms","timestamp":"2025-05-26 02:46:10"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetVoiceMessages {\n  getVoiceMessages {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    endTime\n    duration\n    conversationId\n    metadata\n  }\n}","timestamp":"2025-05-26 02:46:10","variables":{}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-26 02:46:10","variables":{"conversationId":"683245dbfe5136e30fe9e03a","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=683245dbfe5136e30fe9e03a, userId=682bb95fb9b407dd58126686","timestamp":"2025-05-26 02:46:10"}
{"level":"warn","message":"GraphQL anonymous completed with errors in 14ms","timestamp":"2025-05-26 02:46:10"}
{"level":"http","message":"POST / 200 - 18ms","timestamp":"2025-05-26 02:46:10"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 683245dbfe5136e30fe9e03a, unread: 0, messages: 3","timestamp":"2025-05-26 02:46:10"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=683245dbfe5136e30fe9e03a, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=682bb95fb9b407dd58126686, offset=0","timestamp":"2025-05-26 02:46:10"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:46:10"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:46:10"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:46:10"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:46:10"}
{"level":"info","message":"[MessageService] Retrieved 3 messages","timestamp":"2025-05-26 02:46:10"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-26 02:46:10"}
{"level":"http","message":"GraphQL anonymous completed in 81ms","timestamp":"2025-05-26 02:46:10"}
{"level":"http","message":"POST / 200 - 84ms","timestamp":"2025-05-26 02:46:10"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetVoiceMessages {\n  getVoiceMessages {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    endTime\n    duration\n    conversationId\n    metadata\n  }\n}","timestamp":"2025-05-26 02:46:11","variables":{}}
{"level":"warn","message":"GraphQL anonymous completed with errors in 12ms","timestamp":"2025-05-26 02:46:11"}
{"level":"http","message":"POST / 200 - 16ms","timestamp":"2025-05-26 02:46:11"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:46:11"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:46:11"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:46:11"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:46:40","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:46:40"}
{"level":"http","message":"POST / 200 - 35ms","timestamp":"2025-05-26 02:46:40"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:46:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:46:41"}
{"level":"http","message":"POST / 200 - 74ms","timestamp":"2025-05-26 02:46:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:47:09","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:47:09"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-26 02:47:09"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:47:11","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:47:11"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-26 02:47:11"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:47:40","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:47:40"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-26 02:47:40"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:47:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:47:41"}
{"level":"http","message":"POST / 200 - 16ms","timestamp":"2025-05-26 02:47:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:48:09","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:48:09"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-26 02:48:09"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:48:11","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:48:11"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-26 02:48:11"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:48:40","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:48:40"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-26 02:48:40"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:48:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:48:41"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-26 02:48:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:49:09","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:49:09"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-26 02:49:09"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:49:11","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:49:11"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-26 02:49:11"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:49:40","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:49:40"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-26 02:49:40"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:49:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:49:41"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-26 02:49:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:50:10","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 3ms","timestamp":"2025-05-26 02:50:10"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-26 02:50:10"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:50:11","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:50:11"}
{"level":"http","message":"POST / 200 - 9ms","timestamp":"2025-05-26 02:50:11"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:50:40","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:50:40"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-26 02:50:40"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:50:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:50:41"}
{"level":"http","message":"POST / 200 - 10ms","timestamp":"2025-05-26 02:50:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:51:10","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:51:10"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-26 02:51:10"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:51:11","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:51:11"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-26 02:51:11"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:51:39","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:51:39"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-26 02:51:39"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:51:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:51:41"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-26 02:51:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:52:11","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:52:11"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-26 02:52:11"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:52:11","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:52:11"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-26 02:52:11"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:52:40","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:52:40"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-26 02:52:40"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:52:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:52:41"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-26 02:52:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:53:09","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:53:09"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-26 02:53:09"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:53:11","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:53:11"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-26 02:53:11"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:53:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:53:41"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-26 02:53:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:53:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:53:41"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-26 02:53:41"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:54:10","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:54:10"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-26 02:54:10"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:54:11","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:54:11"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-26 02:54:11"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:54:40","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:54:40"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-26 02:54:40"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:54:41","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:54:41"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-26 02:54:41"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-26 02:54:51"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:54:53","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:54:53"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-26 02:54:53"}
{"level":"info","message":"WebSocket connection authenticated for user 682bb95fb9b407dd58126686","timestamp":"2025-05-26 02:54:53"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:54:53"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:54:53"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:54:53"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:54:53"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 5ms","timestamp":"2025-05-26 02:54:53"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 6ms","timestamp":"2025-05-26 02:54:53"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-26 02:54:53","variables":{"limit":10,"page":1}}
{"level":"http","message":"GraphQL anonymous completed in 19ms","timestamp":"2025-05-26 02:54:53"}
{"level":"http","message":"POST / 200 - 21ms","timestamp":"2025-05-26 02:54:53"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetVoiceMessages {\n  getVoiceMessages {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    endTime\n    duration\n    conversationId\n    metadata\n  }\n}","timestamp":"2025-05-26 02:54:53","variables":{}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-26 02:54:53","variables":{"conversationId":"683245dbfe5136e30fe9e03a","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=683245dbfe5136e30fe9e03a, userId=682bb95fb9b407dd58126686","timestamp":"2025-05-26 02:54:53"}
{"level":"warn","message":"GraphQL anonymous completed with errors in 16ms","timestamp":"2025-05-26 02:54:53"}
{"level":"http","message":"POST / 200 - 20ms","timestamp":"2025-05-26 02:54:53"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:54:53"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:54:53"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:54:53"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:54:53"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 683245dbfe5136e30fe9e03a, unread: 0, messages: 3","timestamp":"2025-05-26 02:54:53"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=683245dbfe5136e30fe9e03a, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=682bb95fb9b407dd58126686, offset=0","timestamp":"2025-05-26 02:54:53"}
{"level":"info","message":"[MessageService] Retrieved 3 messages","timestamp":"2025-05-26 02:54:53"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-26 02:54:53"}
{"level":"http","message":"GraphQL anonymous completed in 53ms","timestamp":"2025-05-26 02:54:53"}
{"level":"http","message":"POST / 200 - 57ms","timestamp":"2025-05-26 02:54:53"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetVoiceMessages {\n  getVoiceMessages {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    endTime\n    duration\n    conversationId\n    metadata\n  }\n}","timestamp":"2025-05-26 02:54:54","variables":{}}
{"level":"warn","message":"GraphQL anonymous completed with errors in 7ms","timestamp":"2025-05-26 02:54:54"}
{"level":"http","message":"POST / 200 - 11ms","timestamp":"2025-05-26 02:54:54"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:54:54"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:54:54"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:54:54"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-26 02:54:55"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:54:55","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:54:55"}
{"level":"http","message":"POST / 200 - 17ms","timestamp":"2025-05-26 02:54:55"}
{"level":"info","message":"WebSocket connection authenticated for user 68294132b5d5c86fd2e56e23","timestamp":"2025-05-26 02:54:55"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:54:55"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:54:55"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:54:55"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:54:55"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 7ms","timestamp":"2025-05-26 02:54:55"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-26 02:54:55","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 11ms","timestamp":"2025-05-26 02:54:55"}
{"level":"http","message":"GraphQL anonymous completed in 22ms","timestamp":"2025-05-26 02:54:55"}
{"level":"http","message":"POST / 200 - 26ms","timestamp":"2025-05-26 02:54:55"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetVoiceMessages {\n  getVoiceMessages {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    endTime\n    duration\n    conversationId\n    metadata\n  }\n}","timestamp":"2025-05-26 02:54:55","variables":{}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-26 02:54:55","variables":{"conversationId":"6832451afe5136e30fe9dfaf","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6832451afe5136e30fe9dfaf, userId=68294132b5d5c86fd2e56e23","timestamp":"2025-05-26 02:54:55"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:54:55"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:54:55"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:54:55"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:54:55"}
{"level":"http","message":"GraphQL anonymous completed in 31ms","timestamp":"2025-05-26 02:54:55"}
{"level":"http","message":"POST / 200 - 36ms","timestamp":"2025-05-26 02:54:55"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6832451afe5136e30fe9dfaf, unread: 0, messages: 6","timestamp":"2025-05-26 02:54:55"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6832451afe5136e30fe9dfaf, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=68294132b5d5c86fd2e56e23, offset=0","timestamp":"2025-05-26 02:54:55"}
{"level":"info","message":"[MessageService] Retrieved 6 messages","timestamp":"2025-05-26 02:54:55"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-26 02:54:55"}
{"level":"http","message":"GraphQL anonymous completed in 48ms","timestamp":"2025-05-26 02:54:55"}
{"level":"http","message":"POST / 200 - 52ms","timestamp":"2025-05-26 02:54:55"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:54:56"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:54:56"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:54:56"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-26 02:55:13"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-26 02:55:14"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:55:14","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:55:14"}
{"level":"http","message":"POST / 200 - 8ms","timestamp":"2025-05-26 02:55:14"}
{"level":"info","message":"WebSocket connection authenticated for user 68294132b5d5c86fd2e56e23","timestamp":"2025-05-26 02:55:14"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:55:14"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:55:14"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:55:14"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:55:14"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-26 02:55:14","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 8ms","timestamp":"2025-05-26 02:55:14"}
{"level":"http","message":"GraphQL anonymous completed in 42ms","timestamp":"2025-05-26 02:55:14"}
{"level":"http","message":"POST / 200 - 52ms","timestamp":"2025-05-26 02:55:14"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:55:14"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:55:14"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:55:14"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:55:14"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetVoiceMessages {\n  getVoiceMessages {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    endTime\n    duration\n    conversationId\n    metadata\n  }\n}","timestamp":"2025-05-26 02:55:14","variables":{}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-26 02:55:14","variables":{"conversationId":"6832451afe5136e30fe9dfaf","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6832451afe5136e30fe9dfaf, userId=68294132b5d5c86fd2e56e23","timestamp":"2025-05-26 02:55:14"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 45ms","timestamp":"2025-05-26 02:55:14"}
{"level":"http","message":"GraphQL anonymous completed in 50ms","timestamp":"2025-05-26 02:55:14"}
{"level":"http","message":"POST / 200 - 63ms","timestamp":"2025-05-26 02:55:14"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6832451afe5136e30fe9dfaf, unread: 0, messages: 6","timestamp":"2025-05-26 02:55:14"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6832451afe5136e30fe9dfaf, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=68294132b5d5c86fd2e56e23, offset=0","timestamp":"2025-05-26 02:55:14"}
{"level":"info","message":"[MessageService] Retrieved 6 messages","timestamp":"2025-05-26 02:55:14"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-26 02:55:14"}
{"level":"http","message":"GraphQL anonymous completed in 83ms","timestamp":"2025-05-26 02:55:14"}
{"level":"http","message":"POST / 200 - 91ms","timestamp":"2025-05-26 02:55:14"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:55:15"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:55:15"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:55:15"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:55:15","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:55:15"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-26 02:55:15"}
{"level":"info","message":"WebSocket connection authenticated for user 682bb95fb9b407dd58126686","timestamp":"2025-05-26 02:55:15"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:55:15"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:55:15"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:55:15"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:55:15"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 6ms","timestamp":"2025-05-26 02:55:15"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 7ms","timestamp":"2025-05-26 02:55:15"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-26 02:55:15","variables":{"limit":10,"page":1}}
{"level":"http","message":"GraphQL anonymous completed in 24ms","timestamp":"2025-05-26 02:55:15"}
{"level":"http","message":"POST / 200 - 30ms","timestamp":"2025-05-26 02:55:15"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetVoiceMessages {\n  getVoiceMessages {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    endTime\n    duration\n    conversationId\n    metadata\n  }\n}","timestamp":"2025-05-26 02:55:15","variables":{}}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-26 02:55:15","variables":{"conversationId":"683245dbfe5136e30fe9e03a","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=683245dbfe5136e30fe9e03a, userId=682bb95fb9b407dd58126686","timestamp":"2025-05-26 02:55:15"}
{"level":"warn","message":"GraphQL anonymous completed with errors in 17ms","timestamp":"2025-05-26 02:55:15"}
{"level":"http","message":"POST / 200 - 20ms","timestamp":"2025-05-26 02:55:15"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:55:15"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:55:15"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:55:15"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:55:15"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 683245dbfe5136e30fe9e03a, unread: 0, messages: 3","timestamp":"2025-05-26 02:55:15"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=683245dbfe5136e30fe9e03a, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=682bb95fb9b407dd58126686, offset=0","timestamp":"2025-05-26 02:55:15"}
{"level":"info","message":"[MessageService] Retrieved 3 messages","timestamp":"2025-05-26 02:55:15"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-26 02:55:15"}
{"level":"http","message":"GraphQL anonymous completed in 62ms","timestamp":"2025-05-26 02:55:15"}
{"level":"http","message":"POST / 200 - 66ms","timestamp":"2025-05-26 02:55:15"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetVoiceMessages {\n  getVoiceMessages {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    endTime\n    duration\n    conversationId\n    metadata\n  }\n}","timestamp":"2025-05-26 02:55:15","variables":{}}
{"level":"warn","message":"GraphQL anonymous completed with errors in 15ms","timestamp":"2025-05-26 02:55:15"}
{"level":"http","message":"POST / 200 - 18ms","timestamp":"2025-05-26 02:55:15"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:55:16"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:55:16"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:55:16"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:55:44","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:55:44"}
{"level":"http","message":"POST / 200 - 2ms","timestamp":"2025-05-26 02:55:44"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:55:46","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:55:46"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-26 02:55:46"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:56:14","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:56:14"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-26 02:56:14"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:56:16","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:56:16"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-26 02:56:16"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:56:44","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:56:44"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-26 02:56:44"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:56:46","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:56:46"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-26 02:56:46"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-26 02:56:59"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:57:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:57:01"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-26 02:57:01"}
{"level":"info","message":"WebSocket connection authenticated for user 682bb95fb9b407dd58126686","timestamp":"2025-05-26 02:57:01"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:57:01"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:57:01"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:57:01"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:57:01"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 8ms","timestamp":"2025-05-26 02:57:01"}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 6ms","timestamp":"2025-05-26 02:57:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-26 02:57:01","variables":{"limit":10,"page":1}}
{"level":"http","message":"GraphQL anonymous completed in 18ms","timestamp":"2025-05-26 02:57:01"}
{"level":"http","message":"POST / 200 - 22ms","timestamp":"2025-05-26 02:57:01"}
{"level":"info","message":"Client disconnected (1001): No reason provided","timestamp":"2025-05-26 02:57:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetVoiceMessages {\n  getVoiceMessages {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    endTime\n    duration\n    conversationId\n    metadata\n  }\n}","timestamp":"2025-05-26 02:57:01","variables":{}}
{"level":"warn","message":"GraphQL anonymous completed with errors in 41ms","timestamp":"2025-05-26 02:57:01"}
{"level":"http","message":"POST / 200 - 58ms","timestamp":"2025-05-26 02:57:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-26 02:57:01","variables":{"conversationId":"683245dbfe5136e30fe9e03a","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=683245dbfe5136e30fe9e03a, userId=682bb95fb9b407dd58126686","timestamp":"2025-05-26 02:57:01"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 683245dbfe5136e30fe9e03a, unread: 0, messages: 3","timestamp":"2025-05-26 02:57:01"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=683245dbfe5136e30fe9e03a, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=682bb95fb9b407dd58126686, offset=0","timestamp":"2025-05-26 02:57:01"}
{"level":"info","message":"[MessageService] Retrieved 3 messages","timestamp":"2025-05-26 02:57:01"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-26 02:57:01"}
{"level":"http","message":"GraphQL anonymous completed in 68ms","timestamp":"2025-05-26 02:57:01"}
{"level":"http","message":"POST / 200 - 74ms","timestamp":"2025-05-26 02:57:01"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:57:01"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:57:01"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:57:01"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:57:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:57:01","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:57:01"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-26 02:57:01"}
{"level":"info","message":"WebSocket connection authenticated for user 68294132b5d5c86fd2e56e23","timestamp":"2025-05-26 02:57:01"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:57:01"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:57:01"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:57:01"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:57:01"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetUserNotifications {\n  getUserNotifications {\n    id\n    type\n    content\n    timestamp\n    isRead\n    senderId {\n      id\n      username\n      image\n    }\n    message {\n      id\n      content\n    }\n    readAt\n    relatedEntity\n    metadata\n  }\n}","timestamp":"2025-05-26 02:57:02","variables":{"limit":10,"page":1}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 8ms","timestamp":"2025-05-26 02:57:02"}
{"level":"http","message":"GraphQL anonymous completed in 22ms","timestamp":"2025-05-26 02:57:02"}
{"level":"http","message":"POST / 200 - 27ms","timestamp":"2025-05-26 02:57:02"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetVoiceMessages {\n  getVoiceMessages {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    endTime\n    duration\n    conversationId\n    metadata\n  }\n}","timestamp":"2025-05-26 02:57:02","variables":{}}
{"level":"http","message":"GET /profile?secret=2cinfo1&client=esprit 304 - 10ms","timestamp":"2025-05-26 02:57:02"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetConversation($conversationId: ID!, $limit: Int = 10, $offset: Int = 0) {\n  getConversation(conversationId: $conversationId) {\n    id\n    participants {\n      id\n      username\n      image\n      isOnline\n    }\n    messages(limit: $limit, offset: $offset) {\n      id\n      content\n      type\n      timestamp\n      isRead\n      sender {\n        id\n        username\n        image\n      }\n      receiver {\n        id\n        username\n        image\n      }\n      attachments {\n        url\n        type\n        duration\n      }\n      metadata\n      conversationId\n    }\n  }\n}","timestamp":"2025-05-26 02:57:02","variables":{"conversationId":"6832451afe5136e30fe9dfaf","limit":10,"offset":0}}
{"level":"info","message":"[MessageService] Getting conversation: conversationId=6832451afe5136e30fe9dfaf, userId=68294132b5d5c86fd2e56e23","timestamp":"2025-05-26 02:57:02"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query GetVoiceMessages {\n  getVoiceMessages {\n    id\n    caller {\n      id\n      username\n      image\n    }\n    recipient {\n      id\n      username\n      image\n    }\n    type\n    status\n    startTime\n    endTime\n    duration\n    conversationId\n    metadata\n  }\n}","timestamp":"2025-05-26 02:57:02","variables":{}}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:57:02"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:57:02"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:57:02"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:57:02"}
{"level":"http","message":"GraphQL anonymous completed in 33ms","timestamp":"2025-05-26 02:57:02"}
{"level":"http","message":"POST / 200 - 37ms","timestamp":"2025-05-26 02:57:02"}
{"level":"warn","message":"GraphQL anonymous completed with errors in 25ms","timestamp":"2025-05-26 02:57:02"}
{"level":"http","message":"POST / 200 - 28ms","timestamp":"2025-05-26 02:57:02"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:57:02"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:57:02"}
{"level":"info","message":"WebSocket operation for user 682bb95fb9b407dd58126686, operation: subscribe","timestamp":"2025-05-26 02:57:02"}
{"level":"info","message":"[MessageService] Conversation retrieval completed: 6832451afe5136e30fe9dfaf, unread: 0, messages: 6","timestamp":"2025-05-26 02:57:02"}
{"level":"info","message":"[MessageService] Getting messages: conversationId=6832451afe5136e30fe9dfaf, senderId=undefined, receiverId=undefined, page=1, limit=10, userId=68294132b5d5c86fd2e56e23, offset=0","timestamp":"2025-05-26 02:57:02"}
{"level":"info","message":"[MessageService] Retrieved 6 messages","timestamp":"2025-05-26 02:57:02"}
{"level":"info","message":"[MessageService] Messages retrieval completed successfully","timestamp":"2025-05-26 02:57:02"}
{"level":"http","message":"GraphQL anonymous completed in 61ms","timestamp":"2025-05-26 02:57:02"}
{"level":"http","message":"POST / 200 - 64ms","timestamp":"2025-05-26 02:57:02"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:57:02"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:57:02"}
{"level":"info","message":"WebSocket operation for user 68294132b5d5c86fd2e56e23, operation: subscribe","timestamp":"2025-05-26 02:57:02"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:57:32","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:57:32"}
{"level":"http","message":"POST / 200 - 9ms","timestamp":"2025-05-26 02:57:32"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:57:32","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:57:32"}
{"level":"http","message":"POST / 200 - 29ms","timestamp":"2025-05-26 02:57:32"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:58:02","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:58:02"}
{"level":"http","message":"POST / 200 - 20ms","timestamp":"2025-05-26 02:58:02"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:58:02","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:58:02"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-26 02:58:02"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:58:32","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:58:32"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-26 02:58:32"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:58:32","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 1ms","timestamp":"2025-05-26 02:58:32"}
{"level":"http","message":"POST / 200 - 3ms","timestamp":"2025-05-26 02:58:32"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:59:02","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:59:02"}
{"level":"http","message":"POST / 200 - 4ms","timestamp":"2025-05-26 02:59:02"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:59:02","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:59:02"}
{"level":"http","message":"POST / 200 - 7ms","timestamp":"2025-05-26 02:59:02"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:59:32","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:59:32"}
{"level":"http","message":"POST / 200 - 6ms","timestamp":"2025-05-26 02:59:32"}
{"level":"http","message":"GraphQL anonymous operation started","query":"query Ping {\n  __typename\n}","timestamp":"2025-05-26 02:59:32","variables":{}}
{"level":"http","message":"GraphQL anonymous completed in 0ms","timestamp":"2025-05-26 02:59:32"}
{"level":"http","message":"POST / 200 - 5ms","timestamp":"2025-05-26 02:59:32"}
