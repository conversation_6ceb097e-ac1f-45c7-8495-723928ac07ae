import {
  Component,
  OnIni<PERSON>,
  On<PERSON><PERSON><PERSON>,
  <PERSON>Child,
  ElementRef,
  AfterViewChecked,
  ChangeDetectorRef,
} from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { AuthuserService } from 'src/app/services/authuser.service';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Subscription, combineLatest, Observable, of } from 'rxjs';
import { User } from '@app/models/user.model';
import { UserStatusService } from 'src/app/services/user-status.service';
import {
  Message,
  Conversation,
  Attachment,
  MessageType,
  CallType,
} from 'src/app/models/message.model';
import { ToastService } from 'src/app/services/toast.service';
import { switchMap, distinctUntilChanged, filter } from 'rxjs/operators';
import { MessageService } from '@app/services/message.service';
import { LoggerService } from 'src/app/services/logger.service';
@Component({
  selector: 'app-message-chat',
  templateUrl: 'message-chat.component.html',
  styleUrls: ['./message-chat.component.css', './message-chat-magic.css'],
})
export class MessageChatComponent
  implements OnInit, OnDestroy, AfterViewChecked
{
  @ViewChild('messagesContainer') private messagesContainer!: ElementRef;
  @ViewChild('fileInput', { static: false })
  fileInput!: ElementRef<HTMLInputElement>;

  messages: Message[] = [];
  messageForm: FormGroup;
  conversation: Conversation | null = null;
  loading = true;
  error: any;
  currentUserId: string | null = null;
  currentUsername: string = 'You';
  otherParticipant: User | null = null;
  selectedFile: File | null = null;
  previewUrl: string | ArrayBuffer | null = null;
  isUploading = false;
  isTyping = false;
  typingTimeout: any;
  isRecordingVoice = false;
  voiceRecordingDuration = 0;

  private readonly MAX_MESSAGES_PER_SIDE = 5; // Nombre maximum de messages à afficher par côté (expéditeur/destinataire)
  private readonly MAX_MESSAGES_TO_LOAD = 10; // Nombre maximum de messages à charger à la fois (pagination)
  private readonly MAX_TOTAL_MESSAGES = 100; // Limite totale de messages à conserver en mémoire
  private currentPage = 1; // Page actuelle pour la pagination
  isLoadingMore = false; // Indicateur de chargement en cours (public pour le template)
  hasMoreMessages = true; // Indique s'il y a plus de messages à charger (public pour le template)
  private subscriptions: Subscription = new Subscription();

  // Variables pour l'édition et la suppression de messages
  editingMessageId: string | null = null;
  editingContent: string = '';
  showMessageOptions: { [messageId: string]: boolean } = {};
  showDeleteConfirm: { [messageId: string]: boolean } = {};

  // Variables pour les réactions aux messages
  showReactionPicker: { [messageId: string]: boolean } = {};
  availableReactions: string[] = [
    '👍',
    '❤️',
    '😂',
    '😮',
    '😢',
    '😡',
    '👏',
    '🔥',
  ];

  // Variables pour la recherche de messages
  showSearchBar: boolean = false;
  searchQuery: string = '';
  searchResults: Message[] = [];
  isSearching: boolean = false;
  searchMode: boolean = false;

  // Variables pour le transfert de messages
  showForwardModal: boolean = false;
  forwardingMessageId: string | null = null;
  forwardingMessage: Message | null = null;
  availableConversations: Conversation[] = [];
  selectedConversations: string[] = [];
  isLoadingConversations: boolean = false;
  isForwarding: boolean = false;

  // Variables pour l'épinglage de messages
  pinnedMessages: Message[] = [];
  showPinnedMessages: boolean = false;
  isPinning: { [messageId: string]: boolean } = {};
  showPinConfirm: { [messageId: string]: boolean } = {};

  // Variables pour la gestion des groupes
  showGroupModal: boolean = false;
  groupModalMode: 'create' | 'edit' | 'info' | 'participants' = 'create';
  currentGroup: any = null;
  groupForm: FormGroup;
  availableUsers: User[] = [];
  selectedParticipants: string[] = [];
  isLoadingUsers: boolean = false;
  isCreatingGroup: boolean = false;
  isUpdatingGroup: boolean = false;
  isDeletingGroup: boolean = false;
  showDeleteGroupConfirm: boolean = false;
  showLeaveGroupConfirm: boolean = false;
  groupPhotoFile: File | null = null;
  groupPhotoPreview: string | null = null;

  // Variables pour le sélecteur de thème
  selectedTheme: string = 'theme-default'; // Thème par défaut
  showThemeSelector: boolean = false; // Affichage du sélecteur de thème

  // Variables pour le sélecteur d'émojis
  showEmojiPicker: boolean = false;

  // Variables pour les appels
  incomingCall: any = null;
  showCallModal: boolean = false;
  activeCall: any = null;
  showActiveCallModal: boolean = false;
  isCallMuted: boolean = false;
  isVideoEnabled: boolean = true;
  callDuration: number = 0;
  callTimer: any = null;
  localVideoElement: HTMLVideoElement | null = null;
  remoteVideoElement: HTMLVideoElement | null = null;
  isCallMinimized: boolean = false;
  callQuality: 'excellent' | 'good' | 'poor' | 'connecting' = 'connecting';
  showCallControls: boolean = true;
  callControlsTimeout: any = null;

  // Variables pour la gestion avancée des notifications
  notifications: any[] = [];
  showNotificationPanel: boolean = false;
  unreadNotificationCount: number = 0;
  selectedNotifications: Set<string> = new Set();
  showNotificationOptions: boolean = false;
  notificationFilter: 'all' | 'unread' | 'read' = 'all';
  isLoadingNotifications: boolean = false;
  isMarkingAsRead: boolean = false;
  isDeletingNotifications: boolean = false;
  showDeleteConfirmModal: boolean = false;
  notificationPage: number = 1;
  hasMoreNotifications: boolean = true;
  showNotificationSettings: boolean = false;
  notificationSounds: boolean = true;
  notificationPreview: boolean = true;
  autoMarkAsRead: boolean = true;

  // Variables pour le statut utilisateur en temps réel
  onlineUsers: Map<string, User> = new Map();
  userStatusMap: Map<string, { isOnline: boolean; lastActive: Date | null }> =
    new Map();
  showUserStatusPanel: boolean = false;
  isUpdatingStatus: boolean = false;
  currentUserStatus: 'online' | 'offline' | 'away' | 'busy' = 'online';
  statusUpdateInterval: any = null;
  lastActivityTime: Date = new Date();
  autoAwayTimeout: any = null;
  autoAwayDelay: number = 300000; // 5 minutes
  showStatusSelector: boolean = false;
  statusHistory: Array<{ status: string; timestamp: Date; userId: string }> =
    [];
  presenceUpdateQueue: Array<{ userId: string; status: any; timestamp: Date }> =
    [];
  isProcessingPresenceUpdates: boolean = false;
  userActivityTracker: Map<string, Date> = new Map();
  showDetailedStatus: boolean = false;
  statusFilterType: 'all' | 'online' | 'offline' | 'away' | 'busy' = 'all';

  commonEmojis: string[] = [
    '😀',
    '😃',
    '😄',
    '😁',
    '😆',
    '😅',
    '😂',
    '🤣',
    '😊',
    '😇',
    '🙂',
    '🙃',
    '😉',
    '😌',
    '😍',
    '🥰',
    '😘',
    '😗',
    '😙',
    '😚',
    '😋',
    '😛',
    '😝',
    '😜',
    '🤪',
    '🤨',
    '🧐',
    '🤓',
    '😎',
    '🤩',
    '😏',
    '😒',
    '😞',
    '😔',
    '😟',
    '😕',
    '🙁',
    '☹️',
    '😣',
    '😖',
    '😫',
    '😩',
    '🥺',
    '😢',
    '😭',
    '😤',
    '😠',
    '😡',
    '🤬',
    '🤯',
    '😳',
    '🥵',
    '🥶',
    '😱',
    '😨',
    '😰',
    '😥',
    '😓',
    '🤗',
    '🤔',
    '👍',
    '👎',
    '👏',
    '🙌',
    '👐',
    '🤲',
    '🤝',
    '🙏',
    '✌️',
    '🤞',
    '❤️',
    '🧡',
    '💛',
    '💚',
    '💙',
    '💜',
    '🖤',
    '💔',
    '💯',
    '💢',
  ];

  constructor(
    private MessageService: MessageService,
    public route: ActivatedRoute,
    private authService: AuthuserService,
    private fb: FormBuilder,
    public statusService: UserStatusService,
    public router: Router,
    private toastService: ToastService,
    private logger: LoggerService,
    private cdr: ChangeDetectorRef
  ) {
    this.messageForm = this.fb.group({
      content: ['', [Validators.maxLength(1000)]],
    });

    // Initialiser le formulaire de groupe
    this.groupForm = this.fb.group({
      name: [
        '',
        [
          Validators.required,
          Validators.minLength(2),
          Validators.maxLength(50),
        ],
      ],
      description: ['', [Validators.maxLength(200)]],
    });
  }
  ngOnInit(): void {
    this.currentUserId = this.authService.getCurrentUserId();

    // Charger le thème sauvegardé
    const savedTheme = localStorage.getItem('chat-theme');
    if (savedTheme) {
      this.selectedTheme = savedTheme;
      this.logger.debug('MessageChat', `Loaded saved theme: ${savedTheme}`);
    }

    // Récupérer les messages vocaux pour assurer leur persistance
    this.loadVoiceMessages();

    // S'abonner aux notifications en temps réel
    this.subscribeToNotifications();

    // S'abonner au statut utilisateur en temps réel
    this.subscribeToUserStatus();

    // Initialiser le statut utilisateur
    this.initializeUserStatus();

    // Démarrer le suivi d'activité
    this.startActivityTracking();

    // Ajouter un gestionnaire de clic global pour fermer les sélecteurs de réactions
    document.addEventListener('click', this.onDocumentClick.bind(this));

    const routeSub = this.route.params
      .pipe(
        filter((params) => params['id']),
        distinctUntilChanged(),
        switchMap((params) => {
          this.loading = true;
          this.messages = [];
          this.currentPage = 1; // Réinitialiser à la page 1
          this.hasMoreMessages = true; // Réinitialiser l'indicateur de messages supplémentaires

          this.logger.debug(
            'MessageChat',
            `Loading conversation with pagination: page=${this.currentPage}, limit=${this.MAX_MESSAGES_TO_LOAD}`
          );

          // Charger la conversation avec pagination (page 1, limit 10)
          return this.MessageService.getConversation(
            params['id'],
            this.MAX_MESSAGES_TO_LOAD,
            this.currentPage // Utiliser la page au lieu de l'offset
          );
        })
      )
      .subscribe({
        next: (conversation) => {
          this.handleConversationLoaded(conversation);
        },
        error: (error) => {
          this.handleError('Failed to load conversation', error);
        },
      });
    this.subscriptions.add(routeSub);
  }

  /**
   * Charge les messages vocaux pour assurer leur persistance
   */
  private loadVoiceMessages(): void {
    this.logger.debug('MessageChat', 'Loading voice messages for persistence');

    const sub = this.MessageService.getVoiceMessages().subscribe({
      next: (voiceMessages) => {
        this.logger.info(
          'MessageChat',
          `Retrieved ${voiceMessages.length} voice messages`
        );

        // Les messages vocaux sont maintenant chargés et disponibles dans le service
        // Ils seront automatiquement associés aux conversations correspondantes
        if (voiceMessages.length > 0) {
          this.logger.debug(
            'MessageChat',
            'Voice messages loaded successfully'
          );

          // Forcer le rafraîchissement de la vue après le chargement des messages vocaux
          setTimeout(() => {
            this.cdr.detectChanges();
            this.logger.debug(
              'MessageChat',
              'View refreshed after loading voice messages'
            );
          }, 100);
        }
      },
      error: (error) => {
        this.logger.error(
          'MessageChat',
          'Error loading voice messages:',
          error
        );
        // Ne pas bloquer l'expérience utilisateur si le chargement des messages vocaux échoue
      },
    });

    this.subscriptions.add(sub);
  }

  /**
   * Gère les erreurs et les affiche à l'utilisateur
   * @param message Message d'erreur à afficher
   * @param error Objet d'erreur
   */
  private handleError(message: string, error: any): void {
    this.logger.error('MessageChat', message, error);
    this.loading = false;
    this.error = error;
    this.toastService.showError(message);
  }

  // logique FileService
  getFileIcon(mimeType?: string): string {
    if (!mimeType) return 'fa-file';
    if (mimeType.startsWith('image/')) return 'fa-image';
    if (mimeType.includes('pdf')) return 'fa-file-pdf';
    if (mimeType.includes('word') || mimeType.includes('msword'))
      return 'fa-file-word';
    if (mimeType.includes('excel')) return 'fa-file-excel';
    if (mimeType.includes('powerpoint')) return 'fa-file-powerpoint';
    if (mimeType.includes('audio')) return 'fa-file-audio';
    if (mimeType.includes('video')) return 'fa-file-video';
    if (mimeType.includes('zip') || mimeType.includes('compressed'))
      return 'fa-file-archive';
    return 'fa-file';
  }
  getFileType(mimeType?: string): string {
    if (!mimeType) return 'File';

    const typeMap: Record<string, string> = {
      'image/': 'Image',
      'application/pdf': 'PDF',
      'application/msword': 'Word Doc',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
        'Word Doc',
      'application/vnd.ms-excel': 'Excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
        'Excel',
      'application/vnd.ms-powerpoint': 'PowerPoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation':
        'PowerPoint',
      'audio/': 'Audio',
      'video/': 'Video',
      'application/zip': 'ZIP Archive',
      'application/x-rar-compressed': 'RAR Archive',
    };
    for (const [key, value] of Object.entries(typeMap)) {
      if (mimeType.includes(key)) return value;
    }
    return 'File';
  }

  private handleConversationLoaded(conversation: Conversation): void {
    this.logger.info(
      'MessageChat',
      `Handling loaded conversation: ${conversation.id}`
    );
    this.logger.debug(
      'MessageChat',
      `Conversation has ${conversation?.messages?.length || 0} messages and ${
        conversation?.participants?.length || 0
      } participants`
    );

    // Log détaillé des messages pour le débogage
    if (conversation?.messages && conversation.messages.length > 0) {
      this.logger.debug(
        'MessageChat',
        `First message details: id=${
          conversation.messages[0].id
        }, content=${conversation.messages[0].content?.substring(
          0,
          20
        )}, sender=${conversation.messages[0].sender?.username}`
      );
    }

    this.conversation = conversation;

    // Si la conversation n'a pas de messages, initialiser un tableau vide
    if (!conversation?.messages || conversation.messages.length === 0) {
      this.logger.debug('MessageChat', 'No messages found in conversation');

      // Récupérer les participants
      this.otherParticipant =
        conversation?.participants?.find(
          (p) => p.id !== this.currentUserId && p._id !== this.currentUserId
        ) || null;

      // Initialiser un tableau vide pour les messages
      this.messages = [];

      this.logger.debug('MessageChat', 'Initialized empty messages array');
    } else {
      // Récupérer les messages de la conversation
      const conversationMessages = [...(conversation?.messages || [])];

      // Trier les messages par date (du plus ancien au plus récent)
      conversationMessages.sort((a, b) => {
        const timeA =
          a.timestamp instanceof Date
            ? a.timestamp.getTime()
            : new Date(a.timestamp as string).getTime();
        const timeB =
          b.timestamp instanceof Date
            ? b.timestamp.getTime()
            : new Date(b.timestamp as string).getTime();
        return timeA - timeB;
      });

      // Log détaillé pour comprendre la structure des messages
      if (conversationMessages.length > 0) {
        const firstMessage = conversationMessages[0];
        this.logger.debug(
          'MessageChat',
          `Message structure: sender.id=${firstMessage.sender?.id}, sender._id=${firstMessage.sender?._id}, senderId=${firstMessage.senderId}, receiver.id=${firstMessage.receiver?.id}, receiver._id=${firstMessage.receiver?._id}, receiverId=${firstMessage.receiverId}`
        );
      }

      // Utiliser directement tous les messages triés sans filtrage supplémentaire
      this.messages = conversationMessages;

      this.logger.debug(
        'MessageChat',
        `Using all ${this.messages.length} messages from conversation`
      );

      this.logger.debug(
        'MessageChat',
        `Using ${conversationMessages.length} messages from conversation, showing last ${this.messages.length}`
      );
    }

    this.otherParticipant =
      conversation?.participants?.find(
        (p) => p.id !== this.currentUserId && p._id !== this.currentUserId
      ) || null;

    this.logger.debug(
      'MessageChat',
      `Other participant identified: ${
        this.otherParticipant?.username || 'Unknown'
      }`
    );

    this.loading = false;
    setTimeout(() => this.scrollToBottom(), 100);

    // Mettre à jour la liste des messages épinglés
    this.updatePinnedMessagesList();

    this.logger.debug('MessageChat', `Marking unread messages as read`);
    this.markMessagesAsRead();

    if (this.conversation?.id) {
      this.logger.debug(
        'MessageChat',
        `Setting up subscriptions for conversation: ${this.conversation.id}`
      );
      this.subscribeToConversationUpdates(this.conversation.id);
      this.subscribeToNewMessages(this.conversation.id);
      this.subscribeToTypingIndicators(this.conversation.id);
    }

    this.logger.info('MessageChat', `Conversation loaded successfully`);
  }

  private subscribeToConversationUpdates(conversationId: string): void {
    const sub = this.MessageService.subscribeToConversationUpdates(
      conversationId
    ).subscribe({
      next: (updatedConversation) => {
        this.conversation = updatedConversation;
        this.messages = updatedConversation.messages
          ? [...updatedConversation.messages]
          : [];
        this.scrollToBottom();
      },
      error: (error) => {
        this.toastService.showError('Connection to conversation updates lost');
      },
    });
    this.subscriptions.add(sub);
  }

  private subscribeToNewMessages(conversationId: string): void {
    const sub = this.MessageService.subscribeToNewMessages(
      conversationId
    ).subscribe({
      next: (newMessage) => {
        if (newMessage?.conversationId === this.conversation?.id) {
          // Ajouter le nouveau message à la liste complète
          this.messages = [...this.messages, newMessage].sort((a, b) => {
            const timeA =
              a.timestamp instanceof Date
                ? a.timestamp.getTime()
                : new Date(a.timestamp as string).getTime();
            const timeB =
              b.timestamp instanceof Date
                ? b.timestamp.getTime()
                : new Date(b.timestamp as string).getTime();
            return timeA - timeB; // Tri par ordre croissant pour l'affichage
          });

          this.logger.debug(
            'MessageChat',
            `Added new message, now showing ${this.messages.length} messages`
          );

          setTimeout(() => this.scrollToBottom(), 100);

          // Marquer le message comme lu s'il vient d'un autre utilisateur
          if (
            newMessage.sender?.id !== this.currentUserId &&
            newMessage.sender?._id !== this.currentUserId
          ) {
            if (newMessage.id) {
              this.MessageService.markMessageAsRead(newMessage.id).subscribe();
            }
          }
        }
      },
      error: (error) => {
        this.toastService.showError('Connection to new messages lost');
      },
    });
    this.subscriptions.add(sub);
  }

  private subscribeToTypingIndicators(conversationId: string): void {
    const sub = this.MessageService.subscribeToTypingIndicator(
      conversationId
    ).subscribe({
      next: (event) => {
        if (event.userId !== this.currentUserId) {
          this.isTyping = event.isTyping;
          if (this.isTyping) {
            clearTimeout(this.typingTimeout);
            this.typingTimeout = setTimeout(() => {
              this.isTyping = false;
            }, 2000);
          }
        }
      },
    });
    this.subscriptions.add(sub);
  }

  private markMessagesAsRead(): void {
    const unreadMessages = this.messages.filter(
      (msg) =>
        !msg.isRead &&
        (msg.receiver?.id === this.currentUserId ||
          msg.receiver?._id === this.currentUserId)
    );

    unreadMessages.forEach((msg) => {
      if (msg.id) {
        const sub = this.MessageService.markMessageAsRead(msg.id).subscribe({
          error: (error) => {
            this.logger.error(
              'MessageChat',
              'Error marking message as read:',
              error
            );
          },
        });
        this.subscriptions.add(sub);
      }
    });
  }

  onFileSelected(event: any): void {
    const file = event.target.files[0];
    if (!file) return;

    // Validate file size (e.g., 5MB max)
    if (file.size > 5 * 1024 * 1024) {
      this.toastService.showError('File size should be less than 5MB');
      return;
    }

    // Validate file type
    const validTypes = [
      'image/jpeg',
      'image/png',
      'image/gif',
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    ];
    if (!validTypes.includes(file.type)) {
      this.toastService.showError(
        'Invalid file type. Only images, PDFs and Word docs are allowed'
      );
      return;
    }

    this.selectedFile = file;
    const reader = new FileReader();
    reader.onload = () => {
      this.previewUrl = reader.result;
    };
    reader.readAsDataURL(file);
  }

  removeAttachment(): void {
    this.selectedFile = null;
    this.previewUrl = null;
    if (this.fileInput?.nativeElement) {
      this.fileInput.nativeElement.value = '';
    }
  }

  private typingTimer: any;
  private isCurrentlyTyping = false;
  private readonly TYPING_DELAY = 500; // Délai en ms avant d'envoyer l'événement de frappe
  private readonly TYPING_TIMEOUT = 3000; // Délai en ms avant d'arrêter l'indicateur de frappe

  /**
   * Gère l'événement de frappe de l'utilisateur
   * Envoie un indicateur de frappe avec un délai pour éviter trop de requêtes
   */
  onTyping(): void {
    if (!this.conversation?.id || !this.currentUserId) {
      return;
    }

    // Stocker l'ID de conversation pour éviter les erreurs TypeScript
    const conversationId = this.conversation.id;

    // Annuler le timer précédent
    clearTimeout(this.typingTimer);

    // Si l'utilisateur n'est pas déjà en train de taper, envoyer l'événement immédiatement
    if (!this.isCurrentlyTyping) {
      this.isCurrentlyTyping = true;
      this.logger.debug('MessageChat', 'Starting typing indicator');

      this.MessageService.startTyping(conversationId).subscribe({
        next: () => {
          this.logger.debug(
            'MessageChat',
            'Typing indicator started successfully'
          );
        },
        error: (error) => {
          this.logger.error(
            'MessageChat',
            'Error starting typing indicator:',
            error
          );
        },
      });
    }

    // Définir un timer pour arrêter l'indicateur de frappe après un délai d'inactivité
    this.typingTimer = setTimeout(() => {
      if (this.isCurrentlyTyping) {
        this.isCurrentlyTyping = false;
        this.logger.debug(
          'MessageChat',
          'Stopping typing indicator due to inactivity'
        );

        this.MessageService.stopTyping(conversationId).subscribe({
          next: () => {
            this.logger.debug(
              'MessageChat',
              'Typing indicator stopped successfully'
            );
          },
          error: (error) => {
            this.logger.error(
              'MessageChat',
              'Error stopping typing indicator:',
              error
            );
          },
        });
      }
    }, this.TYPING_TIMEOUT);
  }

  /**
   * Affiche ou masque le sélecteur de thème
   */
  toggleThemeSelector(): void {
    this.showThemeSelector = !this.showThemeSelector;

    // Fermer le sélecteur de thème lorsqu'on clique ailleurs
    if (this.showThemeSelector) {
      setTimeout(() => {
        const clickHandler = (event: MouseEvent) => {
          const target = event.target as HTMLElement;
          if (!target.closest('.theme-selector')) {
            this.showThemeSelector = false;
            document.removeEventListener('click', clickHandler);
          }
        };
        document.addEventListener('click', clickHandler);
      }, 0);
    }
  }

  /**
   * Change le thème de la conversation
   * @param theme Nom du thème à appliquer
   */
  changeTheme(theme: string): void {
    this.selectedTheme = theme;
    this.showThemeSelector = false;

    // Sauvegarder le thème dans le localStorage pour le conserver entre les sessions
    localStorage.setItem('chat-theme', theme);

    this.logger.debug('MessageChat', `Theme changed to: ${theme}`);
  }

  sendMessage(): void {
    this.logger.info('MessageChat', `Attempting to send message`);

    // Vérifier l'authentification
    const token = localStorage.getItem('token');
    this.logger.debug(
      'MessageChat',
      `Authentication check: token=${!!token}, userId=${this.currentUserId}`
    );

    if (
      (this.messageForm.invalid && !this.selectedFile) ||
      !this.currentUserId ||
      !this.otherParticipant?.id
    ) {
      this.logger.warn(
        'MessageChat',
        `Cannot send message: form invalid or missing user IDs`
      );
      return;
    }

    // Arrêter l'indicateur de frappe lorsqu'un message est envoyé
    this.stopTypingIndicator();

    const content = this.messageForm.get('content')?.value;

    // Créer un message temporaire pour l'affichage immédiat (comme dans Facebook Messenger)
    const tempMessage: Message = {
      id: 'temp-' + new Date().getTime(),
      content: content || '',
      sender: {
        id: this.currentUserId || '',
        username: this.currentUsername,
      },
      receiver: {
        id: this.otherParticipant.id,
        username: this.otherParticipant.username || 'Recipient',
      },
      timestamp: new Date(),
      isRead: false,
      isPending: true, // Marquer comme en attente
    };

    // Si un fichier est sélectionné, ajouter l'aperçu au message temporaire
    if (this.selectedFile) {
      // Déterminer le type de fichier
      let fileType = 'file';
      if (this.selectedFile.type.startsWith('image/')) {
        fileType = 'image';

        // Pour les images, ajouter un aperçu immédiat
        if (this.previewUrl) {
          tempMessage.attachments = [
            {
              id: 'temp-attachment',
              url: this.previewUrl ? this.previewUrl.toString() : '',
              type: MessageType.IMAGE,
              name: this.selectedFile.name,
              size: this.selectedFile.size,
            },
          ];
        }
      }

      // Définir le type de message en fonction du type de fichier
      if (fileType === 'image') {
        tempMessage.type = MessageType.IMAGE;
      } else if (fileType === 'file') {
        tempMessage.type = MessageType.FILE;
      }
    }

    // Ajouter immédiatement le message temporaire à la liste
    this.messages = [...this.messages, tempMessage];

    // Réinitialiser le formulaire immédiatement pour une meilleure expérience utilisateur
    const fileToSend = this.selectedFile; // Sauvegarder une référence
    this.messageForm.reset();
    this.removeAttachment();

    // Forcer le défilement vers le bas immédiatement
    setTimeout(() => this.scrollToBottom(true), 50);

    // Maintenant, envoyer le message au serveur
    this.isUploading = true;

    const sendSub = this.MessageService.sendMessage(
      this.otherParticipant.id,
      content,
      fileToSend || undefined,
      MessageType.TEXT
    ).subscribe({
      next: (message) => {
        this.logger.info(
          'MessageChat',
          `Message sent successfully: ${message?.id || 'unknown'}`
        );

        // Remplacer le message temporaire par le message réel
        this.messages = this.messages.map((msg) =>
          msg.id === tempMessage.id ? message : msg
        );

        this.isUploading = false;
      },
      error: (error) => {
        this.logger.error('MessageChat', `Error sending message:`, error);

        // Marquer le message temporaire comme échoué
        this.messages = this.messages.map((msg) => {
          if (msg.id === tempMessage.id) {
            return {
              ...msg,
              isPending: false,
              isError: true,
            };
          }
          return msg;
        });

        this.isUploading = false;
        this.toastService.showError('Failed to send message');
      },
    });

    this.subscriptions.add(sendSub);
  }

  formatMessageTime(timestamp: string | Date | undefined): string {
    if (!timestamp) {
      return 'Unknown time';
    }
    try {
      const date = timestamp instanceof Date ? timestamp : new Date(timestamp);
      // Format heure:minute sans les secondes, comme dans l'image de référence
      return date.toLocaleTimeString([], {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false,
      });
    } catch (error) {
      this.logger.error('MessageChat', 'Error formatting message time:', error);
      return 'Invalid time';
    }
  }

  formatLastActive(lastActive: string | Date | undefined): string {
    if (!lastActive) return 'Offline';
    const lastActiveDate =
      lastActive instanceof Date ? lastActive : new Date(lastActive);
    const now = new Date();
    const diffHours =
      Math.abs(now.getTime() - lastActiveDate.getTime()) / (1000 * 60 * 60);

    if (diffHours < 24) {
      return `Active ${lastActiveDate.toLocaleTimeString([], {
        hour: '2-digit',
        minute: '2-digit',
      })}`;
    }
    return `Active ${lastActiveDate.toLocaleDateString()}`;
  }

  formatMessageDate(timestamp: string | Date | undefined): string {
    if (!timestamp) {
      return 'Unknown date';
    }

    try {
      const date = timestamp instanceof Date ? timestamp : new Date(timestamp);
      const today = new Date();

      // Format pour l'affichage comme dans l'image de référence
      const options: Intl.DateTimeFormatOptions = {
        weekday: 'short',
        hour: '2-digit',
        minute: '2-digit',
      };

      if (date.toDateString() === today.toDateString()) {
        return date.toLocaleTimeString([], {
          hour: '2-digit',
          minute: '2-digit',
        });
      }

      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);

      if (date.toDateString() === yesterday.toDateString()) {
        return `LUN., ${date.toLocaleTimeString([], {
          hour: '2-digit',
          minute: '2-digit',
        })}`;
      }

      // Format pour les autres jours (comme dans l'image)
      const day = date
        .toLocaleDateString('fr-FR', { weekday: 'short' })
        .toUpperCase();
      return `${day}., ${date.toLocaleTimeString([], {
        hour: '2-digit',
        minute: '2-digit',
      })}`;
    } catch (error) {
      this.logger.error('MessageChat', 'Error formatting message date:', error);
      return 'Invalid date';
    }
  }

  shouldShowDateHeader(index: number): boolean {
    if (index === 0) return true;

    try {
      const currentMsg = this.messages[index];
      const prevMsg = this.messages[index - 1];

      if (!currentMsg?.timestamp || !prevMsg?.timestamp) {
        return true;
      }

      const currentDate = this.getDateFromTimestamp(currentMsg.timestamp);
      const prevDate = this.getDateFromTimestamp(prevMsg.timestamp);

      return currentDate !== prevDate;
    } catch (error) {
      this.logger.error('MessageChat', 'Error checking date header:', error);
      return false;
    }
  }

  private getDateFromTimestamp(timestamp: string | Date | undefined): string {
    if (!timestamp) {
      return 'unknown-date';
    }

    try {
      return (
        timestamp instanceof Date ? timestamp : new Date(timestamp)
      ).toDateString();
    } catch (error) {
      this.logger.error(
        'MessageChat',
        'Error getting date from timestamp:',
        error
      );
      return 'invalid-date';
    }
  }
  getMessageType(message: Message | null | undefined): MessageType {
    if (!message) {
      return MessageType.TEXT;
    }

    try {
      // Vérifier d'abord le type de message explicite
      if (message.type) {
        // Convertir les types en minuscules en leurs équivalents en majuscules
        const msgType = message.type.toString();
        if (msgType === 'text' || msgType === 'TEXT') {
          return MessageType.TEXT;
        } else if (msgType === 'image' || msgType === 'IMAGE') {
          return MessageType.IMAGE;
        } else if (msgType === 'file' || msgType === 'FILE') {
          return MessageType.FILE;
        } else if (msgType === 'audio' || msgType === 'AUDIO') {
          return MessageType.AUDIO;
        } else if (msgType === 'video' || msgType === 'VIDEO') {
          return MessageType.VIDEO;
        } else if (msgType === 'system' || msgType === 'SYSTEM') {
          return MessageType.SYSTEM;
        }
      }

      // Ensuite, vérifier les pièces jointes
      if (message.attachments?.length) {
        const attachment = message.attachments[0];
        if (attachment && attachment.type) {
          const attachmentTypeStr = attachment.type.toString();

          // Gérer les différentes formes de types d'attachements
          if (attachmentTypeStr === 'image' || attachmentTypeStr === 'IMAGE') {
            return MessageType.IMAGE;
          } else if (
            attachmentTypeStr === 'file' ||
            attachmentTypeStr === 'FILE'
          ) {
            return MessageType.FILE;
          } else if (
            attachmentTypeStr === 'audio' ||
            attachmentTypeStr === 'AUDIO'
          ) {
            return MessageType.AUDIO;
          } else if (
            attachmentTypeStr === 'video' ||
            attachmentTypeStr === 'VIDEO'
          ) {
            return MessageType.VIDEO;
          }
        }

        // Type par défaut pour les pièces jointes
        return MessageType.FILE;
      }

      // Type par défaut
      return MessageType.TEXT;
    } catch (error) {
      this.logger.error('MessageChat', 'Error getting message type:', error);
      return MessageType.TEXT;
    }
  }

  // Méthode auxiliaire pour vérifier si un message contient une image
  hasImage(message: Message | null | undefined): boolean {
    if (!message || !message.attachments || message.attachments.length === 0) {
      return false;
    }

    const attachment = message.attachments[0];
    if (!attachment || !attachment.type) {
      return false;
    }

    const type = attachment.type.toString();
    return type === 'IMAGE' || type === 'image';
  }

  /**
   * Vérifie si le message est un message vocal
   */
  isVoiceMessage(message: Message | null | undefined): boolean {
    if (!message) {
      return false;
    }

    // Vérifier le type du message
    if (
      message.type === MessageType.VOICE_MESSAGE ||
      message.type === MessageType.VOICE_MESSAGE_LOWER
    ) {
      return true;
    }

    // Vérifier les pièces jointes
    if (message.attachments && message.attachments.length > 0) {
      return message.attachments.some((att) => {
        const type = att.type?.toString();
        return (
          type === 'VOICE_MESSAGE' ||
          type === 'voice_message' ||
          (message.metadata?.isVoiceMessage &&
            (type === 'AUDIO' || type === 'audio'))
        );
      });
    }

    // Vérifier les métadonnées
    return !!message.metadata?.isVoiceMessage;
  }

  /**
   * Récupère l'URL du message vocal
   */
  getVoiceMessageUrl(message: Message | null | undefined): string {
    if (!message || !message.attachments || message.attachments.length === 0) {
      return '';
    }

    // Chercher une pièce jointe de type message vocal ou audio
    const voiceAttachment = message.attachments.find((att) => {
      const type = att.type?.toString();
      return (
        type === 'VOICE_MESSAGE' ||
        type === 'voice_message' ||
        type === 'AUDIO' ||
        type === 'audio'
      );
    });

    return voiceAttachment?.url || '';
  }

  /**
   * Récupère la durée du message vocal
   */
  getVoiceMessageDuration(message: Message | null | undefined): number {
    if (!message) {
      return 0;
    }

    // Essayer d'abord de récupérer la durée depuis les métadonnées
    if (message.metadata?.duration) {
      return message.metadata.duration;
    }

    // Sinon, essayer de récupérer depuis les pièces jointes
    if (message.attachments && message.attachments.length > 0) {
      const voiceAttachment = message.attachments.find((att) => {
        const type = att.type?.toString();
        return (
          type === 'VOICE_MESSAGE' ||
          type === 'voice_message' ||
          type === 'AUDIO' ||
          type === 'audio'
        );
      });

      if (voiceAttachment && voiceAttachment.duration) {
        return voiceAttachment.duration;
      }
    }

    return 0;
  }

  // Méthode pour obtenir l'URL de l'image en toute sécurité
  getImageUrl(message: Message | null | undefined): string {
    if (!message || !message.attachments || message.attachments.length === 0) {
      return '';
    }

    const attachment = message.attachments[0];
    return attachment?.url || '';
  }

  getMessageTypeClass(message: Message | null | undefined): string {
    if (!message) {
      return 'bg-gray-100 rounded-lg px-4 py-2';
    }

    try {
      const isCurrentUser =
        message.sender?.id === this.currentUserId ||
        message.sender?._id === this.currentUserId ||
        message.senderId === this.currentUserId;

      // Utiliser une couleur plus foncée pour les messages de l'utilisateur actuel (à droite)
      // et une couleur plus claire pour les messages des autres utilisateurs (à gauche)
      // Couleurs et forme adaptées exactement à l'image de référence mobile
      const baseClass = isCurrentUser
        ? 'bg-blue-500 text-white rounded-2xl rounded-br-sm'
        : 'bg-gray-200 text-gray-800 rounded-2xl rounded-bl-sm';

      const messageType = this.getMessageType(message);

      // Vérifier si le message contient une image
      if (message.attachments && message.attachments.length > 0) {
        const attachment = message.attachments[0];
        if (attachment && attachment.type) {
          const attachmentTypeStr = attachment.type.toString();
          if (attachmentTypeStr === 'IMAGE' || attachmentTypeStr === 'image') {
            // Pour les images, on utilise un style sans bordure
            return `p-1 max-w-xs`;
          } else if (
            attachmentTypeStr === 'FILE' ||
            attachmentTypeStr === 'file'
          ) {
            return `${baseClass} p-3`;
          }
        }
      }

      // Vérifier le type de message
      if (
        messageType === MessageType.IMAGE ||
        messageType === MessageType.IMAGE_LOWER
      ) {
        // Pour les images, on utilise un style sans bordure
        return `p-1 max-w-xs`;
      } else if (
        messageType === MessageType.FILE ||
        messageType === MessageType.FILE_LOWER
      ) {
        return `${baseClass} p-3`;
      }

      // Type par défaut (texte)
      return `${baseClass} px-4 py-3 whitespace-normal break-words min-w-[120px]`;
    } catch (error) {
      this.logger.error(
        'MessageChat',
        'Error getting message type class:',
        error
      );
      return 'bg-gray-100 rounded-lg px-4 py-2 whitespace-normal break-words';
    }
  }

  // La méthode ngAfterViewChecked est implémentée plus bas dans le fichier

  // Méthode pour détecter le défilement vers le haut et charger plus de messages
  onScroll(event: any): void {
    const container = event.target;
    const scrollTop = container.scrollTop;

    // Si on est proche du haut de la liste et qu'on n'est pas déjà en train de charger
    if (
      scrollTop < 50 &&
      !this.isLoadingMore &&
      this.conversation?.id &&
      this.hasMoreMessages
    ) {
      // Afficher un indicateur de chargement en haut de la liste
      this.showLoadingIndicator();

      // Sauvegarder la hauteur actuelle et la position des messages
      const oldScrollHeight = container.scrollHeight;
      const firstVisibleMessage = this.getFirstVisibleMessage();

      // Marquer comme chargement en cours
      this.isLoadingMore = true;

      // Charger plus de messages avec un délai réduit
      this.loadMoreMessages();

      // Maintenir la position de défilement pour que l'utilisateur reste au même endroit
      // en utilisant le premier message visible comme ancre
      requestAnimationFrame(() => {
        const preserveScrollPosition = () => {
          if (firstVisibleMessage) {
            const messageElement = this.findMessageElement(
              firstVisibleMessage.id
            );
            if (messageElement) {
              // Faire défiler jusqu'à l'élément qui était visible avant
              messageElement.scrollIntoView({ block: 'center' });
            } else {
              // Fallback: utiliser la différence de hauteur
              const newScrollHeight = container.scrollHeight;
              const scrollDiff = newScrollHeight - oldScrollHeight;
              container.scrollTop = scrollTop + scrollDiff;
            }
          }

          // Masquer l'indicateur de chargement
          this.hideLoadingIndicator();
        };

        // Attendre que le DOM soit mis à jour
        setTimeout(preserveScrollPosition, 100);
      });
    }
  }

  // Méthode pour trouver le premier message visible dans la vue
  private getFirstVisibleMessage(): Message | null {
    if (!this.messagesContainer?.nativeElement || !this.messages.length)
      return null;

    const container = this.messagesContainer.nativeElement;
    const messageElements = container.querySelectorAll('.message-item');

    for (let i = 0; i < messageElements.length; i++) {
      const element = messageElements[i];
      const rect = element.getBoundingClientRect();

      // Si l'élément est visible dans la vue
      if (rect.top >= 0 && rect.bottom <= container.clientHeight) {
        const messageId = element.getAttribute('data-message-id');
        return this.messages.find((m) => m.id === messageId) || null;
      }
    }

    return null;
  }

  // Méthode pour trouver un élément de message par ID
  private findMessageElement(
    messageId: string | undefined
  ): HTMLElement | null {
    if (!this.messagesContainer?.nativeElement || !messageId) return null;
    return this.messagesContainer.nativeElement.querySelector(
      `[data-message-id="${messageId}"]`
    );
  }

  // Afficher un indicateur de chargement en haut de la liste
  private showLoadingIndicator(): void {
    // Créer l'indicateur s'il n'existe pas déjà
    if (!document.getElementById('message-loading-indicator')) {
      const indicator = document.createElement('div');
      indicator.id = 'message-loading-indicator';
      indicator.className = 'text-center py-2 text-gray-500 text-sm';
      indicator.innerHTML =
        '<i class="fas fa-spinner fa-spin mr-2"></i> Loading older messages...';

      if (this.messagesContainer?.nativeElement) {
        this.messagesContainer.nativeElement.prepend(indicator);
      }
    }
  }

  // Masquer l'indicateur de chargement
  private hideLoadingIndicator(): void {
    const indicator = document.getElementById('message-loading-indicator');
    if (indicator && indicator.parentNode) {
      indicator.parentNode.removeChild(indicator);
    }
  }

  // Méthode pour charger plus de messages (style Facebook Messenger)
  loadMoreMessages(): void {
    if (this.isLoadingMore || !this.conversation?.id || !this.hasMoreMessages)
      return;

    // Marquer comme chargement en cours
    this.isLoadingMore = true;

    // Augmenter la page pour charger les messages plus anciens
    this.currentPage++;

    // Charger plus de messages depuis le serveur avec pagination
    this.MessageService.getConversation(
      this.conversation.id,
      this.MAX_MESSAGES_TO_LOAD,
      this.currentPage
    ).subscribe({
      next: (conversation) => {
        if (
          conversation &&
          conversation.messages &&
          conversation.messages.length > 0
        ) {
          // Sauvegarder les messages actuels
          const oldMessages = [...this.messages];

          // Créer un Set des IDs existants pour une recherche de doublons plus rapide
          const existingIds = new Set(oldMessages.map((msg) => msg.id));

          // Filtrer et trier les nouveaux messages plus efficacement
          const newMessages = conversation.messages
            .filter((msg) => !existingIds.has(msg.id))
            .sort((a, b) => {
              const timeA = new Date(a.timestamp as string).getTime();
              const timeB = new Date(b.timestamp as string).getTime();
              return timeA - timeB;
            });

          if (newMessages.length > 0) {
            // Ajouter les nouveaux messages au début de la liste
            this.messages = [...newMessages, ...oldMessages];

            // Limiter le nombre total de messages pour éviter les problèmes de performance
            if (this.messages.length > this.MAX_TOTAL_MESSAGES) {
              this.messages = this.messages.slice(0, this.MAX_TOTAL_MESSAGES);
            }

            // Vérifier s'il y a plus de messages à charger
            this.hasMoreMessages =
              newMessages.length >= this.MAX_MESSAGES_TO_LOAD;
          } else {
            // Si aucun nouveau message n'est chargé, c'est qu'on a atteint le début de la conversation
            this.hasMoreMessages = false;
          }
        } else {
          this.hasMoreMessages = false;
        }

        // Désactiver le flag de chargement après un court délai
        // pour permettre au DOM de se mettre à jour
        setTimeout(() => {
          this.isLoadingMore = false;
        }, 200);
      },
      error: (error) => {
        this.logger.error('MessageChat', 'Error loading more messages:', error);
        this.isLoadingMore = false;
        this.hideLoadingIndicator();
        this.toastService.showError('Failed to load more messages');
      },
    });
  }

  // Méthode utilitaire pour comparer les timestamps
  private isSameTimestamp(
    timestamp1: string | Date | undefined,
    timestamp2: string | Date | undefined
  ): boolean {
    if (!timestamp1 || !timestamp2) return false;

    try {
      const time1 =
        timestamp1 instanceof Date
          ? timestamp1.getTime()
          : new Date(timestamp1 as string).getTime();
      const time2 =
        timestamp2 instanceof Date
          ? timestamp2.getTime()
          : new Date(timestamp2 as string).getTime();
      return Math.abs(time1 - time2) < 1000; // Tolérance d'une seconde
    } catch (error) {
      return false;
    }
  }

  scrollToBottom(force: boolean = false): void {
    try {
      if (!this.messagesContainer?.nativeElement) return;

      // Utiliser requestAnimationFrame pour s'assurer que le DOM est prêt
      requestAnimationFrame(() => {
        const container = this.messagesContainer.nativeElement;
        const isScrolledToBottom =
          container.scrollHeight - container.clientHeight <=
          container.scrollTop + 150;

        // Faire défiler vers le bas si:
        // - force est true (pour les nouveaux messages envoyés par l'utilisateur)
        // - ou si l'utilisateur est déjà proche du bas
        if (force || isScrolledToBottom) {
          // Utiliser une animation fluide pour le défilement (comme dans Messenger)
          container.scrollTo({
            top: container.scrollHeight,
            behavior: 'smooth',
          });
        }
      });
    } catch (err) {
      this.logger.error('MessageChat', 'Error scrolling to bottom:', err);
    }
  }

  // Méthode pour ouvrir l'image en plein écran (style Messenger)
  /**
   * Active/désactive l'enregistrement vocal
   */
  toggleVoiceRecording(): void {
    this.isRecordingVoice = !this.isRecordingVoice;

    if (!this.isRecordingVoice) {
      // Si on désactive l'enregistrement, réinitialiser la durée
      this.voiceRecordingDuration = 0;
    }
  }

  /**
   * Gère la fin de l'enregistrement vocal
   * @param audioBlob Blob audio enregistré
   */
  onVoiceRecordingComplete(audioBlob: Blob): void {
    this.logger.debug(
      'MessageChat',
      'Voice recording complete, size:',
      audioBlob.size
    );

    if (!this.conversation?.id && !this.otherParticipant?.id) {
      this.toastService.showError('No conversation or recipient selected');
      this.isRecordingVoice = false;
      return;
    }

    // Récupérer l'ID du destinataire
    const receiverId = this.otherParticipant?.id || '';

    // Envoyer le message vocal
    this.MessageService.sendVoiceMessage(
      receiverId,
      audioBlob,
      this.conversation?.id,
      this.voiceRecordingDuration
    ).subscribe({
      next: (message) => {
        this.logger.debug('MessageChat', 'Voice message sent:', message);
        this.isRecordingVoice = false;
        this.voiceRecordingDuration = 0;
        this.scrollToBottom(true);
      },
      error: (error) => {
        this.logger.error('MessageChat', 'Error sending voice message:', error);
        this.toastService.showError('Failed to send voice message');
        this.isRecordingVoice = false;
      },
    });
  }

  /**
   * Gère l'annulation de l'enregistrement vocal
   */
  onVoiceRecordingCancelled(): void {
    this.logger.debug('MessageChat', 'Voice recording cancelled');
    this.isRecordingVoice = false;
    this.voiceRecordingDuration = 0;
  }

  /**
   * Ouvre une image en plein écran (méthode conservée pour compatibilité)
   * @param imageUrl URL de l'image à afficher
   */
  openImageFullscreen(imageUrl: string): void {
    // Ouvrir l'image dans un nouvel onglet
    window.open(imageUrl, '_blank');
    this.logger.debug('MessageChat', `Image opened in new tab: ${imageUrl}`);
  }

  /**
   * Détecte les changements après chaque vérification de la vue
   * Cela permet de s'assurer que les messages vocaux sont correctement affichés
   * et que le défilement est maintenu
   */
  ngAfterViewChecked(): void {
    // Faire défiler vers le bas si nécessaire
    this.scrollToBottom();

    // Forcer la détection des changements pour les messages vocaux
    // Cela garantit que les messages vocaux sont correctement affichés même après avoir quitté la conversation
    if (this.messages.some((msg) => msg.type === MessageType.VOICE_MESSAGE)) {
      // Utiliser setTimeout pour éviter l'erreur ExpressionChangedAfterItHasBeenCheckedError
      setTimeout(() => {
        this.cdr.detectChanges();
      }, 0);
    }
  }

  /**
   * Arrête l'indicateur de frappe
   */
  private stopTypingIndicator(): void {
    if (this.isCurrentlyTyping && this.conversation?.id) {
      this.isCurrentlyTyping = false;
      clearTimeout(this.typingTimer);

      this.logger.debug('MessageChat', 'Stopping typing indicator');

      // Utiliser l'opérateur de chaînage optionnel pour éviter les erreurs TypeScript
      const conversationId = this.conversation?.id;
      if (conversationId) {
        this.MessageService.stopTyping(conversationId).subscribe({
          next: () => {
            this.logger.debug(
              'MessageChat',
              'Typing indicator stopped successfully'
            );
          },
          error: (error) => {
            this.logger.error(
              'MessageChat',
              'Error stopping typing indicator:',
              error
            );
          },
        });
      }
    }
  }

  /**
   * Navigue vers la liste des conversations
   */
  goBackToConversations(): void {
    this.router.navigate(['/messages/conversations']);
  }

  /**
   * Bascule l'affichage du sélecteur d'émojis
   */
  toggleEmojiPicker(): void {
    this.showEmojiPicker = !this.showEmojiPicker;
    if (this.showEmojiPicker) {
      this.showThemeSelector = false;
    }
  }

  /**
   * Insère un emoji dans le champ de message
   * @param emoji Emoji à insérer
   */
  insertEmoji(emoji: string): void {
    const control = this.messageForm.get('content');
    if (control) {
      const currentValue = control.value || '';
      control.setValue(currentValue + emoji);
      control.markAsDirty();
      // Garder le focus sur le champ de saisie
      setTimeout(() => {
        const inputElement = document.querySelector(
          '.whatsapp-input-field'
        ) as HTMLInputElement;
        if (inputElement) {
          inputElement.focus();
        }
      }, 0);
    }
  }

  /**
   * S'abonne aux notifications en temps réel
   */
  private subscribeToNotifications(): void {
    // S'abonner aux nouvelles notifications
    const notificationSub =
      this.MessageService.subscribeToNewNotifications().subscribe({
        next: (notification) => {
          this.logger.debug(
            'MessageChat',
            `Nouvelle notification reçue: ${notification.type}`
          );

          // Ajouter la notification à la liste locale
          this.notifications.unshift(notification);
          this.updateNotificationCount();

          // Jouer le son si activé
          if (this.notificationSounds) {
            this.MessageService.play('notification');
          }

          // Si c'est une notification de message et que nous sommes dans la conversation concernée
          if (
            notification.type === 'NEW_MESSAGE' &&
            notification.conversationId === this.conversation?.id
          ) {
            // Marquer automatiquement comme lue si l'option est activée
            if (notification.id && this.autoMarkAsRead) {
              this.MessageService.markAsRead([notification.id]).subscribe();
            }
          }
        },
        error: (error) => {
          this.logger.error(
            'MessageChat',
            'Erreur lors de la réception des notifications:',
            error
          );
        },
      });
    this.subscriptions.add(notificationSub);

    // S'abonner à la liste des notifications
    const notificationsListSub = this.MessageService.notifications$.subscribe({
      next: (notifications) => {
        this.notifications = notifications;
        this.updateNotificationCount();
        this.logger.debug(
          'MessageChat',
          `Loaded ${notifications.length} notifications`
        );
      },
      error: (error) => {
        this.logger.error('MessageChat', 'Error loading notifications:', error);
      },
    });
    this.subscriptions.add(notificationsListSub);

    // S'abonner au compteur de notifications non lues
    const notificationCountSub =
      this.MessageService.notificationCount$.subscribe({
        next: (count) => {
          this.unreadNotificationCount = count;
          this.logger.debug(
            'MessageChat',
            `Unread notification count: ${count}`
          );
        },
      });
    this.subscriptions.add(notificationCountSub);

    // S'abonner aux appels entrants
    const callSub = this.MessageService.incomingCall$.subscribe({
      next: (call) => {
        if (call) {
          this.logger.debug(
            'MessageChat',
            `Appel entrant de: ${call.caller.username}`
          );
          this.incomingCall = call;
          this.showCallModal = true;

          // Jouer la sonnerie
          this.MessageService.play('ringtone');
        } else {
          this.showCallModal = false;
          this.incomingCall = null;
        }
      },
    });
    this.subscriptions.add(callSub);

    // S'abonner aux appels actifs
    const activeCallSub = this.MessageService.activeCall$.subscribe({
      next: (call) => {
        this.activeCall = call;
        if (call) {
          this.showActiveCallModal = true;
          this.startCallTimerMethod();
          this.logger.debug('MessageChat', `Appel actif: ${call.id}`);
        } else {
          this.showActiveCallModal = false;
          this.stopCallTimerMethod();
          this.resetCallStateMethod();
        }
      },
    });
    this.subscriptions.add(activeCallSub);

    // S'abonner aux flux vidéo locaux
    const localStreamSub = this.MessageService.localStream$.subscribe({
      next: (stream: MediaStream | null) => {
        if (stream && this.localVideoElement) {
          this.localVideoElement.srcObject = stream;
        }
      },
    });
    this.subscriptions.add(localStreamSub);

    // S'abonner aux flux vidéo distants
    const remoteStreamSub = this.MessageService.remoteStream$.subscribe({
      next: (stream: MediaStream | null) => {
        if (stream && this.remoteVideoElement) {
          this.remoteVideoElement.srcObject = stream;
        }
      },
    });
    this.subscriptions.add(remoteStreamSub);
  }

  /**
   * Initie un appel audio ou vidéo avec l'autre participant
   * @param type Type d'appel (AUDIO ou VIDEO)
   */
  initiateCall(type: 'AUDIO' | 'VIDEO'): void {
    if (!this.otherParticipant || !this.otherParticipant.id) {
      console.error("Impossible d'initier un appel: participant invalide");
      return;
    }

    this.logger.info(
      'MessageChat',
      `Initiation d'un appel ${type} avec ${this.otherParticipant.username}`
    );

    // Utiliser le service d'appel pour initier l'appel
    this.MessageService.initiateCall(
      this.otherParticipant.id,
      type === 'AUDIO' ? CallType.AUDIO : CallType.VIDEO,
      this.conversation?.id
    ).subscribe({
      next: (call) => {
        this.logger.info('MessageChat', 'Appel initié avec succès:', call);
        // Ici, vous pourriez ouvrir une fenêtre d'appel ou rediriger vers une page d'appel
      },
      error: (error) => {
        this.logger.error(
          'MessageChat',
          "Erreur lors de l'initiation de l'appel:",
          error
        );
        this.toastService.showError(
          "Impossible d'initier l'appel. Veuillez réessayer."
        );
      },
    });
  }

  /**
   * Accepte un appel entrant
   */
  acceptCall(): void {
    if (!this.incomingCall) {
      this.logger.error('MessageChat', 'Aucun appel entrant à accepter');
      return;
    }

    this.logger.info(
      'MessageChat',
      `Acceptation de l'appel de ${this.incomingCall.caller.username}`
    );

    this.MessageService.acceptCall(this.incomingCall.id).subscribe({
      next: (call) => {
        this.logger.info('MessageChat', 'Appel accepté avec succès:', call);
        this.showCallModal = false;
        this.incomingCall = null;

        // L'appel actif sera géré par la souscription activeCall$
        this.isVideoEnabled = this.incomingCall?.type === 'VIDEO';
        this.callQuality = 'connecting';

        this.toastService.showSuccess('Appel connecté');
      },
      error: (error) => {
        this.logger.error(
          'MessageChat',
          "Erreur lors de l'acceptation de l'appel:",
          error
        );
        this.toastService.showError(
          "Impossible d'accepter l'appel. Veuillez réessayer."
        );
        this.showCallModal = false;
        this.incomingCall = null;
      },
    });
  }

  /**
   * Rejette un appel entrant
   */
  rejectCall(): void {
    if (!this.incomingCall) {
      this.logger.error('MessageChat', 'Aucun appel entrant à rejeter');
      return;
    }

    this.logger.info(
      'MessageChat',
      `Rejet de l'appel de ${this.incomingCall.caller.username}`
    );

    this.MessageService.rejectCall(this.incomingCall.id).subscribe({
      next: (call) => {
        this.logger.info('MessageChat', 'Appel rejeté avec succès:', call);
        this.showCallModal = false;
        this.incomingCall = null;
      },
      error: (error) => {
        this.logger.error(
          'MessageChat',
          "Erreur lors du rejet de l'appel:",
          error
        );
        this.showCallModal = false;
        this.incomingCall = null;
      },
    });
  }

  /**
   * Termine un appel en cours
   */
  endCall(): void {
    // Utiliser une variable pour stocker la dernière valeur de l'observable
    let activeCall: any = null;

    // S'abonner à l'observable pour obtenir la valeur actuelle
    const sub = this.MessageService.activeCall$.subscribe((call) => {
      activeCall = call;

      if (!activeCall) {
        this.logger.error('MessageChat', 'Aucun appel actif à terminer');
        return;
      }

      this.logger.info('MessageChat', `Fin de l'appel`);

      this.MessageService.endCall(activeCall.id).subscribe({
        next: (call) => {
          this.logger.info('MessageChat', 'Appel terminé avec succès:', call);
        },
        error: (error) => {
          this.logger.error(
            'MessageChat',
            "Erreur lors de la fin de l'appel:",
            error
          );
        },
      });
    });

    // Se désabonner immédiatement après avoir obtenu la valeur
    sub.unsubscribe();
  }

  /**
   * Active/désactive le son de l'appel
   */
  toggleCallMute(): void {
    this.isCallMuted = !this.isCallMuted;

    // Utiliser la méthode publique du service
    this.MessageService.toggleMedia(
      this.activeCall?.id,
      !this.isCallMuted,
      this.isVideoEnabled
    ).subscribe({
      next: (result: any) => {
        this.logger.debug('MessageChat', 'Audio toggle result:', result);
      },
      error: (error: any) => {
        this.logger.error('MessageChat', 'Error toggling audio:', error);
      },
    });

    this.logger.debug(
      'MessageChat',
      `Appel ${this.isCallMuted ? 'muet' : 'son activé'}`
    );
    this.toastService.showInfo(
      this.isCallMuted ? 'Microphone désactivé' : 'Microphone activé'
    );
  }

  /**
   * Active/désactive la vidéo de l'appel
   */
  toggleCallVideo(): void {
    this.isVideoEnabled = !this.isVideoEnabled;

    // Utiliser la méthode publique du service
    this.MessageService.toggleMedia(
      this.activeCall?.id,
      !this.isCallMuted,
      this.isVideoEnabled
    ).subscribe({
      next: (result: any) => {
        this.logger.debug('MessageChat', 'Video toggle result:', result);
      },
      error: (error: any) => {
        this.logger.error('MessageChat', 'Error toggling video:', error);
      },
    });

    this.logger.debug(
      'MessageChat',
      `Vidéo ${this.isVideoEnabled ? 'activée' : 'désactivée'}`
    );
    this.toastService.showInfo(
      this.isVideoEnabled ? 'Caméra activée' : 'Caméra désactivée'
    );
  }

  /**
   * Minimise/maximise la fenêtre d'appel
   */
  toggleCallMinimize(): void {
    this.isCallMinimized = !this.isCallMinimized;
    this.logger.debug(
      'MessageChat',
      `Appel ${this.isCallMinimized ? 'minimisé' : 'maximisé'}`
    );
  }

  /**
   * Démarre le timer de l'appel
   */
  private startCallTimerMethod(): void {
    this.callDuration = 0;
    this.callTimer = setInterval(() => {
      this.callDuration++;

      // Mettre à jour la qualité de l'appel après 3 secondes
      if (this.callDuration === 3 && this.callQuality === 'connecting') {
        this.callQuality = 'excellent';
      }
    }, 1000);
  }

  /**
   * Arrête le timer de l'appel
   */
  private stopCallTimerMethod(): void {
    if (this.callTimer) {
      clearInterval(this.callTimer);
      this.callTimer = null;
    }
  }

  /**
   * Remet à zéro l'état de l'appel
   */
  private resetCallStateMethod(): void {
    this.callDuration = 0;
    this.isCallMuted = false;
    this.isVideoEnabled = true;
    this.isCallMinimized = false;
    this.callQuality = 'connecting';
    this.showCallControls = true;

    if (this.callControlsTimeout) {
      clearTimeout(this.callControlsTimeout);
      this.callControlsTimeout = null;
    }
  }

  /**
   * Formate la durée de l'appel en format MM:SS
   */
  formatCallDuration(seconds: number): string {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds
      .toString()
      .padStart(2, '0')}`;
  }

  /**
   * Gère le survol de la fenêtre d'appel pour afficher/masquer les contrôles
   */
  onCallMouseMove(): void {
    this.showCallControls = true;

    if (this.callControlsTimeout) {
      clearTimeout(this.callControlsTimeout);
    }

    this.callControlsTimeout = setTimeout(() => {
      this.showCallControls = false;
    }, 3000);
  }

  /**
   * Définit les éléments vidéo pour les flux locaux et distants
   */
  setVideoElements(
    localVideo: HTMLVideoElement,
    remoteVideo: HTMLVideoElement
  ): void {
    this.localVideoElement = localVideo;
    this.remoteVideoElement = remoteVideo;

    // Les flux seront connectés via les souscriptions aux observables
    this.logger.debug('MessageChat', 'Video elements set for call interface');
  }

  // --------------------------------------------------------------------------
  // Section: Gestion Avancée des Notifications
  // --------------------------------------------------------------------------

  /**
   * Affiche/masque le panneau de notifications
   */
  toggleNotificationPanel(): void {
    this.showNotificationPanel = !this.showNotificationPanel;

    if (this.showNotificationPanel) {
      this.loadNotifications();
      // Fermer les autres panneaux
      this.showEmojiPicker = false;
      this.showThemeSelector = false;
      this.showNotificationSettings = false;
    }

    this.logger.debug(
      'MessageChat',
      `Notification panel toggled: ${this.showNotificationPanel}`
    );
  }

  /**
   * Charge les notifications
   */
  loadNotifications(refresh: boolean = false): void {
    if (this.isLoadingNotifications) return;

    this.isLoadingNotifications = true;

    if (refresh) {
      this.notificationPage = 1;
      this.hasMoreNotifications = true;
    }

    const loadSub = this.MessageService.getNotifications(
      refresh,
      this.notificationPage,
      20
    ).subscribe({
      next: (notifications) => {
        if (refresh) {
          this.notifications = notifications;
        } else {
          this.notifications = [...this.notifications, ...notifications];
        }

        this.hasMoreNotifications = this.MessageService.hasMoreNotifications();
        this.updateNotificationCount();
        this.isLoadingNotifications = false;

        this.logger.debug(
          'MessageChat',
          `Loaded ${notifications.length} notifications`
        );
      },
      error: (error) => {
        this.logger.error('MessageChat', 'Error loading notifications:', error);
        this.isLoadingNotifications = false;
        this.toastService.showError(
          'Erreur lors du chargement des notifications'
        );
      },
    });

    this.subscriptions.add(loadSub);
  }

  /**
   * Charge plus de notifications (pagination)
   */
  loadMoreNotifications(): void {
    if (!this.hasMoreNotifications || this.isLoadingNotifications) return;

    this.notificationPage++;
    this.loadNotifications();
  }

  /**
   * Met à jour le compteur de notifications non lues
   */
  private updateNotificationCount(): void {
    this.unreadNotificationCount = this.notifications.filter(
      (n) => !n.isRead
    ).length;
  }

  /**
   * Filtre les notifications selon le type sélectionné
   */
  getFilteredNotifications(): any[] {
    switch (this.notificationFilter) {
      case 'unread':
        return this.notifications.filter((n) => !n.isRead);
      case 'read':
        return this.notifications.filter((n) => n.isRead);
      default:
        return this.notifications;
    }
  }

  /**
   * Change le filtre des notifications
   */
  setNotificationFilter(filter: 'all' | 'unread' | 'read'): void {
    this.notificationFilter = filter;
    this.logger.debug('MessageChat', `Notification filter set to: ${filter}`);
  }

  /**
   * Sélectionne/désélectionne une notification
   */
  toggleNotificationSelection(notificationId: string): void {
    if (this.selectedNotifications.has(notificationId)) {
      this.selectedNotifications.delete(notificationId);
    } else {
      this.selectedNotifications.add(notificationId);
    }

    this.logger.debug(
      'MessageChat',
      `Notification ${notificationId} selection toggled`
    );
  }

  /**
   * Sélectionne/désélectionne toutes les notifications visibles
   */
  toggleSelectAllNotifications(): void {
    const filteredNotifications = this.getFilteredNotifications();
    const allSelected = filteredNotifications.every((n) =>
      this.selectedNotifications.has(n.id)
    );

    if (allSelected) {
      // Désélectionner toutes
      filteredNotifications.forEach((n) =>
        this.selectedNotifications.delete(n.id)
      );
    } else {
      // Sélectionner toutes
      filteredNotifications.forEach((n) =>
        this.selectedNotifications.add(n.id)
      );
    }

    this.logger.debug(
      'MessageChat',
      `All notifications selection toggled: ${!allSelected}`
    );
  }

  /**
   * Vérifie si toutes les notifications visibles sont sélectionnées
   */
  areAllNotificationsSelected(): boolean {
    const filteredNotifications = this.getFilteredNotifications();
    return (
      filteredNotifications.length > 0 &&
      filteredNotifications.every((n) => this.selectedNotifications.has(n.id))
    );
  }

  /**
   * Marque les notifications sélectionnées comme lues
   */
  markSelectedAsRead(): void {
    const selectedIds = Array.from(this.selectedNotifications);
    if (selectedIds.length === 0) {
      this.toastService.showWarning('Aucune notification sélectionnée');
      return;
    }

    this.isMarkingAsRead = true;

    const markSub = this.MessageService.markAsRead(selectedIds).subscribe({
      next: (result) => {
        this.logger.info(
          'MessageChat',
          `Marked ${result.readCount} notifications as read`
        );

        // Mettre à jour localement
        this.notifications = this.notifications.map((n) =>
          selectedIds.includes(n.id)
            ? { ...n, isRead: true, readAt: new Date() }
            : n
        );

        this.selectedNotifications.clear();
        this.updateNotificationCount();
        this.isMarkingAsRead = false;

        this.toastService.showSuccess(
          `${result.readCount} notification(s) marquée(s) comme lue(s)`
        );
      },
      error: (error) => {
        this.logger.error(
          'MessageChat',
          'Error marking notifications as read:',
          error
        );
        this.isMarkingAsRead = false;
        this.toastService.showError(
          'Erreur lors du marquage des notifications'
        );
      },
    });

    this.subscriptions.add(markSub);
  }

  /**
   * Marque toutes les notifications comme lues
   */
  markAllAsRead(): void {
    const unreadNotifications = this.notifications.filter((n) => !n.isRead);
    if (unreadNotifications.length === 0) {
      this.toastService.showInfo('Aucune notification non lue');
      return;
    }

    const unreadIds = unreadNotifications.map((n) => n.id);
    this.isMarkingAsRead = true;

    const markSub = this.MessageService.markAsRead(unreadIds).subscribe({
      next: (result) => {
        this.logger.info(
          'MessageChat',
          `Marked all ${result.readCount} notifications as read`
        );

        // Mettre à jour localement
        this.notifications = this.notifications.map((n) =>
          unreadIds.includes(n.id)
            ? { ...n, isRead: true, readAt: new Date() }
            : n
        );

        this.updateNotificationCount();
        this.isMarkingAsRead = false;

        this.toastService.showSuccess(
          'Toutes les notifications ont été marquées comme lues'
        );
      },
      error: (error) => {
        this.logger.error(
          'MessageChat',
          'Error marking all notifications as read:',
          error
        );
        this.isMarkingAsRead = false;
        this.toastService.showError(
          'Erreur lors du marquage des notifications'
        );
      },
    });

    this.subscriptions.add(markSub);
  }

  /**
   * Affiche la confirmation de suppression des notifications sélectionnées
   */
  showDeleteSelectedConfirmation(): void {
    if (this.selectedNotifications.size === 0) {
      this.toastService.showWarning('Aucune notification sélectionnée');
      return;
    }

    this.showDeleteConfirmModal = true;
  }

  /**
   * Supprime les notifications sélectionnées
   */
  deleteSelectedNotifications(): void {
    const selectedIds = Array.from(this.selectedNotifications);
    if (selectedIds.length === 0) return;

    this.isDeletingNotifications = true;
    this.showDeleteConfirmModal = false;

    const deleteSub = this.MessageService.deleteMultipleNotifications(
      selectedIds
    ).subscribe({
      next: (result) => {
        this.logger.info(
          'MessageChat',
          `Deleted ${result.count} notifications`
        );

        // Supprimer localement
        this.notifications = this.notifications.filter(
          (n) => !selectedIds.includes(n.id)
        );
        this.selectedNotifications.clear();
        this.updateNotificationCount();
        this.isDeletingNotifications = false;

        this.toastService.showSuccess(
          `${result.count} notification(s) supprimée(s)`
        );
      },
      error: (error) => {
        this.logger.error(
          'MessageChat',
          'Error deleting notifications:',
          error
        );
        this.isDeletingNotifications = false;
        this.toastService.showError(
          'Erreur lors de la suppression des notifications'
        );
      },
    });

    this.subscriptions.add(deleteSub);
  }

  /**
   * Supprime une notification individuelle
   */
  deleteNotification(notificationId: string): void {
    const deleteSub = this.MessageService.deleteNotification(
      notificationId
    ).subscribe({
      next: (result) => {
        this.logger.info(
          'MessageChat',
          `Notification ${notificationId} deleted`
        );

        // Supprimer localement
        this.notifications = this.notifications.filter(
          (n) => n.id !== notificationId
        );
        this.selectedNotifications.delete(notificationId);
        this.updateNotificationCount();

        this.toastService.showSuccess('Notification supprimée');
      },
      error: (error) => {
        this.logger.error('MessageChat', 'Error deleting notification:', error);
        this.toastService.showError(
          'Erreur lors de la suppression de la notification'
        );
      },
    });

    this.subscriptions.add(deleteSub);
  }

  /**
   * Annule la suppression des notifications
   */
  cancelDeleteNotifications(): void {
    this.showDeleteConfirmModal = false;
  }

  /**
   * Affiche/masque les paramètres de notification
   */
  toggleNotificationSettings(): void {
    this.showNotificationSettings = !this.showNotificationSettings;

    if (this.showNotificationSettings) {
      // Charger les paramètres sauvegardés
      this.loadNotificationSettings();
    }
  }

  /**
   * Charge les paramètres de notification depuis le localStorage
   */
  private loadNotificationSettings(): void {
    this.notificationSounds =
      localStorage.getItem('notificationSounds') !== 'false';
    this.notificationPreview =
      localStorage.getItem('notificationPreview') !== 'false';
    this.autoMarkAsRead = localStorage.getItem('autoMarkAsRead') !== 'false';
  }

  /**
   * Sauvegarde les paramètres de notification
   */
  saveNotificationSettings(): void {
    localStorage.setItem(
      'notificationSounds',
      this.notificationSounds.toString()
    );
    localStorage.setItem(
      'notificationPreview',
      this.notificationPreview.toString()
    );
    localStorage.setItem('autoMarkAsRead', this.autoMarkAsRead.toString());

    this.toastService.showSuccess('Paramètres de notification sauvegardés');
    this.logger.debug('MessageChat', 'Notification settings saved');
  }

  /**
   * Formate la date d'une notification
   */
  formatNotificationDate(timestamp: string | Date): string {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      const diffInMinutes = Math.floor(diffInHours * 60);
      return diffInMinutes <= 1 ? "À l'instant" : `Il y a ${diffInMinutes} min`;
    } else if (diffInHours < 24) {
      return `Il y a ${Math.floor(diffInHours)} h`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return diffInDays === 1 ? 'Hier' : `Il y a ${diffInDays} jours`;
    }
  }

  /**
   * Obtient l'icône d'une notification selon son type
   */
  getNotificationIcon(type: string): string {
    switch (type) {
      case 'NEW_MESSAGE':
        return 'fas fa-comment';
      case 'FRIEND_REQUEST':
        return 'fas fa-user-plus';
      case 'GROUP_INVITATION':
        return 'fas fa-users';
      case 'CALL_MISSED':
        return 'fas fa-phone-slash';
      case 'CALL_INCOMING':
        return 'fas fa-phone';
      case 'SYSTEM':
        return 'fas fa-cog';
      default:
        return 'fas fa-bell';
    }
  }

  /**
   * Obtient la couleur d'une notification selon son type
   */
  getNotificationColor(type: string): string {
    switch (type) {
      case 'NEW_MESSAGE':
        return 'text-blue-500';
      case 'FRIEND_REQUEST':
        return 'text-green-500';
      case 'GROUP_INVITATION':
        return 'text-purple-500';
      case 'CALL_MISSED':
        return 'text-red-500';
      case 'CALL_INCOMING':
        return 'text-yellow-500';
      case 'SYSTEM':
        return 'text-gray-500';
      default:
        return 'text-cyan-500';
    }
  }

  /**
   * TrackBy function pour optimiser le rendu de la liste des notifications
   */
  trackByNotificationId(index: number, notification: any): string {
    return notification.id;
  }

  // --------------------------------------------------------------------------
  // Section: Statut Utilisateur en Temps Réel
  // --------------------------------------------------------------------------

  /**
   * S'abonne au statut utilisateur en temps réel
   */
  private subscribeToUserStatus(): void {
    const statusSub = this.MessageService.subscribeToUserStatus().subscribe({
      next: (user: User) => {
        this.handleUserStatusUpdate(user);
        this.logger.debug(
          'MessageChat',
          `User status updated: ${user.username} - ${
            user.isOnline ? 'online' : 'offline'
          }`
        );
      },
      error: (error) => {
        this.logger.error(
          'MessageChat',
          'Error in user status subscription:',
          error
        );
      },
    });

    this.subscriptions.add(statusSub);
  }

  /**
   * Gère la mise à jour du statut d'un utilisateur
   */
  private handleUserStatusUpdate(user: User): void {
    if (!user.id) return; // Vérification de sécurité

    // Mettre à jour la carte des utilisateurs en ligne
    if (user.isOnline) {
      this.onlineUsers.set(user.id, user);
    } else {
      this.onlineUsers.delete(user.id);
    }

    // Mettre à jour la carte de statut
    this.userStatusMap.set(user.id, {
      isOnline: user.isOnline || false,
      lastActive: user.lastActive ? new Date(user.lastActive) : null,
    });

    // Mettre à jour l'autre participant si c'est lui
    if (this.otherParticipant && this.otherParticipant.id === user.id) {
      this.otherParticipant = { ...this.otherParticipant, ...user };
      this.logger.debug(
        'MessageChat',
        `Updated other participant status: ${
          user.isOnline ? 'online' : 'offline'
        }`
      );
    }

    // Ajouter à l'historique de statut
    this.addToStatusHistory(user.id, user.isOnline ? 'online' : 'offline');

    // Mettre à jour le tracker d'activité
    this.userActivityTracker.set(user.id, new Date());

    // Traiter la file d'attente des mises à jour de présence
    this.processPresenceUpdateQueue();
  }

  /**
   * Initialise le statut de l'utilisateur actuel
   */
  private initializeUserStatus(): void {
    if (!this.currentUserId) return;

    this.isUpdatingStatus = true;

    // Définir l'utilisateur comme en ligne
    const setOnlineSub = this.MessageService.setUserOnline(
      this.currentUserId
    ).subscribe({
      next: (user: User) => {
        this.currentUserStatus = 'online';
        this.lastActivityTime = new Date();
        this.isUpdatingStatus = false;

        this.logger.info('MessageChat', 'User status initialized as online');
        this.toastService.showSuccess('Statut mis à jour : En ligne');
      },
      error: (error) => {
        this.isUpdatingStatus = false;
        this.logger.error(
          'MessageChat',
          'Error initializing user status:',
          error
        );
        this.toastService.showError(
          "Erreur lors de l'initialisation du statut"
        );
      },
    });

    this.subscriptions.add(setOnlineSub);
  }

  /**
   * Démarre le suivi d'activité automatique
   */
  private startActivityTracking(): void {
    // Écouter les événements d'activité
    const events = [
      'mousedown',
      'mousemove',
      'keypress',
      'scroll',
      'touchstart',
      'click',
    ];

    events.forEach((event) => {
      document.addEventListener(event, this.onUserActivity.bind(this), true);
    });

    // Démarrer l'intervalle de vérification du statut
    this.statusUpdateInterval = setInterval(() => {
      this.checkAndUpdateStatus();
    }, 30000); // Vérifier toutes les 30 secondes

    this.logger.debug('MessageChat', 'Activity tracking started');
  }

  /**
   * Gère l'activité de l'utilisateur
   */
  private onUserActivity(): void {
    this.lastActivityTime = new Date();

    // Réinitialiser le timer d'absence automatique
    if (this.autoAwayTimeout) {
      clearTimeout(this.autoAwayTimeout);
    }

    // Si l'utilisateur était absent, le remettre en ligne
    if (
      this.currentUserStatus === 'away' ||
      this.currentUserStatus === 'offline'
    ) {
      this.updateUserStatus('online');
    }

    // Programmer la mise en absence automatique
    this.autoAwayTimeout = setTimeout(() => {
      if (this.currentUserStatus === 'online') {
        this.updateUserStatus('away');
      }
    }, this.autoAwayDelay);
  }

  /**
   * Vérifie et met à jour le statut si nécessaire
   */
  private checkAndUpdateStatus(): void {
    const now = new Date();
    const timeSinceLastActivity =
      now.getTime() - this.lastActivityTime.getTime();

    // Si plus de 5 minutes d'inactivité et toujours en ligne
    if (
      timeSinceLastActivity > this.autoAwayDelay &&
      this.currentUserStatus === 'online'
    ) {
      this.updateUserStatus('away');
    }

    // Si plus de 30 minutes d'inactivité et en absence
    else if (
      timeSinceLastActivity > 1800000 &&
      this.currentUserStatus === 'away'
    ) {
      this.updateUserStatus('offline');
    }
  }

  /**
   * Met à jour le statut de l'utilisateur
   */
  updateUserStatus(status: 'online' | 'offline' | 'away' | 'busy'): void {
    if (!this.currentUserId || this.isUpdatingStatus) return;

    this.isUpdatingStatus = true;
    const previousStatus = this.currentUserStatus;

    let updateObservable;
    if (status === 'online') {
      updateObservable = this.MessageService.setUserOnline(this.currentUserId);
    } else {
      updateObservable = this.MessageService.setUserOffline(this.currentUserId);
    }

    const updateSub = updateObservable.subscribe({
      next: (user: User) => {
        this.currentUserStatus = status;
        this.isUpdatingStatus = false;

        // Ajouter à l'historique
        this.addToStatusHistory(this.currentUserId!, status);

        this.logger.info(
          'MessageChat',
          `User status updated from ${previousStatus} to ${status}`
        );

        // Afficher une notification discrète
        if (status !== previousStatus) {
          const statusText = this.getStatusText(status);
          this.toastService.showInfo(`Statut : ${statusText}`);
        }
      },
      error: (error) => {
        this.isUpdatingStatus = false;
        this.logger.error('MessageChat', 'Error updating user status:', error);
        this.toastService.showError('Erreur lors de la mise à jour du statut');
      },
    });

    this.subscriptions.add(updateSub);
  }

  /**
   * Affiche/masque le sélecteur de statut
   */
  toggleStatusSelector(): void {
    this.showStatusSelector = !this.showStatusSelector;

    if (this.showStatusSelector) {
      // Fermer les autres panneaux
      this.showNotificationPanel = false;
      this.showThemeSelector = false;
      this.showEmojiPicker = false;
    }
  }

  /**
   * Affiche/masque le panneau de statut détaillé
   */
  toggleUserStatusPanel(): void {
    this.showUserStatusPanel = !this.showUserStatusPanel;

    if (this.showUserStatusPanel) {
      this.loadOnlineUsers();
      // Fermer les autres panneaux
      this.showNotificationPanel = false;
      this.showThemeSelector = false;
      this.showEmojiPicker = false;
    }
  }

  /**
   * Charge la liste des utilisateurs en ligne
   */
  loadOnlineUsers(): void {
    const usersSub = this.MessageService.getAllUsers(
      false,
      undefined,
      1,
      50,
      'username',
      'asc',
      true
    ).subscribe({
      next: (users: User[]) => {
        users.forEach((user) => {
          if (user.isOnline && user.id) {
            this.onlineUsers.set(user.id, user);
            this.userStatusMap.set(user.id, {
              isOnline: true,
              lastActive: user.lastActive ? new Date(user.lastActive) : null,
            });
          }
        });

        this.logger.debug('MessageChat', `Loaded ${users.length} online users`);
      },
      error: (error) => {
        this.logger.error('MessageChat', 'Error loading online users:', error);
      },
    });

    this.subscriptions.add(usersSub);
  }

  /**
   * Obtient la liste des utilisateurs filtrés par statut
   */
  getFilteredUsers(): User[] {
    const allUsers = Array.from(this.onlineUsers.values());

    switch (this.statusFilterType) {
      case 'online':
        return allUsers.filter((user) => user.isOnline);
      case 'offline':
        return allUsers.filter((user) => !user.isOnline);
      case 'away':
        return allUsers.filter((user) => {
          if (!user.id) return false;
          const status = this.userStatusMap.get(user.id);
          return status && !status.isOnline && this.isUserAway(user.id);
        });
      case 'busy':
        return allUsers.filter((user) => {
          if (!user.id) return false;
          const status = this.userStatusMap.get(user.id);
          return status && status.isOnline && this.isUserBusy(user.id);
        });
      default:
        return allUsers;
    }
  }

  /**
   * Définit le filtre de statut
   */
  setStatusFilter(
    filter: 'all' | 'online' | 'offline' | 'away' | 'busy'
  ): void {
    this.statusFilterType = filter;
    this.logger.debug('MessageChat', `Status filter set to: ${filter}`);
  }

  /**
   * Vérifie si un utilisateur est en absence
   */
  private isUserAway(userId: string): boolean {
    const lastActivity = this.userActivityTracker.get(userId);
    if (!lastActivity) return false;

    const now = new Date();
    const timeSinceActivity = now.getTime() - lastActivity.getTime();
    return (
      timeSinceActivity > this.autoAwayDelay && timeSinceActivity < 1800000
    ); // Entre 5 et 30 minutes
  }

  /**
   * Vérifie si un utilisateur est occupé
   */
  private isUserBusy(userId: string): boolean {
    // Logique pour déterminer si l'utilisateur est occupé
    // Par exemple, s'il est dans un appel
    return (
      this.activeCall &&
      (this.activeCall.callerId === userId ||
        this.activeCall.receiverId === userId)
    );
  }

  /**
   * Ajoute une entrée à l'historique de statut
   */
  private addToStatusHistory(userId: string, status: string): void {
    this.statusHistory.unshift({
      userId,
      status,
      timestamp: new Date(),
    });

    // Garder seulement les 100 dernières entrées
    if (this.statusHistory.length > 100) {
      this.statusHistory = this.statusHistory.slice(0, 100);
    }
  }

  /**
   * Traite la file d'attente des mises à jour de présence
   */
  private processPresenceUpdateQueue(): void {
    if (
      this.isProcessingPresenceUpdates ||
      this.presenceUpdateQueue.length === 0
    ) {
      return;
    }

    this.isProcessingPresenceUpdates = true;

    // Traiter les mises à jour par batch
    const batch = this.presenceUpdateQueue.splice(0, 10);

    batch.forEach((update) => {
      this.handleUserStatusUpdate(update.status);
    });

    this.isProcessingPresenceUpdates = false;

    // Continuer le traitement s'il reste des éléments
    if (this.presenceUpdateQueue.length > 0) {
      setTimeout(() => this.processPresenceUpdateQueue(), 100);
    }
  }

  /**
   * Obtient le texte du statut
   */
  getStatusText(status: string): string {
    switch (status) {
      case 'online':
        return 'En ligne';
      case 'offline':
        return 'Hors ligne';
      case 'away':
        return 'Absent';
      case 'busy':
        return 'Occupé';
      default:
        return 'Inconnu';
    }
  }

  /**
   * Obtient la couleur du statut
   */
  getStatusColor(status: string): string {
    switch (status) {
      case 'online':
        return 'text-green-500';
      case 'offline':
        return 'text-gray-500';
      case 'away':
        return 'text-yellow-500';
      case 'busy':
        return 'text-red-500';
      default:
        return 'text-gray-400';
    }
  }

  /**
   * Obtient l'icône du statut
   */
  getStatusIcon(status: string): string {
    switch (status) {
      case 'online':
        return 'fas fa-circle';
      case 'offline':
        return 'far fa-circle';
      case 'away':
        return 'fas fa-clock';
      case 'busy':
        return 'fas fa-minus-circle';
      default:
        return 'fas fa-question-circle';
    }
  }

  /**
   * Formate le temps depuis la dernière activité
   */
  formatLastSeen(lastActive: Date | null): string {
    if (!lastActive) return 'Jamais vu';

    const now = new Date();
    const diffMs = now.getTime() - lastActive.getTime();
    const diffMinutes = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffMs / 86400000);

    if (diffMinutes < 1) {
      return "À l'instant";
    } else if (diffMinutes < 60) {
      return `Il y a ${diffMinutes} min`;
    } else if (diffHours < 24) {
      return `Il y a ${diffHours} h`;
    } else {
      return `Il y a ${diffDays} jour${diffDays > 1 ? 's' : ''}`;
    }
  }

  /**
   * Obtient le nombre d'utilisateurs en ligne
   */
  getOnlineUsersCount(): number {
    return Array.from(this.onlineUsers.values()).filter((user) => user.isOnline)
      .length;
  }

  /**
   * TrackBy function pour optimiser le rendu de la liste des utilisateurs
   */
  trackByUserId(index: number, user: User): string {
    return user.id || index.toString();
  }

  // --------------------------------------------------------------------------
  // Section: Édition et Suppression de Messages
  // --------------------------------------------------------------------------

  /**
   * Affiche/masque les options d'un message (éditer, supprimer)
   * @param messageId ID du message
   */
  toggleMessageOptions(messageId: string): void {
    // Fermer toutes les autres options ouvertes
    Object.keys(this.showMessageOptions).forEach((id) => {
      if (id !== messageId) {
        this.showMessageOptions[id] = false;
      }
    });

    // Basculer l'affichage des options pour ce message
    this.showMessageOptions[messageId] = !this.showMessageOptions[messageId];

    // Fermer la confirmation de suppression si elle est ouverte
    this.showDeleteConfirm[messageId] = false;
  }

  /**
   * Vérifie si l'utilisateur peut éditer/supprimer un message
   * @param message Message à vérifier
   * @returns true si l'utilisateur peut modifier le message
   */
  canEditMessage(message: Message): boolean {
    if (!message || !this.currentUserId) return false;

    // L'utilisateur peut éditer ses propres messages
    const isOwner =
      message.sender?.id === this.currentUserId ||
      message.sender?._id === this.currentUserId;

    // Ne pas permettre l'édition des messages système ou supprimés
    const isEditable =
      message.type !== MessageType.SYSTEM && !message.isDeleted;

    return isOwner && isEditable;
  }

  /**
   * Démarre l'édition d'un message
   * @param message Message à éditer
   */
  startEditMessage(message: Message): void {
    if (!this.canEditMessage(message) || !message.id) return;

    this.editingMessageId = message.id;
    this.editingContent = message.content || '';

    // Fermer les options du message
    this.showMessageOptions[message.id] = false;

    this.logger.debug('MessageChat', `Started editing message: ${message.id}`);
  }

  /**
   * Annule l'édition d'un message
   */
  cancelEditMessage(): void {
    this.editingMessageId = null;
    this.editingContent = '';
    this.logger.debug('MessageChat', 'Cancelled message editing');
  }

  /**
   * Sauvegarde les modifications d'un message
   * @param messageId ID du message à sauvegarder
   */
  saveEditMessage(messageId: string): void {
    if (!messageId || !this.editingContent.trim()) {
      this.toastService.showError(
        'Le contenu du message ne peut pas être vide'
      );
      return;
    }

    this.logger.debug('MessageChat', `Saving edited message: ${messageId}`);

    const editSub = this.MessageService.editMessage(
      messageId,
      this.editingContent.trim()
    ).subscribe({
      next: (updatedMessage) => {
        this.logger.info(
          'MessageChat',
          `Message edited successfully: ${messageId}`
        );

        // Mettre à jour le message dans la liste locale
        this.messages = this.messages.map((msg) =>
          msg.id === messageId
            ? { ...msg, ...updatedMessage, isEdited: true }
            : msg
        );

        // Réinitialiser l'état d'édition
        this.cancelEditMessage();

        this.toastService.showSuccess('Message modifié avec succès');
      },
      error: (error) => {
        this.logger.error('MessageChat', `Error editing message:`, error);
        this.toastService.showError(
          'Erreur lors de la modification du message'
        );
      },
    });

    this.subscriptions.add(editSub);
  }

  /**
   * Affiche la confirmation de suppression d'un message
   * @param messageId ID du message à supprimer
   */
  showDeleteConfirmation(messageId: string): void {
    // Fermer les options du message
    this.showMessageOptions[messageId] = false;

    // Afficher la confirmation de suppression
    this.showDeleteConfirm[messageId] = true;

    this.logger.debug(
      'MessageChat',
      `Showing delete confirmation for message: ${messageId}`
    );
  }

  /**
   * Annule la suppression d'un message
   * @param messageId ID du message
   */
  cancelDeleteMessage(messageId: string): void {
    this.showDeleteConfirm[messageId] = false;
    this.logger.debug(
      'MessageChat',
      `Cancelled delete for message: ${messageId}`
    );
  }

  /**
   * Confirme et supprime un message
   * @param messageId ID du message à supprimer
   */
  confirmDeleteMessage(messageId: string): void {
    if (!messageId) return;

    this.logger.debug('MessageChat', `Deleting message: ${messageId}`);

    const deleteSub = this.MessageService.deleteMessage(messageId).subscribe({
      next: (deletedMessage) => {
        this.logger.info(
          'MessageChat',
          `Message deleted successfully: ${messageId}`
        );

        // Marquer le message comme supprimé dans la liste locale
        this.messages = this.messages.map((msg) =>
          msg.id === messageId
            ? {
                ...msg,
                content: 'Ce message a été supprimé',
                isDeleted: true,
                attachments: [], // Supprimer les pièces jointes
              }
            : msg
        );

        // Fermer la confirmation de suppression
        this.showDeleteConfirm[messageId] = false;

        this.toastService.showSuccess('Message supprimé avec succès');
      },
      error: (error) => {
        this.logger.error('MessageChat', `Error deleting message:`, error);
        this.toastService.showError('Erreur lors de la suppression du message');

        // Fermer la confirmation en cas d'erreur
        this.showDeleteConfirm[messageId] = false;
      },
    });

    this.subscriptions.add(deleteSub);
  }

  /**
   * Gère la touche Entrée lors de l'édition d'un message
   * @param event Événement clavier
   * @param messageId ID du message en cours d'édition
   */
  onEditKeyPress(event: KeyboardEvent, messageId: string): void {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      this.saveEditMessage(messageId);
    } else if (event.key === 'Escape') {
      event.preventDefault();
      this.cancelEditMessage();
    }
  }

  // --------------------------------------------------------------------------
  // Section: Réactions aux Messages
  // --------------------------------------------------------------------------

  /**
   * Affiche/masque le sélecteur de réactions pour un message
   * @param messageId ID du message
   */
  toggleReactionPicker(messageId: string): void {
    // Fermer tous les autres sélecteurs de réactions
    Object.keys(this.showReactionPicker).forEach((id) => {
      if (id !== messageId) {
        this.showReactionPicker[id] = false;
      }
    });

    // Basculer l'affichage du sélecteur pour ce message
    this.showReactionPicker[messageId] = !this.showReactionPicker[messageId];

    // Fermer les options du message si elles sont ouvertes
    this.showMessageOptions[messageId] = false;

    this.logger.debug(
      'MessageChat',
      `Toggled reaction picker for message: ${messageId}`
    );
  }

  /**
   * Ajoute ou supprime une réaction à un message
   * @param messageId ID du message
   * @param emoji Emoji de la réaction
   */
  reactToMessage(messageId: string, emoji: string): void {
    if (!messageId || !emoji) {
      this.logger.warn(
        'MessageChat',
        'Invalid messageId or emoji for reaction'
      );
      return;
    }

    this.logger.debug(
      'MessageChat',
      `Adding reaction ${emoji} to message: ${messageId}`
    );

    const reactionSub = this.MessageService.reactToMessage(
      messageId,
      emoji
    ).subscribe({
      next: (updatedMessage) => {
        this.logger.info(
          'MessageChat',
          `Reaction added successfully to message: ${messageId}`
        );

        // Mettre à jour le message dans la liste locale
        this.messages = this.messages.map((msg) =>
          msg.id === messageId
            ? { ...msg, reactions: updatedMessage.reactions }
            : msg
        );

        // Fermer le sélecteur de réactions
        this.showReactionPicker[messageId] = false;

        this.toastService.showSuccess('Réaction ajoutée');
      },
      error: (error) => {
        this.logger.error('MessageChat', `Error adding reaction:`, error);
        this.toastService.showError("Erreur lors de l'ajout de la réaction");
      },
    });

    this.subscriptions.add(reactionSub);
  }

  /**
   * Supprime une réaction d'un message
   * @param messageId ID du message
   * @param emoji Emoji de la réaction à supprimer
   */
  removeReaction(messageId: string, emoji: string): void {
    if (!messageId || !emoji) {
      this.logger.warn(
        'MessageChat',
        'Invalid messageId or emoji for reaction removal'
      );
      return;
    }

    this.logger.debug(
      'MessageChat',
      `Removing reaction ${emoji} from message: ${messageId}`
    );

    // Utiliser la même fonction reactToMessage - le backend gère la logique de suppression
    const reactionSub = this.MessageService.reactToMessage(
      messageId,
      emoji
    ).subscribe({
      next: (updatedMessage) => {
        this.logger.info(
          'MessageChat',
          `Reaction removed successfully from message: ${messageId}`
        );

        // Mettre à jour le message dans la liste locale
        this.messages = this.messages.map((msg) =>
          msg.id === messageId
            ? { ...msg, reactions: updatedMessage.reactions }
            : msg
        );

        this.toastService.showSuccess('Réaction supprimée');
      },
      error: (error) => {
        this.logger.error('MessageChat', `Error removing reaction:`, error);
        this.toastService.showError(
          'Erreur lors de la suppression de la réaction'
        );
      },
    });

    this.subscriptions.add(reactionSub);
  }

  /**
   * Vérifie si l'utilisateur actuel a déjà réagi avec un emoji spécifique
   * @param message Message à vérifier
   * @param emoji Emoji à vérifier
   * @returns true si l'utilisateur a déjà réagi avec cet emoji
   */
  hasUserReacted(message: Message, emoji: string): boolean {
    if (!message.reactions || !this.currentUserId) return false;

    return message.reactions.some(
      (reaction) =>
        reaction.userId === this.currentUserId && reaction.emoji === emoji
    );
  }

  /**
   * Obtient le nombre de réactions pour un emoji spécifique
   * @param message Message à vérifier
   * @param emoji Emoji à compter
   * @returns Nombre de réactions pour cet emoji
   */
  getReactionCount(message: Message, emoji: string): number {
    if (!message.reactions) return 0;

    return message.reactions.filter((reaction) => reaction.emoji === emoji)
      .length;
  }

  /**
   * Obtient les réactions uniques d'un message avec leur nombre
   * @param message Message à analyser
   * @returns Array des réactions avec leur nombre
   */
  getUniqueReactions(
    message: Message
  ): { emoji: string; count: number; users: string[] }[] {
    if (!message.reactions || message.reactions.length === 0) return [];

    const reactionMap = new Map<string, { count: number; users: string[] }>();

    message.reactions.forEach((reaction) => {
      if (reactionMap.has(reaction.emoji)) {
        const existing = reactionMap.get(reaction.emoji)!;
        existing.count++;
        existing.users.push(reaction.userId);
      } else {
        reactionMap.set(reaction.emoji, {
          count: 1,
          users: [reaction.userId],
        });
      }
    });

    return Array.from(reactionMap.entries()).map(([emoji, data]) => ({
      emoji,
      count: data.count,
      users: data.users,
    }));
  }

  /**
   * Gère le clic sur une réaction existante (ajouter ou supprimer)
   * @param messageId ID du message
   * @param emoji Emoji de la réaction
   */
  onReactionClick(messageId: string, emoji: string): void {
    const message = this.messages.find((msg) => msg.id === messageId);
    if (!message) return;

    if (this.hasUserReacted(message, emoji)) {
      this.removeReaction(messageId, emoji);
    } else {
      this.reactToMessage(messageId, emoji);
    }
  }

  /**
   * Ferme tous les sélecteurs de réactions ouverts
   */
  closeAllReactionPickers(): void {
    Object.keys(this.showReactionPicker).forEach((messageId) => {
      this.showReactionPicker[messageId] = false;
    });
  }

  /**
   * Gestionnaire de clic global pour fermer les sélecteurs de réactions
   * @param event Événement de clic
   */
  onDocumentClick(event: Event): void {
    const target = event.target as HTMLElement;

    // Vérifier si le clic est à l'extérieur des sélecteurs de réactions
    if (
      !target.closest('.reaction-picker') &&
      !target.closest('[data-reaction-trigger]')
    ) {
      this.closeAllReactionPickers();

      // Fermer aussi les options de messages
      Object.keys(this.showMessageOptions).forEach((messageId) => {
        this.showMessageOptions[messageId] = false;
      });
    }
  }

  // --------------------------------------------------------------------------
  // Section: Recherche de Messages
  // --------------------------------------------------------------------------

  /**
   * Affiche/masque la barre de recherche
   */
  toggleSearchBar(): void {
    this.showSearchBar = !this.showSearchBar;

    if (!this.showSearchBar) {
      // Fermer la recherche et revenir aux messages normaux
      this.clearSearch();
    }

    // Fermer les autres éléments ouverts
    this.showEmojiPicker = false;
    this.showThemeSelector = false;
    this.closeAllReactionPickers();

    this.logger.debug(
      'MessageChat',
      `Search bar toggled: ${this.showSearchBar}`
    );
  }

  /**
   * Effectue une recherche de messages
   * @param query Terme de recherche
   */
  searchMessages(query: string): void {
    if (!query || query.trim().length < 2) {
      this.clearSearch();
      return;
    }

    this.searchQuery = query.trim();
    this.isSearching = true;
    this.searchMode = true;

    this.logger.debug('MessageChat', `Searching for: "${this.searchQuery}"`);

    const searchSub = this.MessageService.searchMessages(
      this.searchQuery,
      this.conversation?.id // Rechercher uniquement dans la conversation actuelle
    ).subscribe({
      next: (results) => {
        this.searchResults = results;
        this.isSearching = false;

        this.logger.info(
          'MessageChat',
          `Found ${results.length} search results`
        );

        if (results.length === 0) {
          this.toastService.showInfo('Aucun message trouvé');
        }
      },
      error: (error) => {
        this.isSearching = false;
        this.logger.error('MessageChat', 'Error searching messages:', error);
        this.toastService.showError('Erreur lors de la recherche');
      },
    });

    this.subscriptions.add(searchSub);
  }

  /**
   * Efface la recherche et revient aux messages normaux
   */
  clearSearch(): void {
    this.searchQuery = '';
    this.searchResults = [];
    this.searchMode = false;
    this.isSearching = false;

    this.logger.debug('MessageChat', 'Search cleared');
  }

  /**
   * Gère la saisie dans la barre de recherche
   * @param event Événement de saisie
   */
  onSearchInput(event: any): void {
    const query = event.target.value;

    // Recherche en temps réel avec un délai
    clearTimeout(this.searchTimeout);
    this.searchTimeout = setTimeout(() => {
      this.searchMessages(query);
    }, 300); // Délai de 300ms pour éviter trop de requêtes
  }

  /**
   * Gère la touche Entrée dans la barre de recherche
   * @param event Événement clavier
   */
  onSearchKeyPress(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      event.preventDefault();
      clearTimeout(this.searchTimeout);
      this.searchMessages(this.searchQuery);
    } else if (event.key === 'Escape') {
      event.preventDefault();
      this.toggleSearchBar();
    }
  }

  /**
   * Navigue vers un message spécifique dans la conversation
   * @param messageId ID du message à afficher
   */
  navigateToMessage(messageId: string): void {
    // Fermer la recherche
    this.clearSearch();
    this.showSearchBar = false;

    // Trouver le message dans la liste actuelle
    const messageIndex = this.messages.findIndex((msg) => msg.id === messageId);

    if (messageIndex !== -1) {
      // Le message est déjà chargé, faire défiler vers lui
      this.scrollToMessage(messageId);
    } else {
      // Le message n'est pas chargé, il faut charger plus de messages
      this.loadMessagesUntilFound(messageId);
    }

    this.logger.debug('MessageChat', `Navigating to message: ${messageId}`);
  }

  /**
   * Fait défiler vers un message spécifique
   * @param messageId ID du message
   */
  private scrollToMessage(messageId: string): void {
    setTimeout(() => {
      const messageElement = document.querySelector(
        `[data-message-id="${messageId}"]`
      );
      if (messageElement) {
        messageElement.scrollIntoView({
          behavior: 'smooth',
          block: 'center',
        });

        // Mettre en surbrillance temporairement le message
        messageElement.classList.add('highlight-message');
        setTimeout(() => {
          messageElement.classList.remove('highlight-message');
        }, 2000);
      }
    }, 100);
  }

  /**
   * Charge les messages jusqu'à trouver le message recherché
   * @param messageId ID du message à trouver
   */
  private loadMessagesUntilFound(messageId: string): void {
    if (!this.conversation?.id || !this.hasMoreMessages) {
      this.toastService.showWarning(
        'Message non trouvé dans cette conversation'
      );
      return;
    }

    // Charger plus de messages
    this.loadMoreMessages();

    // Vérifier après le chargement
    setTimeout(() => {
      const messageIndex = this.messages.findIndex(
        (msg) => msg.id === messageId
      );
      if (messageIndex !== -1) {
        this.scrollToMessage(messageId);
      } else if (this.hasMoreMessages) {
        // Continuer à charger si le message n'est toujours pas trouvé
        this.loadMessagesUntilFound(messageId);
      } else {
        this.toastService.showWarning(
          'Message non trouvé dans cette conversation'
        );
      }
    }, 500);
  }

  /**
   * Surligne les termes de recherche dans le contenu du message
   * @param content Contenu du message
   * @param query Terme de recherche
   * @returns Contenu avec les termes surlignés
   */
  highlightSearchTerms(content: string, query: string): string {
    if (!content || !query) return content;

    const regex = new RegExp(`(${query})`, 'gi');
    return content.replace(regex, '<mark class="search-highlight">$1</mark>');
  }

  // Variable pour le timeout de recherche
  private searchTimeout: any;

  // --------------------------------------------------------------------------
  // Section: Transfert de Messages
  // --------------------------------------------------------------------------

  /**
   * Ouvre le modal de transfert pour un message
   * @param message Message à transférer
   */
  openForwardModal(message: Message): void {
    if (!message.id) {
      this.toastService.showError('Impossible de transférer ce message');
      return;
    }

    this.forwardingMessageId = message.id;
    this.forwardingMessage = message;
    this.selectedConversations = [];
    this.showForwardModal = true;

    // Charger les conversations disponibles
    this.loadAvailableConversations();

    // Fermer les options du message
    if (message.id) {
      this.showMessageOptions[message.id] = false;
    }

    this.logger.debug(
      'MessageChat',
      `Opening forward modal for message: ${message.id}`
    );
  }

  /**
   * Ferme le modal de transfert
   */
  closeForwardModal(): void {
    this.showForwardModal = false;
    this.forwardingMessageId = null;
    this.forwardingMessage = null;
    this.selectedConversations = [];
    this.availableConversations = [];

    this.logger.debug('MessageChat', 'Forward modal closed');
  }

  /**
   * Charge les conversations disponibles pour le transfert
   */
  loadAvailableConversations(): void {
    this.isLoadingConversations = true;

    const conversationsSub = this.MessageService.getConversations().subscribe({
      next: (conversations) => {
        // Filtrer la conversation actuelle
        this.availableConversations = conversations.filter(
          (conv) => conv.id !== this.conversation?.id
        );

        this.isLoadingConversations = false;

        this.logger.info(
          'MessageChat',
          `Loaded ${this.availableConversations.length} available conversations for forwarding`
        );
      },
      error: (error) => {
        this.isLoadingConversations = false;
        this.logger.error(
          'MessageChat',
          'Error loading conversations for forwarding:',
          error
        );
        this.toastService.showError(
          'Erreur lors du chargement des conversations'
        );
      },
    });

    this.subscriptions.add(conversationsSub);
  }

  /**
   * Bascule la sélection d'une conversation pour le transfert
   * @param conversationId ID de la conversation
   */
  toggleConversationSelection(conversationId: string): void {
    const index = this.selectedConversations.indexOf(conversationId);

    if (index > -1) {
      // Désélectionner
      this.selectedConversations.splice(index, 1);
    } else {
      // Sélectionner
      this.selectedConversations.push(conversationId);
    }

    this.logger.debug(
      'MessageChat',
      `Toggled conversation selection: ${conversationId}, selected: ${this.selectedConversations.length}`
    );
  }

  /**
   * Vérifie si une conversation est sélectionnée
   * @param conversationId ID de la conversation
   * @returns true si la conversation est sélectionnée
   */
  isConversationSelected(conversationId: string): boolean {
    return this.selectedConversations.includes(conversationId);
  }

  /**
   * Transfère le message vers les conversations sélectionnées
   */
  forwardMessage(): void {
    if (!this.forwardingMessageId || this.selectedConversations.length === 0) {
      this.toastService.showWarning(
        'Veuillez sélectionner au moins une conversation'
      );
      return;
    }

    this.isForwarding = true;

    this.logger.debug(
      'MessageChat',
      `Forwarding message ${this.forwardingMessageId} to ${this.selectedConversations.length} conversations`
    );

    const forwardSub = this.MessageService.forwardMessage(
      this.forwardingMessageId,
      this.selectedConversations
    ).subscribe({
      next: (forwardedMessages) => {
        this.isForwarding = false;

        this.logger.info(
          'MessageChat',
          `Message forwarded successfully to ${forwardedMessages.length} conversations`
        );

        // Fermer le modal
        this.closeForwardModal();

        // Afficher un message de succès
        const conversationCount = this.selectedConversations.length;
        const successMessage =
          conversationCount === 1
            ? 'Message transféré avec succès'
            : `Message transféré vers ${conversationCount} conversations`;

        this.toastService.showSuccess(successMessage);
      },
      error: (error) => {
        this.isForwarding = false;
        this.logger.error('MessageChat', 'Error forwarding message:', error);
        this.toastService.showError('Erreur lors du transfert du message');
      },
    });

    this.subscriptions.add(forwardSub);
  }

  /**
   * Obtient le nom d'affichage d'une conversation
   * @param conversation Conversation
   * @returns Nom d'affichage de la conversation
   */
  getConversationDisplayName(conversation: Conversation): string {
    if (conversation.isGroup && conversation.groupName) {
      return conversation.groupName;
    }

    // Pour les conversations privées, afficher le nom de l'autre participant
    if (conversation.participants && conversation.participants.length > 0) {
      const otherParticipant = conversation.participants.find(
        (p) => (p.id || p._id) !== this.currentUserId
      );

      if (otherParticipant) {
        return (
          otherParticipant.username ||
          otherParticipant.fullName ||
          'Utilisateur inconnu'
        );
      }
    }

    return 'Conversation sans nom';
  }

  /**
   * Obtient l'image d'affichage d'une conversation
   * @param conversation Conversation
   * @returns URL de l'image de la conversation
   */
  getConversationDisplayImage(conversation: Conversation): string {
    if (conversation.isGroup && conversation.groupPhoto) {
      return conversation.groupPhoto;
    }

    // Pour les conversations privées, afficher l'image de l'autre participant
    if (conversation.participants && conversation.participants.length > 0) {
      const otherParticipant = conversation.participants.find(
        (p) => (p.id || p._id) !== this.currentUserId
      );

      if (otherParticipant && otherParticipant.image) {
        return otherParticipant.image;
      }
    }

    return 'assets/images/default-avatar.png';
  }

  /**
   * Sélectionne toutes les conversations disponibles
   */
  selectAllConversations(): void {
    this.selectedConversations = this.availableConversations
      .map((conv) => conv.id)
      .filter((id): id is string => id !== undefined);
    this.logger.debug(
      'MessageChat',
      `Selected all ${this.selectedConversations.length} conversations`
    );
  }

  /**
   * Désélectionne toutes les conversations
   */
  deselectAllConversations(): void {
    this.selectedConversations = [];
    this.logger.debug('MessageChat', 'Deselected all conversations');
  }

  /**
   * Vérifie si toutes les conversations sont sélectionnées
   * @returns true si toutes les conversations sont sélectionnées
   */
  areAllConversationsSelected(): boolean {
    return (
      this.availableConversations.length > 0 &&
      this.selectedConversations.length === this.availableConversations.length
    );
  }

  // --------------------------------------------------------------------------
  // Section: Épinglage de Messages
  // --------------------------------------------------------------------------

  /**
   * Affiche la confirmation d'épinglage pour un message
   * @param messageId ID du message à épingler
   */
  showPinConfirmation(messageId: string): void {
    // Fermer toutes les autres confirmations
    Object.keys(this.showPinConfirm).forEach((id) => {
      this.showPinConfirm[id] = false;
    });

    // Fermer les options du message
    if (this.showMessageOptions[messageId]) {
      this.showMessageOptions[messageId] = false;
    }

    // Afficher la confirmation pour ce message
    this.showPinConfirm[messageId] = true;

    this.logger.debug(
      'MessageChat',
      `Showing pin confirmation for message: ${messageId}`
    );
  }

  /**
   * Annule la confirmation d'épinglage
   * @param messageId ID du message
   */
  cancelPinConfirmation(messageId: string): void {
    this.showPinConfirm[messageId] = false;
    this.logger.debug(
      'MessageChat',
      `Cancelled pin confirmation for message: ${messageId}`
    );
  }

  /**
   * Épingle ou désépingle un message
   * @param message Message à épingler/désépingler
   */
  togglePinMessage(message: Message): void {
    if (!message.id || !this.conversation?.id) {
      this.toastService.showError("Impossible d'épingler ce message");
      return;
    }

    const messageId = message.id;
    const conversationId = this.conversation.id;
    const isPinned = this.isMessagePinned(message);

    // Fermer la confirmation
    this.showPinConfirm[messageId] = false;

    // Marquer comme en cours d'épinglage
    this.isPinning[messageId] = true;

    this.logger.debug(
      'MessageChat',
      `${isPinned ? 'Unpinning' : 'Pinning'} message: ${messageId}`
    );

    const pinSub = this.MessageService.pinMessage(
      messageId,
      conversationId
    ).subscribe({
      next: (updatedMessage) => {
        this.isPinning[messageId] = false;

        this.logger.info(
          'MessageChat',
          `Message ${
            isPinned ? 'unpinned' : 'pinned'
          } successfully: ${messageId}`
        );

        // Mettre à jour le message dans la liste locale
        this.messages = this.messages.map((msg) =>
          msg.id === messageId
            ? {
                ...msg,
                pinned: !isPinned,
                pinnedAt: !isPinned ? new Date() : undefined,
                pinnedBy: !isPinned
                  ? {
                      id: this.currentUserId || undefined,
                      username: this.currentUsername,
                    }
                  : undefined,
              }
            : msg
        );

        // Mettre à jour la liste des messages épinglés
        this.updatePinnedMessagesList();

        // Afficher un message de succès
        const successMessage = isPinned
          ? 'Message désépinglé avec succès'
          : 'Message épinglé avec succès';

        this.toastService.showSuccess(successMessage);
      },
      error: (error) => {
        this.isPinning[messageId] = false;
        this.logger.error('MessageChat', 'Error toggling pin message:', error);

        const errorMessage = isPinned
          ? 'Erreur lors du désépinglage du message'
          : "Erreur lors de l'épinglage du message";

        this.toastService.showError(errorMessage);
      },
    });

    this.subscriptions.add(pinSub);
  }

  /**
   * Vérifie si un message est épinglé
   * @param message Message à vérifier
   * @returns true si le message est épinglé
   */
  isMessagePinned(message: Message): boolean {
    return !!message.pinned;
  }

  /**
   * Met à jour la liste des messages épinglés
   */
  updatePinnedMessagesList(): void {
    this.pinnedMessages = this.messages.filter((msg) =>
      this.isMessagePinned(msg)
    );
    this.logger.debug(
      'MessageChat',
      `Updated pinned messages list: ${this.pinnedMessages.length} messages`
    );
  }

  /**
   * Bascule l'affichage des messages épinglés
   */
  togglePinnedMessages(): void {
    this.showPinnedMessages = !this.showPinnedMessages;

    if (this.showPinnedMessages) {
      this.updatePinnedMessagesList();
    }

    this.logger.debug(
      'MessageChat',
      `Toggled pinned messages display: ${this.showPinnedMessages}`
    );
  }

  /**
   * Fait défiler vers un message épinglé
   * @param messageId ID du message vers lequel faire défiler
   */
  scrollToPinnedMessage(messageId: string): void {
    // Fermer la vue des messages épinglés
    this.showPinnedMessages = false;

    // Attendre un court délai pour que la vue se mette à jour
    setTimeout(() => {
      const messageElement = document.getElementById(`message-${messageId}`);
      if (messageElement) {
        messageElement.scrollIntoView({
          behavior: 'smooth',
          block: 'center',
        });

        // Ajouter un effet de surbrillance temporaire
        messageElement.classList.add('highlight-message');
        setTimeout(() => {
          messageElement.classList.remove('highlight-message');
        }, 2000);

        this.logger.debug(
          'MessageChat',
          `Scrolled to pinned message: ${messageId}`
        );
      } else {
        this.logger.warn(
          'MessageChat',
          `Message element not found: ${messageId}`
        );
        this.toastService.showWarning(
          'Message non trouvé dans la conversation actuelle'
        );
      }
    }, 100);
  }

  /**
   * Obtient le nombre de messages épinglés
   * @returns Nombre de messages épinglés
   */
  getPinnedMessagesCount(): number {
    return this.pinnedMessages.length;
  }

  /**
   * Obtient le texte d'affichage pour l'épinglage
   * @param message Message
   * @returns Texte à afficher
   */
  getPinDisplayText(message: Message): string {
    return this.isMessagePinned(message) ? 'Désépingler' : 'Épingler';
  }

  /**
   * Obtient l'icône pour l'épinglage
   * @param message Message
   * @returns Classe d'icône
   */
  getPinIcon(message: Message): string {
    return this.isMessagePinned(message)
      ? 'fas fa-thumbtack'
      : 'far fa-thumbtack';
  }

  // --------------------------------------------------------------------------
  // Section: Gestion des Groupes
  // --------------------------------------------------------------------------

  /**
   * Ouvre le modal de création de groupe
   */
  openCreateGroupModal(): void {
    this.groupModalMode = 'create';
    this.currentGroup = null;
    this.groupForm.reset();
    this.selectedParticipants = [];
    this.groupPhotoFile = null;
    this.groupPhotoPreview = null;
    this.showGroupModal = true;

    // Charger les utilisateurs disponibles
    this.loadAvailableUsers();

    this.logger.debug('MessageChat', 'Opened create group modal');
  }

  /**
   * Ouvre le modal d'édition de groupe
   * @param group Groupe à éditer
   */
  openEditGroupModal(group: any): void {
    this.groupModalMode = 'edit';
    this.currentGroup = group;
    this.groupForm.patchValue({
      name: group.name,
      description: group.description || '',
    });
    this.selectedParticipants = group.participants?.map((p: any) => p.id) || [];
    this.groupPhotoFile = null;
    this.groupPhotoPreview = group.photo || null;
    this.showGroupModal = true;

    // Charger les utilisateurs disponibles
    this.loadAvailableUsers();

    this.logger.debug(
      'MessageChat',
      `Opened edit group modal for: ${group.id}`
    );
  }

  /**
   * Ferme le modal de groupe
   */
  closeGroupModal(): void {
    this.showGroupModal = false;
    this.groupModalMode = 'create';
    this.currentGroup = null;
    this.groupForm.reset();
    this.selectedParticipants = [];
    this.availableUsers = [];
    this.groupPhotoFile = null;
    this.groupPhotoPreview = null;
    this.showDeleteGroupConfirm = false;
    this.showLeaveGroupConfirm = false;

    this.logger.debug('MessageChat', 'Closed group modal');
  }

  /**
   * Charge les utilisateurs disponibles pour les groupes
   */
  loadAvailableUsers(): void {
    this.isLoadingUsers = true;

    const usersSub = this.MessageService.getAllUsers().subscribe({
      next: (response: any) => {
        // Filtrer l'utilisateur actuel
        // Gérer différents formats de réponse
        let users: User[] = [];
        if (Array.isArray(response)) {
          users = response;
        } else if (response && response.users) {
          users = response.users;
        } else if (response) {
          users = [response];
        }

        this.availableUsers = users.filter(
          (user: User) => user.id !== this.currentUserId
        );

        this.isLoadingUsers = false;

        this.logger.info(
          'MessageChat',
          `Loaded ${this.availableUsers.length} available users for groups`
        );
      },
      error: (error) => {
        this.isLoadingUsers = false;
        this.logger.error(
          'MessageChat',
          'Error loading users for groups:',
          error
        );
        this.toastService.showError(
          'Erreur lors du chargement des utilisateurs'
        );
      },
    });

    this.subscriptions.add(usersSub);
  }

  /**
   * Bascule la sélection d'un participant
   * @param userId ID de l'utilisateur
   */
  toggleParticipantSelection(userId: string): void {
    const index = this.selectedParticipants.indexOf(userId);

    if (index > -1) {
      // Désélectionner
      this.selectedParticipants.splice(index, 1);
    } else {
      // Sélectionner
      this.selectedParticipants.push(userId);
    }

    this.logger.debug(
      'MessageChat',
      `Toggled participant selection: ${userId}, selected: ${this.selectedParticipants.length}`
    );
  }

  /**
   * Vérifie si un utilisateur est sélectionné
   * @param userId ID de l'utilisateur
   * @returns true si l'utilisateur est sélectionné
   */
  isParticipantSelected(userId: string): boolean {
    return this.selectedParticipants.includes(userId);
  }

  /**
   * Gère la sélection de photo de groupe
   * @param event Événement de sélection de fichier
   */
  onGroupPhotoSelected(event: any): void {
    const file = event.target.files[0];
    if (!file) return;

    // Valider le type de fichier
    if (!file.type.startsWith('image/')) {
      this.toastService.showError('Veuillez sélectionner une image');
      return;
    }

    // Valider la taille (max 2MB)
    if (file.size > 2 * 1024 * 1024) {
      this.toastService.showError(
        "La taille de l'image ne doit pas dépasser 2MB"
      );
      return;
    }

    this.groupPhotoFile = file;

    // Créer un aperçu
    const reader = new FileReader();
    reader.onload = () => {
      this.groupPhotoPreview = reader.result as string;
    };
    reader.readAsDataURL(file);

    this.logger.debug('MessageChat', 'Group photo selected');
  }

  /**
   * Supprime la photo de groupe sélectionnée
   */
  removeGroupPhoto(): void {
    this.groupPhotoFile = null;
    this.groupPhotoPreview = null;

    this.logger.debug('MessageChat', 'Group photo removed');
  }

  /**
   * Crée un nouveau groupe
   */
  createGroup(): void {
    if (this.groupForm.invalid || this.selectedParticipants.length === 0) {
      this.toastService.showWarning(
        'Veuillez remplir tous les champs requis et sélectionner au moins un participant'
      );
      return;
    }

    this.isCreatingGroup = true;

    const formData = this.groupForm.value;

    this.logger.debug(
      'MessageChat',
      `Creating group: ${formData.name} with ${this.selectedParticipants.length} participants`
    );

    const createSub = this.MessageService.createGroup(
      formData.name,
      this.selectedParticipants,
      this.groupPhotoFile || undefined,
      formData.description || undefined
    ).subscribe({
      next: (group) => {
        this.isCreatingGroup = false;

        this.logger.info(
          'MessageChat',
          `Group created successfully: ${group.id}`
        );

        // Fermer le modal
        this.closeGroupModal();

        // Afficher un message de succès
        this.toastService.showSuccess('Groupe créé avec succès');
      },
      error: (error) => {
        this.isCreatingGroup = false;
        this.logger.error('MessageChat', 'Error creating group:', error);
        this.toastService.showError('Erreur lors de la création du groupe');
      },
    });

    this.subscriptions.add(createSub);
  }

  /**
   * Met à jour un groupe existant
   */
  updateGroup(): void {
    if (!this.currentGroup || this.groupForm.invalid) {
      this.toastService.showWarning('Veuillez remplir tous les champs requis');
      return;
    }

    this.isUpdatingGroup = true;

    const formData = this.groupForm.value;
    const updateData: any = {
      name: formData.name,
      description: formData.description,
    };

    if (this.groupPhotoFile) {
      updateData.photo = this.groupPhotoFile;
    }

    this.logger.debug('MessageChat', `Updating group: ${this.currentGroup.id}`);

    const updateSub = this.MessageService.updateGroup(
      this.currentGroup.id,
      updateData
    ).subscribe({
      next: (group) => {
        this.isUpdatingGroup = false;

        this.logger.info(
          'MessageChat',
          `Group updated successfully: ${group.id}`
        );

        // Mettre à jour les données locales si c'est la conversation actuelle
        if (this.conversation?.id === group.id) {
          this.conversation = { ...this.conversation, ...group };
        }

        // Fermer le modal
        this.closeGroupModal();

        // Afficher un message de succès
        this.toastService.showSuccess('Groupe mis à jour avec succès');
      },
      error: (error) => {
        this.isUpdatingGroup = false;
        this.logger.error('MessageChat', 'Error updating group:', error);
        this.toastService.showError('Erreur lors de la mise à jour du groupe');
      },
    });

    this.subscriptions.add(updateSub);
  }

  /**
   * Nettoyage lors de la destruction du composant
   */
  ngOnDestroy(): void {
    // Arrêter l'indicateur de frappe lorsque l'utilisateur quitte la conversation
    this.stopTypingIndicator();

    // Nettoyer le timeout de recherche
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }

    // Nettoyer le timeout de frappe
    if (this.typingTimeout) {
      clearTimeout(this.typingTimeout);
    }

    // Nettoyer les timers de statut utilisateur
    if (this.statusUpdateInterval) {
      clearInterval(this.statusUpdateInterval);
    }

    if (this.autoAwayTimeout) {
      clearTimeout(this.autoAwayTimeout);
    }

    // Mettre l'utilisateur hors ligne avant de quitter
    if (this.currentUserId) {
      this.MessageService.setUserOffline(this.currentUserId).subscribe({
        next: () => {
          this.logger.debug(
            'MessageChat',
            'User set offline on component destroy'
          );
        },
        error: (error) => {
          this.logger.error(
            'MessageChat',
            'Error setting user offline:',
            error
          );
        },
      });
    }

    // Supprimer le gestionnaire de clic global
    document.removeEventListener('click', this.onDocumentClick.bind(this));

    // Nettoyer les événements d'activité
    const events = [
      'mousedown',
      'mousemove',
      'keypress',
      'scroll',
      'touchstart',
      'click',
    ];
    events.forEach((event) => {
      document.removeEventListener(event, this.onUserActivity.bind(this), true);
    });

    // Se désabonner de tous les observables
    this.subscriptions.unsubscribe();

    this.logger.debug('MessageChat', 'Component destroyed and cleaned up');
  }
}
