{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@auth0/angular-jwt\";\nexport class PlanningService {\n  constructor(http, jwtHelper) {\n    this.http = http;\n    this.jwtHelper = jwtHelper;\n  }\n  getUserHeaders() {\n    const token = localStorage.getItem('token');\n    if (!token || this.jwtHelper.isTokenExpired(token)) {\n      throw new Error('Token invalide ou expiré');\n    }\n    return new HttpHeaders({\n      Authorization: `Bearer ${token || ''}`,\n      'Content-Type': 'application/json'\n    });\n  }\n  getAllPlannings() {\n    return this.http.get(`${environment.urlBackend}plannings/getall`, {\n      headers: this.getUserHeaders()\n    });\n  }\n  getPlanningById(id) {\n    return this.http.get(`${environment.urlBackend}plannings/getone/${id}`, {\n      headers: this.getUserHeaders()\n    });\n  }\n  createPlanning(planning) {\n    return this.http.post(`${environment.urlBackend}plannings/add`, planning, {\n      headers: this.getUserHeaders()\n    });\n  }\n  updatePlanning(id, planning) {\n    return this.http.put(`${environment.urlBackend}plannings/update/${id}`, planning, {\n      headers: this.getUserHeaders()\n    });\n  }\n  deletePlanning(id) {\n    return this.http.delete(`${environment.urlBackend}plannings/delete/${id}`, {\n      headers: this.getUserHeaders()\n    });\n  }\n  getPlanningsByUser(userId) {\n    return this.http.get(`${environment.urlBackend}plannings/user/${userId}`, {\n      headers: this.getUserHeaders()\n    });\n  }\n  getPlanningsWithDetails() {\n    return this.http.get(`${environment.urlBackend}plannings/with-details`, {\n      headers: this.getUserHeaders()\n    });\n  }\n  getPlanningWithReunions(id) {\n    return this.http.get(`${environment.urlBackend}plannings/with-reunions/${id}`, {\n      headers: this.getUserHeaders()\n    });\n  }\n  static {\n    this.ɵfac = function PlanningService_Factory(t) {\n      return new (t || PlanningService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.JwtHelperService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: PlanningService,\n      factory: PlanningService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpHeaders", "environment", "PlanningService", "constructor", "http", "jwtHelper", "getUserHeaders", "token", "localStorage", "getItem", "isTokenExpired", "Error", "Authorization", "getAllPlannings", "get", "urlBackend", "headers", "getPlanningById", "id", "createPlanning", "planning", "post", "updatePlanning", "put", "deletePlanning", "delete", "getPlanningsByUser", "userId", "getPlanningsWithDetails", "getPlanningWithReunions", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "JwtHelperService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\services\\planning.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpHeaders, HttpClient } from '@angular/common/http';\nimport { Observable } from 'rxjs';\nimport { environment } from 'src/environments/environment';\nimport { Planning } from '../models/planning.model';\nimport { JwtHelperService } from '@auth0/angular-jwt';\n\n@Injectable({\n  providedIn: 'root',\n})\nexport class PlanningService {\n  constructor(private http: HttpClient, private jwtHelper: JwtHelperService) {}\n  private getUserHeaders(): HttpHeaders {\n    const token = localStorage.getItem('token');\n    if (!token || this.jwtHelper.isTokenExpired(token)) {\n      throw new Error('Token invalide ou expiré');\n    }\n    return new HttpHeaders({\n      Authorization: `Bearer ${token || ''}`,\n      'Content-Type': 'application/json',\n    });\n  }\n\n  getAllPlannings(): Observable<Planning[]> {\n    return this.http.get<Planning[]>(\n      `${environment.urlBackend}plannings/getall`,\n      { headers: this.getUserHeaders() }\n    );\n  }\n\n  getPlanningById(id: string): Observable<Planning> {\n    return this.http.get<Planning>(\n      `${environment.urlBackend}plannings/getone/${id}`,\n      { headers: this.getUserHeaders() }\n    );\n  }\n\n  createPlanning(planning: Planning): Observable<Planning> {\n    return this.http.post<Planning>(\n      `${environment.urlBackend}plannings/add`,\n      planning,\n      { headers: this.getUserHeaders() }\n    );\n  }\n\n  updatePlanning(id: string, planning: Planning): Observable<Planning> {\n    return this.http.put<Planning>(\n      `${environment.urlBackend}plannings/update/${id}`,\n      planning,\n      { headers: this.getUserHeaders() }\n    );\n  }\n\n  deletePlanning(id: string): Observable<void> {\n    return this.http.delete<void>(\n      `${environment.urlBackend}plannings/delete/${id}`,\n      { headers: this.getUserHeaders() }\n    );\n  }\n\n  getPlanningsByUser(userId: string): Observable<Planning[]> {\n    return this.http.get<Planning[]>(\n      `${environment.urlBackend}plannings/user/${userId}`,\n      {\n        headers: this.getUserHeaders(),\n      }\n    );\n  }\n  getPlanningsWithDetails(): Observable<Planning[]> {\n    return this.http.get<Planning[]>(\n      `${environment.urlBackend}plannings/with-details`,\n      { headers: this.getUserHeaders() }\n    );\n  }\n\n  getPlanningWithReunions(id: string): Observable<Planning> {\n    return this.http.get<Planning>(\n      `${environment.urlBackend}plannings/with-reunions/${id}`,\n      { headers: this.getUserHeaders() }\n    );\n  }\n}\n"], "mappings": "AACA,SAASA,WAAW,QAAoB,sBAAsB;AAE9D,SAASC,WAAW,QAAQ,8BAA8B;;;;AAO1D,OAAM,MAAOC,eAAe;EAC1BC,YAAoBC,IAAgB,EAAUC,SAA2B;IAArD,KAAAD,IAAI,GAAJA,IAAI;IAAsB,KAAAC,SAAS,GAATA,SAAS;EAAqB;EACpEC,cAAcA,CAAA;IACpB,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAACF,KAAK,IAAI,IAAI,CAACF,SAAS,CAACK,cAAc,CAACH,KAAK,CAAC,EAAE;MAClD,MAAM,IAAII,KAAK,CAAC,0BAA0B,CAAC;;IAE7C,OAAO,IAAIX,WAAW,CAAC;MACrBY,aAAa,EAAE,UAAUL,KAAK,IAAI,EAAE,EAAE;MACtC,cAAc,EAAE;KACjB,CAAC;EACJ;EAEAM,eAAeA,CAAA;IACb,OAAO,IAAI,CAACT,IAAI,CAACU,GAAG,CAClB,GAAGb,WAAW,CAACc,UAAU,kBAAkB,EAC3C;MAAEC,OAAO,EAAE,IAAI,CAACV,cAAc;IAAE,CAAE,CACnC;EACH;EAEAW,eAAeA,CAACC,EAAU;IACxB,OAAO,IAAI,CAACd,IAAI,CAACU,GAAG,CAClB,GAAGb,WAAW,CAACc,UAAU,oBAAoBG,EAAE,EAAE,EACjD;MAAEF,OAAO,EAAE,IAAI,CAACV,cAAc;IAAE,CAAE,CACnC;EACH;EAEAa,cAAcA,CAACC,QAAkB;IAC/B,OAAO,IAAI,CAAChB,IAAI,CAACiB,IAAI,CACnB,GAAGpB,WAAW,CAACc,UAAU,eAAe,EACxCK,QAAQ,EACR;MAAEJ,OAAO,EAAE,IAAI,CAACV,cAAc;IAAE,CAAE,CACnC;EACH;EAEAgB,cAAcA,CAACJ,EAAU,EAAEE,QAAkB;IAC3C,OAAO,IAAI,CAAChB,IAAI,CAACmB,GAAG,CAClB,GAAGtB,WAAW,CAACc,UAAU,oBAAoBG,EAAE,EAAE,EACjDE,QAAQ,EACR;MAAEJ,OAAO,EAAE,IAAI,CAACV,cAAc;IAAE,CAAE,CACnC;EACH;EAEAkB,cAAcA,CAACN,EAAU;IACvB,OAAO,IAAI,CAACd,IAAI,CAACqB,MAAM,CACrB,GAAGxB,WAAW,CAACc,UAAU,oBAAoBG,EAAE,EAAE,EACjD;MAAEF,OAAO,EAAE,IAAI,CAACV,cAAc;IAAE,CAAE,CACnC;EACH;EAEAoB,kBAAkBA,CAACC,MAAc;IAC/B,OAAO,IAAI,CAACvB,IAAI,CAACU,GAAG,CAClB,GAAGb,WAAW,CAACc,UAAU,kBAAkBY,MAAM,EAAE,EACnD;MACEX,OAAO,EAAE,IAAI,CAACV,cAAc;KAC7B,CACF;EACH;EACAsB,uBAAuBA,CAAA;IACrB,OAAO,IAAI,CAACxB,IAAI,CAACU,GAAG,CAClB,GAAGb,WAAW,CAACc,UAAU,wBAAwB,EACjD;MAAEC,OAAO,EAAE,IAAI,CAACV,cAAc;IAAE,CAAE,CACnC;EACH;EAEAuB,uBAAuBA,CAACX,EAAU;IAChC,OAAO,IAAI,CAACd,IAAI,CAACU,GAAG,CAClB,GAAGb,WAAW,CAACc,UAAU,2BAA2BG,EAAE,EAAE,EACxD;MAAEF,OAAO,EAAE,IAAI,CAACV,cAAc;IAAE,CAAE,CACnC;EACH;;;uBAtEWJ,eAAe,EAAA4B,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,gBAAA;IAAA;EAAA;;;aAAfjC,eAAe;MAAAkC,OAAA,EAAflC,eAAe,CAAAmC,IAAA;MAAAC,UAAA,EAFd;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}